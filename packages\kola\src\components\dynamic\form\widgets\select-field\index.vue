<template>
  <a-form-item
    :tooltip="tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="field.rules"
    :hide-label="field.hideLabel"
    :hide-asterisk="field.hideAsterisk"
    :disabled="disabled"
    :style="field.style"
  >
    <!-- radioGroup -->
    <radio-group
      v-model="value"
      :source-data="sourceData"
      :format="field.format"
      :label-key="labelKey"
      :value-key="valueKey"
      v-if="field.format === 'radio' || field.format === 'buttonRadio'"
    />
    <radio-group-card
      v-model="value"
      :sub-key="subKey"
      :format="field.format"
      :source-data="sourceData"
      :value-key="valueKey"
      :label-key="labelKey"
      :card="field.card"
      v-if="field.format === 'cardRadio'"
    />
    <check-group
      v-model="value"
      :source-data="sourceData"
      :format="field.format"
      :label-key="labelKey"
      :value-key="valueKey"
      v-if="field.format === 'checkbox'"
      :show-all="showAll"
      :direction="direction"
      :bordered="field.select?.bordered"
    />
    <check-group-button
      v-model="value"
      :source-data="sourceData"
      :format="field.format"
      :label-key="labelKey"
      :value-key="valueKey"
      v-if="field.format === 'buttonCheckbox'"
      :show-all="showAll"
      :direction="direction"
      :max="field.select?.max"
    />
    <kola-select
      ref="kolaSelectRef"
      v-model="value"
      :source-data="sourceData"
      :format="field.format"
      :label-key="labelKey"
      :value-key="valueKey"
      :show-all="showAll"
      :show-all-text="props?.field?.select?.showAllText"
      v-if="field.format === 'singleSelect' || field.format === 'multipleSelect'"
      :field="field"
      @input-value-change="(inputVal) => (searchText = inputVal)"
    />
    <template #extra v-if="extra">
      <component :is="extra" />
    </template>
    <template #label v-if="field.labelTip">
      {{ field.label }}
      <a-tooltip :content="field.labelTip" position="bottom">
        <icon-exclamation-circle-fill />
      </a-tooltip>
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { Ref, inject, ref, watchEffect, computed, reactive, provide } from 'vue';
  import { isFunction, isNil } from 'lodash';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { FormData, SelectField, Option } from '../../../types/form';
  import useCheckAll from './hooks/check-all';
  import RadioGroup from './radio-group.vue';
  import RadioGroupCard from './radio-group-card.vue';
  import CheckGroup from './check-group.vue';
  import CheckGroupButton from './check-group-button.vue';
  import KolaSelect from './select/index.vue';
  import { selectInjectionKey } from './context';

  const searchText = ref<string>('');
  const loading = ref(false);

  const props = defineProps<{
    field: SelectField;
    path: string;
  }>();
  const formData = inject<Ref<FormData>>('formData');
  const formRef = inject<FormInstance>('formRef');
  const value = defineModel<any>({
    default: '',
    set(val) {
      if (props.field.onChange) {
        const oldVal = formData?.value?.[props.field.name] ?? '';
        setTimeout(() => {
          props.field.onChange?.(val, formData, oldVal, props.path);
          props.field.getRef?.({ formRef });
        }, 0);
      }
      return val;
    },
  });

  const direction = computed(() => {
    return props.field.select?.direction || 'horizontal';
  });

  const sourceData = ref<Option[]>([]);
  const labelKey = computed(() => {
    return props.field.source.labelKey ?? 'label';
  });
  const valueKey = computed(() => {
    return props.field.source.valueKey ?? 'value';
  });
  const subKey = computed(() => {
    return props.field.source.subKey ?? 'subLabel';
  });
  const disabled = computed(() => {
    if (isFunction(props.field.disabled)) {
      return props.field.disabled(props.field, formData?.value as any, props.path);
    }
    if (!isNil(props.field.disabled)) {
      return props.field.disabled;
    }
    return undefined;
  });
  const tooltip = computed(() => {
    if (isFunction(props.field.tooltip)) {
      return props.field.tooltip(props.field, formData?.value as any);
    }
    return props.field.tooltip;
  });
  const extra = props.field.extra as unknown as string;
  const { showAll, indeterminate, checkedAll, changeAll, toggleIndeterminate } = useCheckAll(props);

  const kolaSelectRef = ref();

  async function getSourceData() {
    if (typeof props.field.source.data === 'function') {
      sourceData.value = await props.field.source.data(
        props.field,
        formData?.value as any,
        props.path,
        searchText.value
      );
      loading.value = false;
    } else {
      sourceData.value = props.field.source.data || [];
    }
    if (showAll.value) {
      handleChange(value.value);
    }
  }

  function handleChangeAll(flag) {
    let inputText = '';
    if (props.field.format === 'multipleSelect') {
      inputText = kolaSelectRef.value.getInputText?.() ?? '';
    }
    changeAll({
      flag,
      modelValue: value,
      sourceData,
      labelKey,
      valueKey,
      inputText,
    });
  }

  function handleChange(values) {
    if (showAll.value) {
      toggleIndeterminate(values, sourceData);
    }
  }

  function onPopupVisibleChange(visible: boolean) {
    searchText.value = '';
    if (isFunction(props.field.select?.popupVisibleChange)) {
      props.field.select?.popupVisibleChange(visible, formData?.value, value.value);
    }
  }

  function handleReverse() {
    const currentValue = value.value;
    value.value = sourceData.value
      .filter((item) => !currentValue.includes(item[valueKey.value]))
      .map((v) => v[valueKey.value]);
  }

  const updateSearchText = (newSearchText: string) => {
    loading.value = true;
    searchText.value = newSearchText;
  };

  provide(
    selectInjectionKey,
    reactive({
      checkedAll,
      indeterminate,
      handleChangeAll,
      handleChange,
      onPopupVisibleChange,
      handleReverse,
      getSourceData,
      updateSearchText,
      loading,
    })
  );

  watchEffect(() => {
    getSourceData();
  });
</script>
