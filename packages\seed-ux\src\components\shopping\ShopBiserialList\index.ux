<template>
  <div class="personal-page">
    <list
      if="{{ goodsList && goodsList.length }}"
      class="goods-list"
      onscrollbottom="onscrollbottom"
      columns="1"
    >
      <block for="(index,item) in goodsList">
        <list-item
          type="listItem"
          style="margin-top: {{ index == 0 || index == 1 ? '0px' : '15px' }};"
        >
          <div class="goods-item" onclick="goGoodsDetails(item)">
            <image
              oncomplete="onImgComplete()"
              onerror="onImgError(e,item)"
              src="{{ item.pict_url | formatImgUrl }}"
              class="goods-img"
            ></image>
            <div class="goods-bottom-wapper">
              <!-- 文本 -->
              <div class="goods-desc-box">
                <text class="goods-text ellipsis2" lines="{{ 2 }}">
                  {{ item.title }}
                </text>
                <div if="{{ item.platform == 'tb' }}" class="brand">
                  <text class="brand-text">{{
                    item.platform | formatPlatform
                  }}</text>
                </div>
              </div>
              <div class="goods-box">
                <!-- 券 -->
                <div class="goods-coupon-box">
                  <div class="goods-coupon">
                    <text class="coupon-text" style="margin-left: 10px"
                      >领</text
                    >
                    <text
                      class="coupon-text"
                      style="margin-left: 2px; font-weight: 500"
                      >{{ parseInt(item.coupon_amount, 10) }}</text
                    >
                    <text class="coupon-text unit" style="margin-left: 2px"
                      >元</text
                    >
                    <image src="../assets/images/logo.png" class="quan"></image>
                  </div>
                </div>
                <!-- 价格 -->
                <div class="goods-price-box">
                  <text class="price-flag">¥</text>
                  <text
                    class="price"
                    style="font-size:{{
                      item.final_price.length > 5 ? '24px;' : '34px;'
                    }};margin-top:-2px;"
                  >
                    {{ item.final_price }}</text
                  >
                  <text class="text">到手价</text>
                  <text class="last-price">
                    <span class="last-price-flag">¥</span>
                    <span style="margin-top: -2px">{{
                      item.initial_price
                    }}</span>
                  </text>
                </div>
              </div>
            </div>
          </div>
        </list-item>
      </block>
      <list-item type="tip" class="goods-tip">
        <text>没有更多商品了，可以去其他货架淘货吧~</text>
      </list-item>
    </list>
  </div>
</template>

<script>
  export default {
    props: {
      isShowLoading: {
        type: Number,
        default: -1,
      },
      activeTab: {
        default: 0,
        type: Number,
      },
    },

    data: {
      isDataEnd: false,
      selectTab: 'tb',
      goodsList: [],
      searchEnd: false,
      sort: 1,
      sortType: '',
      page: 1,
      sortTabList: [
        {
          key: 1,
          name: '综合',
        },
        {
          key: 2,
          name: '销量',
        },
      ],
    },

    onInit() {
      this.getGoodsList()
    },
    onImgComplete() {
      console.log('onImgComplete==图片加载成功')
    },
    onImgError(err, item) {
      console.log('onImgError==图片加载失败', err)
      item.pict_url = '../assets/images/logo.png'
    },
    clickReport(str) {
      COMMON_REPORT_UTILS.page_click_report(str)
    },
    // 格式化品牌
    formatPlatform(val) {
      if (!val) return ''
      switch (val) {
        case 'tb':
          val = '淘宝'
          break
        case 'pdd':
          val = '拼多多'
          break
        case 'jd':
          val = '京东'
          break
      }
      return val
    },
    formatImgUrl(url) {
      if (!url.includes('http') && !url.includes('logo.png')) {
        url = `http:${url}`
      } else {
        url = url
      }
      return url
    },
    sortTabSwitch(key) {
      this.goodsList = []
      this.searchEnd = false
      this.page = 1
      this.list_id = ''
      if (key) {
        this.sort = key
        this.sortType = ''
      } else {
        let sortValue = this.sortType == 3 ? 4 : 3
        this.sort = sortValue
        this.sortType = sortValue
      }
      this.getGoodsList()
    },
    getGoodsList() {
      const param = {
        platform: 'tb',
        page: this.page,
      }
      $apis.example
        .goodsList(param)
        .then((res) => {
          let goodsList = res.data
          if (this.page == 1) {
            this.goodsList = goodsList
          } else {
            this.goodsList = [...this.goodsList, ...goodsList]
          }
          console.log('goooods', this.goodsList, this.page)
          this.isDataEnd = true
          console.log('goooooods', this.goodsList)
        })
        .catch((err) => {
          this.isDataEnd = true
          this.goodsList = []
          console.log('接口请求失败：', err)
        })
    },
    goGoodsDetails(item) {
      this.$emit('pageJumpHandle', {
        pageUrl: 'subPages/Detail',
        jumpType: 'push',
        pageData: {
          platform: item.platform,
          itemId: item.item_id,
          as: 0,
        },
      })
      let reportText = `商品卡片_${this.selectTab}_${item.item_id}`
      COMMON_REPORT_UTILS.page_click_report(reportText)
    },
    onSearch() {
      this.$emit('pageJumpHandle', {
        pageUrl: 'subPages/Search',
        jumpType: 'replace',
        pageData: {},
      })
    },
    // 下拉加载
    onscrollbottom() {
      this.page = ++this.page
      this.getGoodsList()
    },
  }
</script>
<style lang="less">
  /*  文字溢出2行换行 */
  .ellipsis2 {
    text-overflow: ellipsis;
    lines: 2;
  }

  .personal-page {
    flex-direction: column;
    align-items: center;
    width: 100%;
    /* background-image: url(../assets/images/logo.png); */
    background-size: 100% 479px;
    background-repeat: no-repeat;
  }

  .margin-left-37 {
    margin-left: 37px;
  }

  .home-head {
    flex-direction: column;
    align-items: center;
    padding-top: 110px;
    width: 750px;

    .title {
      width: 164px;
      height: 77px;
      align-self: flex-start;
      margin-left: 20px;
      margin-bottom: 7px;
    }

    .search-box {
      width: 702px;
      height: 74px;
      align-items: center;
      margin-bottom: 15px;

      .icon {
        width: 100%;
        height: 100%;
        object-fit: fill;
      }

      .btn {
        width: 115px;
        height: 54px;
        line-height: 54px;
        background: linear-gradient(to right, #ff7947, #ff511b);
        border-radius: 39px;
        justify-content: center;

        text {
          font-weight: 500;
          font-size: 26px;
          color: #ffffff;
        }
      }
    }

    .tab {
      width: 750px;
      padding-left: 37px;
      padding-bottom: 30px;
      justify-content: space-around;
      background: repeating-linear-gradient(to bottom, #ffffff, #f7f7f7);

      .tab-item {
        width: 106px;
        margin-right: 80px;
        flex-direction: column;
        align-items: center;

        .tab-icon {
          width: 80px;
          height: 80px;
          object-fit: fill;
        }

        .tab-text {
          font-size: 28px;
          font-weight: 600;
          color: #333333;
        }
      }
    }
  }

  .goods-list {
    width: 100%;

    .goods-item {
      height: 235px;
      margin: 15 15px;
      background-color: #ffffff;
      border-radius: 14px;

      .goods-img {
        width: 205px;
        height: 205px;
        margin: 15px;
        border-radius: 20px;
        flex-shrink: 0;
      }

      .goods-bottom-wapper {
        flex-direction: column;
        justify-content: space-between;
        padding-top: 20px;
        padding-left: 20px;

        .goods-desc-box {
          height: 100px;
          align-items: flex-start;

          .brand {
            width: 60px;
            height: 30px;
            border: 1px solid #f92a18;
            border-radius: 4px;
            justify-content: center;
            align-items: center;
            position: absolute;
            left: 0px;
            top: 8px;

            .brand-text {
              width: 40px;
              height: 20px;
              font-size: 20px;
              font-weight: 600;
              color: #f92a18;
              line-height: 20px;
            }
          }

          .goods-text {
            line-height: 48px;
            font-size: 26px;
            font-weight: 500;
            color: #333333;
            text-indent: 64;
          }
        }

        .goods-box {
          justify-content: space-between;
          align-items: center;
          margin-bottom: 35px;

          .goods-price-box {
            align-self: flex-end;
            margin-right: 21px;

            .text {
              font-size: 20px;
              font-weight: 400;
              color: #ff2535;
              letter-spacing: -0.01px;
              margin-right: 8px;
            }

            .price-flag {
              font-size: 26px;
              font-weight: 500;
              color: #ff2535;
              letter-spacing: -0.02px;
              padding-right: 4px;
              margin-top: 5px;
            }

            .price {
              margin-right: 4px;
              font-size: 34px;
              font-weight: 600;
              color: #ff2535;
            }

            .last-price-flag {
              font-size: 20px;
              color: #cdcdcd;
              text-decoration: line-through;
              margin-top: 6px;
            }

            .last-price {
              font-size: 20px;
              font-weight: 400;
              color: #cdcdcd;
              letter-spacing: -0.02px;
              text-decoration: line-through;
              margin-top: 3px;
            }
          }

          .goods-coupon-box {
            align-items: center;
            justify-content: flex-start;
            height: 34px;

            .goods-coupon {
              justify-content: flex-start;
              align-items: center;
              height: 32px;
              background-color: #ffebee;
              border-radius: 6px;

              .coupon-text {
                height: 22px;
                font-size: 22px;
                font-weight: 500;
                color: #ff3628;
                line-height: 22px;
              }

              .unit {
                font-size: 22px;
              }

              .quan {
                width: 47px;
                height: 32px;
                object-fit: fill;
              }
            }
          }
        }

        .goods-shop {
          justify-content: space-between;

          text {
            font-size: 26px;
            font-weight: 400;
            color: #cdcdcd;
          }

          .sell-count {
            font-size: 24px;
            font-weight: 400;
            color: #cdcdcd;
          }
        }
      }

      .number {
        font-weight: 700;
      }

      .bg_560_view {
        width: 100%;
        flex-wrap: wrap;
        margin-top: 35px;
        justify-content: center;
      }

      .img_bg {
        width: 140px;
        height: 140px;
        margin: 6px 6px;
      }

      .spu_name {
        lines: 2;
        text-overflow: ellipsis;
        color: #333;
        margin-top: 20px;
        margin-left: 20px;
        margin-right: 20px;
      }

      .swiperView {
        height: 300px;
        margin: 35px 25px;
        page-animation-name: Opacity;
        page-transform-origin: 0px 0px;
        animation-timing-function: linear;
        indicator-color: #c4c4c4;
        indicator-selected-color: #fa2209;
        indicator-size: 8px;
        indicator-bottom: 10px;

        @keyframes Opacity {
          from {
            opacity: 0;
          }

          to {
            opacity: 1;
          }
        }
      }

      .no_goodsList {
        flex: 1;
        display: flex;
        width: 100%;
        height: 100%;
        justify-content: center;
        flex-direction: column;
        align-items: center;
      }
    }
  }
</style>
