const Log = require('../model/log');
const User = require('../model/user');

// 日志记录中间件
async function logMiddleware(req, res, next) {
    try {
        // 从请求中获取用户信息
        const role = await User.findOne({ id: req.user.userId });

        // 创建日志记录
        const log = new Log({
            username: role.username,
            role: role.role,
            requestApi: req.path, // 当前请求的路径
            requestParams: JSON.stringify(req.body), // 请求参数
            lastLogin: Date.now() // 当前时间
        });

        // 保存日志
        await log.save();
        // 继续执行下一个中间件或路由处理函数
        next();
    } catch (error) {
        // 如果发生错误，传递给错误处理中间件
        next(error);
    }
}

module.exports = logMiddleware;