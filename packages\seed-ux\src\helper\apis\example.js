import $ajax from '../ajax'

/**
 * @desc 在实际开发中，您可以将 baseUrl 替换为您的请求地址前缀；
 *
 * 已将 $apis 挂载在 global，您可以通过如下方式，进行调用：
 * $apis.example.getSomeApi().then().catch().finally()
 *
 * 备注：如果您不需要发起请求，删除 apis 目录，以及 app.ux 中引用即可；
 */
 const shopBaseUrl = "https://kuzapi.tianyaoguan.cn/"

 export default {
 
     loginApi(data) {
         return $ajax.post(`${shopBaseUrl}${login_api}`, data)
     },
     //获取商品列表
     goodsList(data) {
         return $ajax.post(`${shopBaseUrl}coupon/storage-coupon/list`, data)
     },
     //搜索商品
     searchGoods(data) {
         return $ajax.post(`${shopBaseUrl}coupon/coupon/search`, data)
     },
     //搜索商品详情
     goodsDetail(data) {
         return $ajax.post(`${shopBaseUrl}coupon/coupon/goods_detail`, data)
     },
     //列表商品详情
     listGoodsDetail(data) {
         return $ajax.post(`${shopBaseUrl}coupon/storage-coupon/goods_detail`, data)
     },
 }
