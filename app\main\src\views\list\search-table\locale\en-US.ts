export default {
  'menu.list.searchTable': 'Search Table',
  'searchTable.form.number': 'Set Number',
  'searchTable.form.number.placeholder': 'Please enter Set Number',
  'searchTable.form.name': 'Set Name',
  'searchTable.form.name.placeholder': 'Please enter Set Name',
  'searchTable.form.contentType': 'Content Type',
  'searchTable.form.contentType.img': 'image-text',
  'searchTable.form.contentType.horizontalVideo': 'Horizontal short video',
  'searchTable.form.contentType.verticalVideo': 'Vertical short video',
  'searchTable.form.filterType': 'Filter Type',
  'searchTable.form.filterType.artificial': 'artificial',
  'searchTable.form.filterType.rules': 'Rules',
  'searchTable.form.createdTime': 'Create Date',
  'searchTable.form.status': 'Status',
  'searchTable.form.status.online': 'Online',
  'searchTable.form.status.offline': 'Offline',
  'searchTable.form.search': 'Search',
  'searchTable.form.reset': 'Reset',
  'searchTable.form.selectDefault': 'All',
  'searchTable.operation.create': 'Create',
  'searchTable.operation.import': 'Import',
  'searchTable.operation.download': 'Download',
  // columns
  'searchTable.columns.index': '#',
  'searchTable.columns.number': 'Set Number',
  'searchTable.columns.name': 'Set Name',
  'searchTable.columns.contentType': 'Content Type',
  'searchTable.columns.filterType': 'Filter Type',
  'searchTable.columns.count': 'Count',
  'searchTable.columns.createdTime': 'CreatedTime',
  'searchTable.columns.status': 'Status',
  'searchTable.columns.operations': 'Operations',
  'searchTable.columns.operations.view': 'View',
  // size
  'searchTable.size.mini': 'mini',
  'searchTable.size.small': 'small',
  'searchTable.size.medium': 'middle',
  'searchTable.size.large': 'large',
  // actions
  'searchTable.actions.refresh': 'refresh',
  'searchTable.actions.density': 'density',
  'searchTable.actions.columnSetting': 'columnSetting',
};
