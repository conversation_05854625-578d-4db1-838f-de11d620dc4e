const express = require('express');
const router = express.Router();
const AppVersionController = require('../controller/AppVersion');

// AppVersion相关路由
router.post('/AppVersions', AppVersionController.createAppVersion); // 创建AppVersion
router.get('/AppVersions', AppVersionController.getAllAppVersions); // 获取AppVersion列表
router.get('/AppVersions/:id', AppVersionController.getAppVersionById); // 获取单个AppVersion
router.put('/AppVersions/:id', AppVersionController.updateAppVersion); // 更新AppVersion
router.delete('/AppVersions/:id', AppVersionController.deleteAppVersion); // 删除AppVersion

module.exports = router;
