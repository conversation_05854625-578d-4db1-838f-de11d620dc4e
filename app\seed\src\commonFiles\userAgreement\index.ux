<import name="common-modal" src="../../components/commonModal/index"></import>
<template>
  <div class="{{showModal ? 'wrapper-show': 'wrapper-hide'}}">
    <block if="{{showModal}}">
      <common-modal
        title="{{modalInfo.title}}"
        cancel-btn-text="{{modalInfo.cancelBtnText}}"
        confirm-btn-text="{{modalInfo.confirmBtnText}}"
        confirm-btn-style="{{modalInfo.confirmBtnStyle}}"
        confirm-text-style="{{modalInfo.confirmTextStyle}}"
        cancel-btn-style="{{modalInfo.cancelBtnStyle}}"
        cancel-text-style="{{modalInfo.cancelTextStyle}}"
        oncancel-fun="cancelFun"
        onconfirm-fun="confirmFun"
        show-rich-text="{{false}}"
      >
        <div>
          <div>
            <text
              style="
                line-height: 48px;
                width: 520px;
                font-size: 32px;
                font-weight: 400;
                color: #888888;
                text-align: left;
              "
              >为了更好的保障您的个人权益，在使用我们的产品前，请认真阅读：<a
                style="color: #42c59b; text-decoration: none"
                @click="goLink(1)"
                >《用户协议》</a
              ><a style="color: #42c59b; text-decoration: none" @click="goLink(2)">《隐私政策》</a
              >，如果您同意以上内容，请点击“同意”，开始使用我们的产品和服务！</text
            >
          </div>
        </div>
      </common-modal>
    </block>
  </div>
</template>

<script>
import router from '@system.router'
export default {
  props: {
    type: ''
  },
  data: {
    showModal: false,
    modalInfo: {
      title: '隐私政策保护指引',
      content: '',
      cancelBtnText: '拒绝',
      confirmBtnText: '同意开始',
      cancelTextStyle: 'color: #42C59B;font-size:28px',
      confirmBtnStyle:
        'background: {"values":[{"type":"linearGradient","directions":["180deg"],"values":["#42C59B","#42C59B 100%"]}]};',
      confirmTextStyle: 'color: #ffffff;font-size:28px'
    },
    links: null
  },
  onInit() {
    if (!this.checkAgreementLink()) return
    $utils.getStorage('privacyDialog').then(data => {
      if (!data) {
        this.showModal = true
      }
    })
  },

  checkAgreementLink() {
    if (!this.agreementUrl || !this.privacyUrl) {
      console.error('项目隐私政策链接未配置,请在brand-config文件中配置')
      return false
    }
    return true
  },

  // 隐私政策   不同意
  cancelFun() {
    this.$app.exit()
  },

  // 隐私政策   同意
  confirmFun() {
    this.showModal = false
    $utils.setStorage('privacyDialog', true)
  },

  // 隐私政策
  goLink(i) {
    const title = i === 1 ? '用户协议' : '隐私政策'
    const url = i === 1? this.agreementUrl : this.privacyUrl
    const webUrl = `${url}`
    router.push({
        uri: 'pages/CommonWebview',
        params: {
          title,
          webUrl,
        }
    })
  },
}
</script>
<style lang="less">
.wrapper-hide {
  width: 0px;
  height: 0px;
}
.wrapper-show {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
