<template>
  <div class="search-bar" :style="styles">
    <a-input-search
      v-model="textValue"
      :size="size"
      :placeholder="placeholder"
      :style="{ color: styles.color, fontSize: styles.fontSize }"
    />
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'middle'
  },
  placeholder: {
    type: String,
    default: ''
  },
  styles: {
    type: Object,
    default: () => ({})
  },
});

</script>

<style scoped>
.search-bar {
  width: 100%;
  padding: 8px;
}
</style> 