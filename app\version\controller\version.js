const Version = require('../model/version');

/**
 * 获取版本列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Promise<void>}
 */
async function getVersionList(req, res) {
  try {
    // 获取查询参数
    const { page = 1, pageSize = 10, name } = req.query;

    // 构建查询条件
    const query = {};
    if (name) {
      query.name = { $regex: name, $options: 'i' }; // 模糊查询
    }

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 查询数据
    const [versions, total] = await Promise.all([
      Version.find(query).skip(skip).limit(Number(pageSize)).sort({ releaseDate: -1 }), // 按发布日期倒序
      Version.countDocuments(query),
    ]);

    res.json({
      code: 0,
      message: 'success',
      data: {
        list: versions,
        total,
        page: Number(page),
        pageSize: Number(pageSize),
      },
    });
  } catch (error) {
    console.error('获取版本列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
    });
  }
}

module.exports = {
  getVersionList,
};
