<template>
    <a :href="downloadUrl">
        {{ downloadUrl ? '下载' : '-' }}
    </a>

</template>

<script lang="ts" setup>

import { computed } from 'vue'

const props = defineProps<{
    content: string,
    record: any
}>()

const downloadUrl = computed(()=>{
    switch(props.content){
        case '测试包':
            return props.record.devPkg
        case '线上log包':
            return props.record.releaseLogPkg
        case '线上包':
            return props.record.releasePkg
        default:
            return ''
    }
})
</script>