const multer = require('multer');
const express = require('express');

const { fetchSdkGitLabBranches, updateAdInterface } = require('../controller/sdk');
const { getAppInfo } = require('../controller/app-info');
const { appStatus } = require('../controller/status');
const { downloadAPK, createBuildTask, buildTaskList, delTask } = require('../controller/build-task');
const { decodeZip, testStream, testStream2 } = require('../controller/deobfuscator');
const { login,info,logout } = require('../controller/login');
const logMiddleware = require('../controller/log');
const {upgrade} = require('../task/upgrade');
// const { simPack } = require('../controller/sim-pack');

const upload = multer({ storage: multer.memoryStorage() });

const router = express.Router();

router.get('/sdkVersion', fetchSdkGitLabBranches);

router.get('/appList', getAppInfo);

router.post('/delTask', logMiddleware, delTask);

router.get('/updateAdInterface', updateAdInterface);

router.get('/status', appStatus);

router.get('/download', downloadAPK);
router.post('/pack',logMiddleware, createBuildTask);
router.get('/packResult', buildTaskList);
router.post('/upgrade',upgrade);


router.post('/decode', upload.single('file'), decodeZip);

router.get('/stream', testStream);
router.get('/stream2', testStream2);

router.post('/user/login',login)
router.post('/user/info',info)
router.get('/user/logout',logout)
// router.post('/sim-pack', upload.array('file', 2), simPack);

module.exports = router;
