import useAppId from '@/hooks/app-id';

const appId = useAppId();

export const formSchema = {
  fields: [
    {
      name: 'pathName',
      label: '页面名称',
      type: 'text',
      required: true,
      maxLength: 50,
    },
  ],
};

export function getDefaultFormData() {
  return {
    pathName: '页面名称',
  };
}

const operation = [
  {
    text: '新增按钮名称',
    auth: 'auth',
    props: {
      type: 'primary',
      disabled: false,
      loading: false,
    },
    clickActionType: 'modal',
    modal: {
      props: {
        title: '新增弹窗名称',
        width: 700,
        fullscreen: false,
      },
      contentType: 'form',
      getDefaultValue: getDefaultFormData(),
      form: {
        formSchema,
      },
      action: async (value: any) => {},
    },
  },
];

export default operation;
