<template>
  <div class="batch-operation-bar">
    <div class="content" v-if="selectedKeys.length > 0">
      <a-typography-title :heading="6" style="margin: 0" v-if="!hideSelectedTip">
        当前已选{{ selectedKeys.length }}条
      </a-typography-title>
      <template v-for="item in props.operations" :key="item.text">
        <DynamicButton :config="item" :record="selectedKeys" :batch-table-data="batchTableData"></DynamicButton>
      </template>
      <a-button @click="changeBatchStatus(false)" type="text" style="margin-left: auto">
        <icon-close />
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onUnmounted } from 'vue';
  import useBatch from '../../hooks/batch';

  const props = defineProps({
    operations: {
      type: Array,
      default: () => [],
    },
    hideSelectedTip: {
      type: Boolean,
      default: false,
    },
  });

  const { changeBatchStatus, selectedKeys, batchTableData } = useBatch();

  onUnmounted(() => {
    changeBatchStatus(false);
  });
</script>

<style lang="less" scoped>
  #batchParentContainer {
    z-index: 100;
    position: fixed;
    bottom: 0;
    right: 20px;
    height: 70px;

    :deep(.arco-drawer-container) {
      .arco-drawer-body {
        border-top: 1px solid var(--color-border);
        display: flex;
        justify-content: end;
        padding: 20px 0 4px;
      }
    }
  }

  .hide-batch-container {
    display: none;
  }

  .content {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    background-color: rgb(var(--primary-1));
    margin: 16px 24px;
    margin-bottom: 0;
    padding: 8px;

    & > * {
      margin-right: 16px !important;
    }
  }

  .batch-operation-bar {
    background-color: var(--color-bg-1);
  }
</style>
