export interface API {
  connect(requestId: string, platformId: string, token: string | null | undefined): Promise<void>;
  send(data: any): void;
  on(type: string, callback: (data: any) => void): void;
  off(type: string, callback: (data: any) => void): void;
  close(): void;
}

export class WebSocketManager {
  private static instance: WebSocketManager | null = null;

  private url: string;

  private websocket: WebSocket | null = null;

  private isConnected = false;

  private messageQueue: any[] = [];

  private eventHandlers: { [key: string]: ((data: any) => void)[] } = {};

  private topicId: string | null = null;

  private constructor(url: string) {
    this.url = url;
  }

  public static getInstance(url: string): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager(url);
    }
    return WebSocketManager.instance;
  }

  public getConnectionState(): number | null {
    return this.websocket ? this.websocket.readyState : null;
  }

  public async reconnect(): Promise<void> {
    if (this.websocket) {
      this.websocket.close();
    }
    this.isConnected = false;
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (this.websocket) {
          this.websocket = new WebSocket(this.websocket.url);
          this.websocket.onopen = () => {
            this.isConnected = true;
            this.messageQueue.forEach((msg) => this.sendMessage(msg));
            this.messageQueue = [];
            resolve();
          };

          this.websocket.onerror = (error) => {
            reject(error);
          };

          this.websocket.onclose = () => {
            this.isConnected = false;
          };

          this.websocket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            const {
              type,
              data: { topicId },
            } = message;
            if (type === 'onMessage' && message.code === 0) {
              this.topicId = topicId;
            }
            const handlers = this.eventHandlers[type];
            if (handlers) {
              handlers.forEach((handler) => handler(message));
            }
          };
        }
      }, 3000); // 设置重连间隔，例如1秒
    });
  }

  public connect(requestId: string, platformId: string, token: string | null): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnected) {
        resolve();
        return;
      }

      const wsUrl = `${this.url}?requestId=${requestId}&platformId=${platformId}&token=${token}`;
      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = () => {
        this.isConnected = true;
        this.messageQueue.forEach((msg) => this.sendMessage(msg));
        this.messageQueue = [];
        resolve();
      };

      this.websocket.onerror = (error) => {
        reject(error);
      };

      this.websocket.onclose = () => {
        this.isConnected = false;
      };

      this.websocket.onmessage = (event) => {
        const message = JSON.parse(event.data);
        const {
          type,
          data: { topicId },
        } = message;
        if (type === 'onMessage' && message.code === 0) {
          this.topicId = topicId;
        }
        const handlers = this.eventHandlers[type];
        if (handlers) {
          handlers.forEach((handler) => handler(message));
        }
      };
    });
  }

  public async sendMessage(message: any): Promise<void> {
    if (this.websocket && this.websocket.readyState === WebSocket.CONNECTING) {
      // 如果连接正在进行，直接将消息加入队列
      this.messageQueue.push(message);
      return;
    }
    if (this.websocket && this.websocket.readyState !== WebSocket.OPEN) {
      this.messageQueue.push(message);
      this.reconnect();
      return;
    }
    if (this.isConnected && this.websocket) {
      if (this.topicId && message.messageType === 'onMessage') {
        message.data.topicId = this.topicId;
      }
      this.websocket.send(JSON.stringify(message));
    } else {
      this.messageQueue.push(message);
    }
  }

  public onMessage(type: string, callback: (data: any) => void): void {
    if (!this.eventHandlers[type]) {
      this.eventHandlers[type] = [];
    }
    this.eventHandlers[type].push(callback);
  }

  public offMessage(type: string, callback: (data: any) => void): void {
    if (this.eventHandlers[type]) {
      this.eventHandlers[type] = this.eventHandlers[type].filter((handler) => handler !== callback);
    }
  }

  public close(): void {
    if (this.websocket) {
      this.websocket.close();
      console.log('WebSocket closed');
    } else {
      console.error('WebSocket is not initialized');
    }
  }
}

export default WebSocketManager;
