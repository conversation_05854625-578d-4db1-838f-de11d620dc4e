<template>
  <div class="http-input-wrapper">
    <span class="prefix">https://</span>
    <a-input
      :model-value="modelValue"
      @update:model-value="handleInput"
      :placeholder="placeholder"
      class="input"
    />
  </div>
</template>

<script lang="ts" setup>
import { Input as AInput } from '@arco-design/web-vue';

defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入'
  }
});

const emit = defineEmits(['update:modelValue']);

const handleInput = (value: string) => {
  emit('update:modelValue', value);
};
</script>

<style scoped>
.http-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.prefix {
  padding: 0 8px;
  background-color: var(--color-fill-2);
  color: var(--color-text-2);
  border-right: 1px solid var(--color-border);
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  white-space: nowrap;
}

.input {
  flex: 1;
}

:deep(.arco-input-wrapper) {
  border: none;
  box-shadow: none;
}

:deep(.arco-input) {
  border: none;
  padding-left: 0;
}
</style> 