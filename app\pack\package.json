{"name": "pack", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "echo \"Error: no build specified\" ", "test": "jest", "start": "node index.js", "add": "node src/scripts/addAdmin.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/traverse": "^7.26.5", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "axios": "^0.24.0", "depd": "^2.0.0", "jsonwebtoken": "^9.0.2", "merge-descriptors": "^2.0.0", "mongoose": "^8.9.2", "multer": "1.4.5-lts.1", "nanoid": "^5.1.5", "obfuscator-io-deobfuscator": "^1.0.4", "pm2": "^5.4.3", "split2": "^4.2.0", "unzipper": "^0.12.3", "uuid": "^9.0.1", "vite-plugin-monaco-editor": "^1.1.0", "ws": "^8.18.0"}}