import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const DASHBOARD: AppRouteRecordRaw = {
  path: '/seed',
  name: 'seed',
  components: {
    default: DEFAULT_LAYOUT,
    content: () => import('@/views/seed/main/index.vue'),
  },
  meta: {
    hideChildrenInMenu: true,
    name: 'Seed',
    requiresAuth: true,
    icon: 'icon-code',
    order: 1,
    roles: ['admin', 'FRE', 'UI'],
  },
  children: [
    {
      path: 'create/:_id',
      name: 'seed_create',
      component: () => import('@/views/seed/create/index.vue'),
      meta: {
        name: 'Create Seed',
        requiresAuth: true,
        roles: ['admin', 'FRE', 'UI'],
      },
    },
  ],
};

export default DASHBOARD;
