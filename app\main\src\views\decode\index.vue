<template>
  <div class="page">
    <h1>解混淆</h1>
    <p>上传RPK或者ZIP包</p>
    <a-spin :loading="loading" :tip="`${Math.floor(progress)}%`">
      <a-upload :custom-request="customRequest" />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';

  const loading = ref(false);
  const progress = ref(0);
  let progressInterval: number | undefined;

  function startProgress() {
    progress.value = 0;
    progressInterval = setInterval(() => {
      if (progress.value < 99) {
        progress.value += 2.475; // 40s内从0到99
      }
    }, 1000);
  }

  function stopProgress() {
    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = undefined;
    }
    progress.value = 100;
  }

  watch(loading, (newValue) => {
    if (newValue) {
      startProgress();
    } else {
      stopProgress();
    }
  });

  function customRequest(option: any) {
    const { onProgress, onError, onSuccess, fileItem, name } = option;
    onSuccess();
    loading.value = true;
    const formData = new FormData();
    formData.append('file', fileItem.file);
    fetch('/api/decode', {
      method: 'POST',
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server error: ${response.statusText}`);
        }
        return response.blob(); // 获取二进制文件数据
      })
      .then((blob) => {
        loading.value = false;
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'decode.zip'; // 下载的文件名
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      })
      .catch((error) => {
        if (onError) {
          onError(error);
        }
      });
  }
</script>

<style>
  .page {
    padding: 20px;
    background: #fff;
  }
</style>
