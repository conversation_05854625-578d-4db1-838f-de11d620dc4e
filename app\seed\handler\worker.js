const { parentPort } = require('worker_threads');

const {main} = require('../src/index.js')

parentPort.on('message', async (task) => {
    try {
        const res = await main(JSON.parse(task));
        parentPort.postMessage({ type: 'success', ...res });
        // 将结果发送回主线程
    } catch (error) {
        parentPort.postMessage({ type: 'error', error: error.message });
        console.log('worker-err',error)
    }
  });