const axios = require('axios');
const split2 = require('split2');

const aiHost = process.env.NODE_ENV === 'production' ? 'http://localhost:11434' : 'http://fe.local/ai';
const fastHost = process.env.NODE_ENV === 'production' ? 'http://localhost:8000' : 'http://fe.local/fast';
const getAnswer = async (req, res) => {
  const { prompt } = req.query;
  // 设置 SSE 响应头
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');

  const data = {
    model: 'qwen3:0.6b',
    messages: [{ role: 'user', content: `${prompt}/no_think` }],
  };

  axios
    .post(`${aiHost}/api/chat`, data, {
      responseType: 'stream', // 设置响应类型为流
      headers: {
        'Content-Type': 'application/json',
      },
    })
    .then((response) => {
      const stream = response.data;

      // 使用 split2 按 \n\n 分割流
      const lineStream = stream.pipe(split2('\n'));

      lineStream.on('data', (line) => {
        if (line.trim()) {
          // 忽略空行
          res.write(`data: ${line}\n\n`); // 符合 SSE 格式
        }
      });

      lineStream.on('end', () => {
        console.log('Stream ended');
        res.end();
      });

      lineStream.on('error', (error) => {
        console.error('Stream error:', error);
        res.end();
      });
    })
    .catch((error) => {
      console.error('Error:', error.response?.data || error.message);
      res.end();
    });
};

const getAnswerV2 = async (req, res) => {
  // 设置 SSE 响应头
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  const { prompt } = req.query;

  axios
    .get(`${fastHost}/answer?query=${encodeURIComponent(prompt)}\\no_think`, {
      responseType: 'stream', // 设置响应类型为流
    })
    .then((response) => {
      const stream = response.data;
      stream.on('data', (line) => {
        res.write(`data: ${line.toString().replace(/\n/, '\\n')}\n\n`); // 符合 SSE 格式
      });

      stream.on('end', () => {
        res.end();
      });

      stream.on('error', (error) => {
        console.error('Stream error:', error);
        res.end();
      });
    })
    .catch((error) => {
      console.error('Error:', error.response?.data || error.message);
      res.end();
    });
};

module.exports = {
  getAnswer,
  getAnswerV2,
};
