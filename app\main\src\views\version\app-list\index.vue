<template>
  <DynamicPage
    :filter="filterConfig"
    :table="tableConfig"
    :operation="operations"
    :batch-operation="batchOperations"
    auto-load
    row-key="_id"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import CompetitorsAppApi from '@/api/CompetitorsApp';

  // Filter 配置
  const filterConfig = ref({
    formSchema: {
      fields: [
        {
          name: 'name',
          label: 'name',
          type: 'text',
          placeholder: '请输入name',
        },
        {
          name: 'packageName',
          label: 'packageName',
          type: 'text',
          placeholder: '请输入packageName',
        },
        {
          name: 'icon',
          label: 'icon',
          type: 'text',
          placeholder: '请输入icon',
        },
        {
          name: 'company',
          label: 'company',
          type: 'text',
          placeholder: '请输入company',
        },
      ],
    },
  });

  // 数据加载方法
  async function loadData(filter, pagination) {
    return CompetitorsAppApi.list({
      ...filter,
      ...pagination,
    });
  }

  // Table 配置
  const tableConfig = ref({
    columns: [
      {
        title: 'name',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: 'packageName',
        dataIndex: 'packageName',
        ellipsis: true,
      },
      {
        title: 'icon',
        dataIndex: 'icon',
        ellipsis: true,
        customRender: {
          type: 'image',
        },
      },
      {
        title: 'company',
        dataIndex: 'company',
        ellipsis: true,
      },
      {
        title: '操作',
        dataIndex: 'operations',
        customRender: {
          type: 'operations',
          props: {
            operations: [
              {
                text: '编辑',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    'title': '编辑',
                    'esc-to-close': false,
                  },
                  contentType: 'form',
                  form: {
                    formSchema: {
                      fields: [
                        {
                          name: 'name',
                          label: 'name',
                          type: 'text',
                          placeholder: '请输入name',
                        },
                        {
                          name: 'packageName',
                          label: 'packageName',
                          type: 'text',
                          placeholder: '请输入packageName',
                        },
                        {
                          name: 'icon',
                          label: 'icon',
                          type: 'text',
                          placeholder: '请输入icon',
                        },
                        {
                          name: 'company',
                          label: 'company',
                          type: 'text',
                          placeholder: '请输入company',
                        },
                      ],
                    },
                  },
                  getDefaultValue(rowData) {
                    const { _id: id } = rowData;
                    return CompetitorsAppApi.getById(id).then(
                      (res) => res.data
                    );
                  },
                  action: async ({ formData, record, refreshTable }) => {
                    const { _id: id } = record;
                    await CompetitorsAppApi.update(id, formData);
                    refreshTable();
                  },
                },
              },
              {
                text: '删除',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    title: '确认删除',
                  },
                  contentType: 'text',
                  text: '确认删除？',
                  action: async ({ record, refreshTable }) => {
                    const { _id: id } = record;
                    await CompetitorsAppApi.delete(id);
                    refreshTable();
                  },
                },
              },
            ],
          },
        },
      },
    ],
    isPageable: true,
    pagination: {
      current: 1,
      pageSize: 10,
      showPageSize: true,
      showTotal: true,
      total: 0,
      showJumper: true,
      pageSizeOptions: [10, 20, 50],
    },
    load: {
      action: loadData,
    },
  });

  // 操作按钮配置
  const operations = ref([
    {
      text: '新增',
      props: {
        type: 'primary',
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '新增',
          width: 800,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                name: 'name',
                label: 'name',
                type: 'text',
                placeholder: '请输入name',
              },
              {
                name: 'packageName',
                label: 'packageName',
                type: 'text',
                placeholder: '请输入packageName',
              },
              {
                name: 'icon',
                label: 'icon',
                type: 'text',
                placeholder: '请输入icon',
              },
              {
                name: 'company',
                label: 'company',
                type: 'text',
                placeholder: '请输入company',
              },
            ],
          },
        },
        getDefaultValue: () => {
          return {
            name: '',
            packageName: '',
            icon: '',
            company: '',
          };
        },
        close: (data) => {
          data.refreshTable();
        },
        action: async (data) => {
          await CompetitorsAppApi.create(data.formData);
          Message.success('操作成功');
          data.refreshTable();
        },
      },
    },
    {
      text: '批量操作',
      props: {
        type: 'primary',
      },
      clickActionType: 'batch',
    },
  ]); // 单个操作按钮
  const batchOperations = ref([]); // 批量操作按钮
</script>
