const prompt = require('@system.prompt')
class Tools {

    constructor() {

    }
    queryString(url, query) {
        let str = []
        for (let key in query) {
            str.push(key + '=' + query[key])
        }
        let paramStr = str.join('&')
        return paramStr ? `${url}?${paramStr}` : url
    }
    showToast(message = '', duration = 0) {
        if (!message) return
        prompt.showToast({
            message: message,
            duration,
        })
    }


}

export default Tools;