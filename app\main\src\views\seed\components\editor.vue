<template>
  <div
    class="editor-container"
    :style="{ height: height, width: '100%' }"
    ref="editorContainer"
  ></div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import * as monaco from 'monaco-editor'

const props = defineProps({
  modelValue: { type: [String, Object], default: '' },
  readOnly: { type: Boolean, default: false },
  height: { type: String, default: '200px' },
  defaultValue: { type: [String, Object], default: '' }
})
const emit = defineEmits(['update:modelValue', 'change'])

const editorContainer = ref(null)
let editorInstance = null

function getValue(val) {
  if (typeof val === 'object') {
    try {
      return JSON.stringify(val, null, 2)
    } catch {
      return ''
    }
  }
  return val || ''
}

onMounted(() => {
  editorInstance = monaco.editor.create(editorContainer.value, {
    value: getValue(props.modelValue || props.defaultValue),
    language: 'json',
    readOnly: props.readOnly,
    theme: 'vs',
    automaticLayout: true
  })

  editorInstance.onDidChangeModelContent(() => {
    if (props.readOnly) return
    let value = editorInstance.getValue()
    try {
      value = JSON.parse(value)
    } catch {
      // 保持字符串
    }
    emit('update:modelValue', value)
    emit('change', value)
  })
})

// 监听 modelValue 外部变化
watch(
  () => props.modelValue,
  (val) => {
    if (!editorInstance) return
    const newVal = getValue(val)
    if (editorInstance.getValue() !== newVal) {
      editorInstance.setValue(newVal)
    }
  }
)

// 监听 readOnly
watch(
  () => props.readOnly,
  (val) => {
    if (editorInstance) {
      editorInstance.updateOptions({ readOnly: val })
    }
  }
)

// 监听 height 变化
watch(
  () => props.height,
  () => {
    nextTick(() => {
      if (editorInstance) editorInstance.layout()
    })
  }
)

// 监听弹窗打开（父组件 visible 变化时，建议传递 visible prop 进来，或用 v-if 控制 Editor 渲染）
defineExpose({
  layout: () => {
    if (editorInstance) editorInstance.layout()
  }
})
</script>

<style scoped>
.editor-container {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
}
</style>