const path = require('path')
const crypto = require('crypto')
const axios = require('axios');
const { GITLAB_TOKEN, REPO_ID_MAP } = require('../constant/index')


function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function generateUniqueId() {
  let uniqueId = '';
  for (let i = 0; i < 12; i++) {
    uniqueId += Math.floor(Math.random() * 10); // 随机生成0-9之间的数字
  }
  return uniqueId;
}

function extractVersion(filename) {
  // 使用正则表达式匹配版本号
  if (!filename) return null;
  const versionPattern = /\d+\.\d+\.\d+/;
  const match = filename.match(versionPattern);
  return match ? match[0] : null; // 如果找到匹配，返回版本号；否则返回 null
}

function getAppName(appName, key, targetDir) {
  console.log('key,', key)
  let newAppName = ''
  const packCode = key.split('_')[0]
  const pkgInfoPath = path.join(targetDir, 'node_modules/ad-interface/bin/config/pkg-info.js')
  delete require.cache[require.resolve(pkgInfoPath)]; // 清除缓存
  const pkgInfo = require(pkgInfoPath)
  for (const category in pkgInfo) {
    const subCategory = pkgInfo[category];
    for (const key in subCategory) {
      if (subCategory[key].pkg === packCode) {
        newAppName = subCategory[key].name;  // 找到对应 pkg 的 name
        break;
      }
    }
  }
  return newAppName || appName
}

function getBuildError(output) {
  if (output.includes('hap: not found')) {
    return 'hap: not found';
  }
  console.log('output', output);
  const reg = /Error: (.*)/;
  const match = output.match(reg);
  return match ? match[1] : null;
}


function timeFormat(timeStamp) {
  return new Date(timeStamp || 0).toLocaleString()
}

function getWorkSpace(brand, sdkVersion) {
  if (sdkVersion === 'release-v2.1.2p'){
    return path.join(__dirname, '../../build', 'qkproject-pure')  // pure
  }
  // 使用正则提取版本号并比较
  const versionMatch = sdkVersion.match(/v(\d+)\.(\d+)\.(\d+)/);
  if (versionMatch) {
    const [major, minor, patch] = versionMatch.slice(1).map(Number);
    if (major === 2 && minor === 2 && patch >= 0) {
      return path.join(__dirname, '../../build', 'qkproject')  // hw-220
    }
  }
  if (brand === 'xiaomi') {
    return path.join(__dirname, '../../build', 'qkproject')  // master
  }
  else {
    if (sdkVersion.match(/\d+/) && Number(sdkVersion.match(/\d+/)[0]) >= 4) {
      return path.join(__dirname, '../../build', 'qkproject')  // master
    }
    else {
      return path.join(__dirname, '../../build', 'qkproject-develop')  // develop
    }
  }
}


function removeEmptyProperties(obj) {
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      // 检查属性值是否为空（null、undefined、空字符串）
      if (obj[key] === null || obj[key] === undefined || obj[key] === '') {
        delete obj[key]; // 删除属性
      }
    }
  }
  return obj;
}


function salt() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let salt = '';
  for (let i = 0; i < 10; i++) {
    salt += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return salt;
}

function sha256(raw) {
  return crypto.createHash('sha256').update(raw).digest('hex');
}

async function createMergeRequest(originBranch, targetBranch, title, description) {
  const PROJECT_ID = REPO_ID_MAP['qkproject']; // 项目 ID, fe/quickapp/qkproject
  const SOURCE_BRANCH = originBranch; // 你的分支
  const TARGET_BRANCH = targetBranch; // 目标分支
  try {
    const response = await axios.post(
      `https://gitlab.ghfkj.cn/api/v4/projects/${PROJECT_ID}/merge_requests`,
      {
        source_branch: SOURCE_BRANCH,
        target_branch: TARGET_BRANCH,
        title: title, // MR 标题
        description: description, // MR 描述
      },
      {
        headers: {
          'PRIVATE-TOKEN': GITLAB_TOKEN,
        },
      }
    );
    return response.data.web_url || '';
  } catch (error) {
    console.error('Error creating Merge Request:', error.response.data);
    return ''
  }
}


async function getCommits(repo, branch, count = 3) {
  try {
    const response = await axios.get(
      `https://gitlab.ghfkj.cn/api/v4/projects/${REPO_ID_MAP[repo]}/repository/commits?ref_name=${branch}&per_page=${count}`,
      {
        headers: {
          'PRIVATE-TOKEN': GITLAB_TOKEN,
        },
      }
    )
    return response.data

  } catch (e) { 
    console.log('getCommits-error',e)
  }

}

async function getOneCommit(repo, branch) {
  try {
    const latestCommit = await getCommits(repo, branch, 5)
    console.log('latestCommit', latestCommit)
    const neatCommit = latestCommit.map((item) => { return { title: item.title, id: item.id } }).filter(item => !item.title.includes('Merge branch'))
    const commit = `commit: ${neatCommit[0].id.substr(0, 7)};\n${neatCommit[0].title}`
    return commit
  } catch (error) {
    console.error('Error getting commit:', error);
    return '';
  }
}


module.exports = {
  sleep,
  generateUniqueId,
  extractVersion,
  getAppName,
  getBuildError,
  timeFormat,
  getWorkSpace,
  removeEmptyProperties,
  salt,
  sha256,
  createMergeRequest,
  getCommits,
  getOneCommit
};
