<template>
  <a-switch
    @change="onSwitchChange"
    :model-value="props.record[props.column.dataIndex!]"
    :checked-value="props.checkedValue || 1"
    :unchecked-value="props.uncheckedValue || 0"
    :loading="loading"
    v-auth="props.auth"
  >
    <template #checked-icon>
      <icon-check />
    </template>
    <template #unchecked-icon>
      <icon-close />
    </template>
  </a-switch>
</template>

<script lang="ts" setup>
  import { defineProps } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import useLoading from '@repo/sdk/hooks/loading';
  import _ from 'lodash';
  import { RenderProps } from '../type';
  import useRefreshTable from '../../../hooks/refresh-table';

  const props = defineProps<
    RenderProps & {
      action: any;
      checkedValue?: any;
      uncheckedValue?: any;
      confirm?: any;
      auth?: string;
      hideMessage?: boolean;
    }
  >();

  const { loading, setLoading } = useLoading();
  const refreshFn = useRefreshTable();

  async function onAction(value: string | number | boolean) {
    await props.action(value, props.record);
    if (!props.hideMessage) {
      Message.success('操作成功');
    }
    refreshFn?.();
  }

  async function onSwitchChange(value: boolean | string | number) {
    if (props.confirm && (!props.confirm.times || _.includes(props.confirm.times, value))) {
      Modal.open({
        title: props.confirm.title,
        content: props.confirm.content,
        onBeforeOk: async () => onAction(value),
      });
    } else {
      setLoading(true);
      try {
        await onAction(value);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.log('e', e);
      } finally {
        setLoading(false);
      }
    }
  }
</script>
