import { Fragment, getCurrentInstance, h, inject, reactive, ref, resolveComponent } from 'vue';
import { cloneDeep, isFunction, noop } from 'lodash';
// eslint-disable-next-line import/namespace
import { Alert } from '@arco-design/web-vue';
import { DButtonModal } from '../../types/button';
import useRefreshTable from '../../hooks/refresh-table';
import useDispatch from '../../hooks/dispatch';

const useOperationModal = ({ modalOptions }: { modalOptions: DButtonModal }) => {
  const currentInstance = getCurrentInstance();

  const contentRef = ref();

  const loading = ref(false);

  const getCustomParams = inject('getCustomParams', noop);

  const refreshTable = useRefreshTable();

  const dispatch = useDispatch();

  const showOperationModal = ({
    record,
    defaultFormData,
    isDrawer = false,
  }: {
    record?: Record<string, any>;
    defaultFormData?: any;
    isDrawer?: boolean;
  }) => {
    if (!currentInstance) {
      return;
    }

    const formData = reactive(cloneDeep(defaultFormData ?? {}));

    const AlertRender = (message) => {
      return () =>
        h(
          'div',
          {
            style: {
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1,
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
            },
          },
          [h(Alert, { type: 'warning' }, message)]
        );
    };

    const contentRender = h(Fragment, [
      formData?.showAlert ? h(AlertRender(modalOptions?.alert?.message)) : null,
      h(contentTypeRender),
    ]);

    const config = {
      ...modalOptions.props,
      formData,
      content: contentRender,
      onBeforeOk: () => handleBeforeOK(record),
      okLoading: loading.value,
      onBeforeCancel: () => handleClose(record),
    };

    const { close, update } = isDrawer
      ? currentInstance.appContext.config.globalProperties.$drawer.open(config)
      : currentInstance.appContext.config.globalProperties.$modal.open(config);

    function contentTypeRender() {
      const baseProps = {
        ref: contentRef,
        record,
        loading: loading.value,
        setLoading, // 组件内部控制loading
        getCustomParams, // 组件内部的参数处理
        onClose: close, // 组件内部控制弹窗关闭
      };
      switch (modalOptions.contentType) {
        case 'form': {
          const DynamicForm = resolveComponent('DynamicForm');
          return h(DynamicForm, {
            ...baseProps,
            ...modalOptions.form,
            modelValue: formData,
          });
        }
        case 'custom': {
          const customComp = modalOptions.custom || 'div';
          return h(customComp, {
            data: defaultFormData,
            ...baseProps,
            modelValue: formData,
          });
        }
        case 'detail': {
          return h(resolveComponent('DynamicDetail'), {
            ...baseProps,
            ...modalOptions.detail,
          });
        }
        case 'text': {
          return h('div', isFunction(modalOptions.text) ? modalOptions.text() : modalOptions.text);
        }
        default: {
          return h('none');
        }
      }
    }

    async function handleBeforeOK(records?: Record<string, any>): Promise<boolean> {
      switch (modalOptions.contentType) {
        case 'form': {
          try {
            await contentRef.value.validate();
            if (formData?.showAlert) {
              return true;
            }
            setLoading(true);
            const res = await modalOptions.action?.({
              formData,
              record: records,
              refreshTable,
              dispatch,
              getCustomParams,
              closePopover: close,
            });
            setLoading(false);
            return res !== false;
          } catch (e) {
            // eslint-disable-next-line no-console
            console.log('e', e);
            setLoading(false);
            return false;
          }
        }
        case 'custom': {
          try {
            setLoading(true);
            await contentRef.value.done?.({ dispatch, getCustomParams });
            if (formData?.showAlert) {
              return true;
            }
            const res = await modalOptions.action?.({
              formData,
              record: records,
              dispatch,
              refreshTable,
            });
            return res !== false;
          } catch (e) {
            // eslint-disable-next-line no-console
            console.log('e', e);
            return false;
          } finally {
            setLoading(false);
          }
        }
        default: {
          try {
            if (formData?.showAlert) {
              return true;
            }
            setLoading(true);
            await modalOptions.action?.({
              formData,
              record: records,
              dispatch,
              refreshTable,
              getCustomParams,
            });
            setLoading(false);
            return true;
          } catch (e) {
            // eslint-disable-next-line no-console
            console.log('e', e);
            setLoading(false);
            return false;
          }
        }
      }
    }

    const handleClose = (records) => {
      modalOptions.close?.({
        formData,
        record: records,
        refreshTable,
        dispatch,
        getCustomParams,
      });
      return true;
    };

    const setLoading = (isLoading: boolean) => {
      loading.value = isLoading;
      update({ okLoading: isLoading });
    };

    // eslint-disable-next-line consistent-return
    return { update, setOkLoading: setLoading };
  };

  return {
    showOperationModal,
    refreshTable,
  };
};

export default useOperationModal;
