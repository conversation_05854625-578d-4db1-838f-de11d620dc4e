import { onBeforeUnmount, Ref } from 'vue';

let isDragging = false;
let startX = 0;
let startY = 0;
let initialTop = 0;
let initialLeft = 0;
export const useDragging = (chartWrapper: Ref<HTMLElement | null | undefined> | undefined) => {
  const startDragging = (event: MouseEvent) => {
    if (!chartWrapper || !chartWrapper.value) return;

    isDragging = true;
    startX = event.clientX;
    startY = event.clientY;

    const rect = chartWrapper.value.getBoundingClientRect();
    initialTop = rect.top;
    initialLeft = rect.left;

    document.addEventListener('mousemove', onDragging);
    document.addEventListener('mouseup', stopDragging);
  };
  const onDragging = (event: MouseEvent) => {
    if (!isDragging || !chartWrapper || !chartWrapper.value) return;

    const deltaX = event.clientX - startX;
    const deltaY = event.clientY - startY;

    // 获取当前窗口的宽度和高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 获取元素的宽度和高度

    const elementWidth = chartWrapper.value.offsetWidth;
    const elementHeight = chartWrapper.value.offsetHeight;

    // 计算新位置
    let newLeft = initialLeft + deltaX;
    let newTop = initialTop + deltaY;

    // 限制左边界（最小值为 0）
    if (newLeft < 0) {
      newLeft = 0;
    }

    // 限制右边界（最大值为窗口宽度减去元素宽度）
    if (newLeft + elementWidth > windowWidth) {
      newLeft = windowWidth - elementWidth;
    }

    // 限制上边界（最小值为 0）
    if (newTop < 0) {
      newTop = 0;
    }

    // 限制下边界（最大值为窗口高度减去元素高度）
    if (newTop + elementHeight > windowHeight) {
      newTop = windowHeight - elementHeight;
    }
    // 更新元素位置
    if (chartWrapper) {
      chartWrapper.value.style.left = `${newLeft}px`;
      chartWrapper.value.style.top = `${newTop}px`;
    }
  };

  const stopDragging = () => {
    if (!isDragging) return;

    isDragging = false;
    document.removeEventListener('mousemove', onDragging);
    document.removeEventListener('mouseup', stopDragging);
  };

  onBeforeUnmount(() => {
    document.removeEventListener('mousemove', onDragging);
    document.removeEventListener('mouseup', stopDragging);
  });

  return {
    startDragging,
    onDragging,
    stopDragging,
  };
};
export default useDragging;
