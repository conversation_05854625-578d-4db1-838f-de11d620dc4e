#!/usr/bin/env node
const { program } = require('commander');
const  mainHandler  = require('../handler/index'); // 假设你的主处理函数在index.js中


// 配置命令行参数
program
  .version('1.0.0')
  .description('Convert .ux files to .vue files')
  .option('-i, --input <component-name>', 'convert.ux files to.vue files')
  .parse(process.argv);

const options = program.opts();

if(!options.input){
    console.error('Please specify the component name using the --name option.');
    process.exit(1);
}

mainHandler(options.input);

console.log(options);
