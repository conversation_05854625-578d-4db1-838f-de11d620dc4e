const mongoose = require('mongoose');
const { Schema } = mongoose;

// 定义组件子 schema
const componentSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  label: { type: String, required: true },
  pinyin: { type: String, required: true },
  props: { type: Schema.Types.Mixed, default: {} },
  styles: { type: Schema.Types.Mixed, default: {} }
});

// 定义页面子 schema
const pageSchema = new Schema({
  pageName: { type: String, required: true },
  pageId: { type: String, required: true },
  themeColor: { type: String, required: true },
  backgroundColor: { type: String, required: true },
  path: { type: String, default: '' },
  components: { type: [componentSchema], default: [] }
});

const appSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    packageName: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    version: {
      type: String,
      required: true,
      trim: true,
    },
    icon: {
      type: String,
      default: '',
    },
    // 内容配置
    content: {
      type: {
        main: { type: [pageSchema], default: [] },
        subPages: { type: [pageSchema], default: [] }
      },
      default: {
        main: [],
        subPages: []
      }
    },
    theme: {
      type: Object,
    },
    private: {
      type: String,
      default: '',
    },
    agreement: {
      type: String,
      default: '',
    },
  },
  {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
  }
);

module.exports = mongoose.model('SeedApp', appSchema);
