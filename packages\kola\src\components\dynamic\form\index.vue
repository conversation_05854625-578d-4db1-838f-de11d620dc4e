<template>
  <a-form
    ref="formRef"
    auto-label-width
    :model="formData"
    :layout="layout"
    :disabled="formDisabled"
    :scroll-to-first-error="isScrollToError"
    :style="arcoFormProps.style || {}"
    :label-col-props="arcoFormProps.labelColProps"
    :label-align="arcoFormProps.labelAlign"
  >
    <template v-if="layout !== 'inline'">
      <WidgetByType :field="composedObjectField" v-model="formData" path="" />
      <slot name="footer"></slot>
    </template>
    <template v-else>
      <template v-for="f in formSchema.fields" :key="f.name">
        <template v-if="useResponsive">
          <div :class="useResponsive ? 'field-responsive' : ''" :style="{ display: f?.hidden ? 'none' : 'block' }">
            <WidgetByType
              :field="f"
              v-model="formData[f.name]"
              v-if="!f.visibleOn || f.visibleOn(formData[f.name], formData)"
              path=""
            />
          </div>
        </template>
        <template v-else>
          <WidgetByType
            :field="f"
            v-model="formData[f.name]"
            v-if="!f.visibleOn || f.visibleOn(formData[f.name], formData)"
            path=""
          />
        </template>
      </template>

      <slot name="footer"></slot>
    </template>
  </a-form>
</template>

<script lang="ts" setup>
  import { computed, provide, ref, onMounted, Fragment } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { cloneDeep, isFunction, isNil, uniqueId } from 'lodash';
  import { DForm, FormData, ObjectField as ObjectFieldType, ArcoFormProps } from '../types/form';
  import WidgetByType from './widgets/widget-by-type.vue';
  import useDispatch from '../hooks/dispatch';

  defineOptions({ name: 'DynamicForm' });

  // 用于事件控制
  const formId = uniqueId();
  provide('formId', formId);

  const formData = defineModel<FormData>({ default: {} });

  const props = withDefaults(defineProps<DForm>(), {
    layout: 'horizontal',
    arcoFormProps: () => ({} as ArcoFormProps),
  });
  const isScrollToError = computed(() => {
    return isNil(props.isScrollToError) ? true : props.isScrollToError;
  });
  const defaultFormData = cloneDeep(props.defaultFormData ?? formData.value);
  const formDisabled = computed(() => {
    if (isNil(props.disabled)) return false;
    if (isFunction(props.disabled)) return props.disabled(formData.value);
    return props.disabled;
  });

  const formRef = ref<FormInstance>();

  const composedObjectField = computed<ObjectFieldType>(() => {
    return {
      type: 'object',
      fields: props.formSchema.fields,
      hideLabel: true,
      name: '',
      label: '',
      layout: props.layout,
    };
  });

  defineExpose({
    reset: () => {
      formData.value = cloneDeep(props.resetFormData ?? defaultFormData);
      formRef.value?.clearValidate();
    },
    validate: async () => {
      useDispatch(formId)({ type: 'formValidate' });
      return new Promise((resolve, reject) => {
        formRef.value?.validate((errors) => {
          if (!errors) {
            resolve(true);
          } else {
            // eslint-disable-next-line prefer-promise-reject-errors
            reject({
              type: 'form',
              errors: false,
              fields: errors,
            });
          }
        });
      });
    },
    clearValidate: () => {
      formRef.value?.clearValidate();
    },
    scrollToField: (field: string) => {
      formRef.value?.scrollToField(field);
    },
    setFields: (data: Record<string, any>) => {
      formRef.value?.setFields(data);
    },
    validateField: (field: string) => {
      formRef.value?.validateField(field);
    },
    getRef: () => formRef.value,
  });

  provide('formData', formData);
  provide('formRef', formRef);
</script>

<style lang="less" scoped>
  @breakpoint-large: 1281px;
  @breakpoint-xlarge: 1441px;
  @breakpoint-xxlarge: 1921px;

  .field-responsive {
    padding-right: 16px;
    width: 50%;

    .responsive-width(@breakpoint-large, 33%);
    .responsive-width(@breakpoint-xlarge, 25%);
    .responsive-width(@breakpoint-xxlarge, 20%);
  }

  .responsive-width(@breakpoint, @width) {
    @media (min-width: @breakpoint) {
      width: @width;
    }
  }
</style>
