<template>
    <div class="user-section" id="userSection">
      <a-popover popup-container="#userSection">
        <div class="user">
          <userSvg />
        </div>
        <template #content>
          <div class="user-content">
            <div class="user-first">
              <userSvg />
              <div class="user-info">
                <div class="user-name">{{ user?.name || '未知用户' }}</div>
                <div
                  class="user-role"
                  v-if="user?.roleListLength > 0 && user?.roleListLength <= 1"
                  >{{ user?.roleName }}
                </div>
                <template v-if="user?.roleListLength > 1">
                  <a-tooltip :content="user?.roleListName" position="bl">
                    <span class="user-role"> {{ user?.roleName }}</span>
                  </a-tooltip>
                </template>
              </div>
            </div>
            <div class="user-item" @click="logout">退出登录</div>
          </div>
        </template>
      </a-popover>
    </div>
  </template>
  
  <script lang="ts" setup>
    import { useUserStore } from '@/store';
    import { auth } from '@repo/sdk';
    import userSvg from '@/components/icons/user.vue';
    import { computed } from 'vue';
  
    const userStore = useUserStore();
    console.log('userStore',userStore)
    const user = computed(() => {
      if (!userStore.userName || !userStore.roleList) return null;
      const roleListLength = userStore.roleList.length;
      const firstRoleName = userStore.roleList[0].roleName;
      const roleListName = userStore.roleList
        .map((item) => item.roleName)
        .join('、');
      return {
        name: userStore.userName,
        roleListLength,
        roleName: roleListLength > 1 ? `${firstRoleName}...` : firstRoleName,
        roleListName,
      };
    });
  
    function logout() {
      auth.logout();
    }
  </script>
  
  <style scoped lang="less">
    #userSection {
      position: relative;
  
      .user {
        height: 39px;
        display: flex;
        align-items: center;
        cursor: pointer;
      }
  
      .user-content {
        width: 192px;
  
        .user-first {
          padding-left: 20px;
          display: flex;
          align-items: center;
          height: 75px;
          width: 100%;
          border-bottom: 1px solid rgb(0 0 0 / 8%);
  
          .user-info {
            margin-left: 12px;
  
            .user-name {
              font-size: 14px;
              line-height: 28px;
              color: #000;
            }
  
            .user-role {
              font-size: 12px;
              line-height: 22px;
              color: #4e5969;
            }
          }
        }
  
        .user-item {
          height: 48px;
          font-size: 14px;
          color: #000;
          line-height: 48px;
          cursor: pointer;
          padding-left: 16px;
  
          &:hover {
            background-color: var(--color-fill-2);
          }
        }
      }
  
      :deep(.arco-trigger-popup) {
        .arco-popover-popup-content {
          border-radius: 2px;
          padding: 0;
        }
  
        .arco-popover-content {
          margin-top: 0;
        }
      }
    }
  </style>
  