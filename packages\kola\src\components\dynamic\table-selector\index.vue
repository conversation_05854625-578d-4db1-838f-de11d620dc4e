<template>
  <div class="container">
    <div class="source">
      <div class="filter" v-if="filter">
        <DynamicForm :form-schema="filter.formSchema" v-model="filterData" layout="inline">
          <template #footer v-if="showFilterOperation">
            <div>
              <a-button type="primary" @click="query" class="search-btn">
                <template #icon>
                  <icon-search />
                </template>
                查询
              </a-button>
              <a-button @click="reset" class="reset-btn">
                <template #icon>
                  <icon-refresh />
                </template>
                重置
              </a-button>
            </div>
          </template>
        </DynamicForm>
      </div>
      <slot name="filter"></slot>
      <div class="option" v-if="options">
        <a-space layout="inline">
          <a-tag v-if="props.showCount">{{ countString }}</a-tag>
          <a-tag v-if="props.customTip">{{ customTip }}</a-tag>
          <slot name="title"></slot>
          <a-space v-if="props.options">
            <DynamicButton :config="item" v-for="(item, index) in options" :key="index" />
          </a-space>
        </a-space>
      </div>
      <div class="table">
        <Table
          ref="tableRef"
          v-model="formData"
          :filter="filterData"
          :load="load"
          :columns="computedColumns"
          :value-key="valueKey"
          :label-key="labelKey"
          :extra-fields="extraFields"
          :extra-value="extraValue"
          :is-multiple="isMultiple"
          :select-all-fields="selectAllFields"
          :show-filter-operation="showFilterOperation"
          :hide-selection="hideSelection"
          :up-to-date-table-data="upToDateTableData"
          :custom-pagination="customPagination"
        />
      </div>
      <slot name="extra"></slot>
    </div>
    <div class="target" v-if="!hideTarget" :style="rightOperationStyle">
      <div class="selected">
        <span>{{ selectedString }}</span>
        <a-button type="text" @click="handleClear" v-if="showClear">清空</a-button>
      </div>
      <a-tag class="tag" closable v-for="item in formData" :key="item[valueKey]" @close="handleRemoveItem(item)">
        {{ item[labelKey] }}
      </a-tag>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch, computed, nextTick } from 'vue';
  import { cloneDeep, isFunction } from 'lodash';
  import Table from './table.vue';
  import { DTableSelector } from '../types/table-selector';
  import formatColumns from '../table/column-render/format-columns.tsx';
  import { FormData } from '../types/form';

  const tableRef = ref();
  const props = defineProps<DTableSelector>();

  const formData = defineModel<Record<string, any>[]>({ default: [] });

  const handleRemoveItem = (item) => {
    formData.value = formData.value.filter((data) => data[props.valueKey] !== item[props.valueKey]);
  };

  const handleClear = () => {
    formData.value = [];
  };

  const filterData = ref<FormData>({});
  const computedColumns = computed(() => {
    return formatColumns(props.columns);
  });
  const countString = computed(() => {
    if (isFunction(props.showCount)) {
      return props.showCount(formData.value?.length || 0);
    }

    const allDataCount = props.showAllCount ? `/${tableRef.value?.pagination?.total}` : '';

    return `已选择${formData.value.length}${allDataCount}条`;
  });
  const selectedString = computed(() => {
    if (isFunction(props.showSelectCount)) {
      return props.showSelectCount(formData.value?.length || 0);
    }

    return `已选数据：`;
  });
  watch(
    () => props?.filter,
    (value) => {
      if (!value) return;
      const { defaultFormData } = value;
      if (defaultFormData) {
        filterData.value = cloneDeep(defaultFormData);
      }
    },
    { immediate: true }
  );

  const query = () => {
    tableRef.value.refreshTable();
  };
  const reset = async () => {
    tableRef.value.handlePageReset();
    filterData.value = cloneDeep(props.filter.defaultFormData || {});
    await nextTick();
    tableRef.value.refreshTable();
  };

  function setFilter(val) {
    Object.keys(val).forEach((key) => {
      filterData.value[key] = val[key];
    });
  }
  defineExpose({
    tableRef,
    formData,
    refreshTable: () => tableRef.value?.refreshTable(),
    setFilter,
  });
</script>

<style scoped lang="less">
  .container {
    height: 100%;
    display: flex;
    overflow: auto;
  }

  .source {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .table {
    flex: 1;
    overflow: hidden;
  }

  .option {
    margin-bottom: 10px;
  }

  .target {
    margin-left: 20px;
    max-height: 100%;
    min-width: 150px;
    padding: 8px;
    overflow-y: auto;
    flex: none;
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 4px;
    border: 1px solid var(--color-border);
  }

  .tag {
    flex: none;
  }

  .search-btn,
  .reset-btn {
    margin-bottom: 16px;
    margin-right: 16px;
  }

  .selected {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
