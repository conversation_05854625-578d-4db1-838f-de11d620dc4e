const fs = require('fs');
const path = require('path');
const { OUT_PUT_DIR, COMMON_FILES_LIST } = require('../constant.js');

function generateCommonFiles() {
    COMMON_FILES_LIST.forEach(file => {
        const sourcePath = path.join(__dirname,'../', file.src); // 源文件路径
        const destinationPath = path.join(OUT_PUT_DIR, file.dest); // 目标文件路径
        fs.cpSync(sourcePath, destinationPath, { recursive: true }); // 复制文件夹
    });
}

module.exports = { generateCommonFiles }