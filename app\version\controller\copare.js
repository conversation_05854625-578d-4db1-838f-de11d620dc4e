const path = require('path');
const fs = require('fs');
const { getPreVersion } = require('../service/version');

function getAppVersionFileList(req, res) {
  const { name, packageName, version, brand } = req.query;

  const decodeFilePath = path.resolve(__dirname, '../rpks/decode', `${name}-${version}-${brand}`);

  if (!fs.existsSync(decodeFilePath)) {
    res.status(404).json({ message: '文件不存在' });
    return;
  }

  res.json({ version, brand, list: generateTree(decodeFilePath) });
}

function getFileContent(req, res) {
  const { name, packageName, version, brand, key } = req.query;

  const decodeFilePath = path.resolve(__dirname, '../rpks/decode', `${name}-${version}-${brand}`, key);

  if (!fs.existsSync(decodeFilePath)) {
    res.status(404).json({ message: '文件不存在' });
    return;
  }
  const stats = fs.statSync(decodeFilePath);
  if (stats.isDirectory()) {
    res.status(400).json({ message: '请求的是文件夹路径' });
    return;
  }

  fs.createReadStream(decodeFilePath).pipe(res);
}

async function getPreVersionContent(req, res) {
  const { name, brand, key, releaseDate } = req.query;
  const preVersionIns = await getPreVersion({ name, brand, releaseDate });

  if (!preVersionIns) {
    res.status(404).json({ message: '没有找到上一个版本' });
    return;
  }

  const { name: preName, version: preVersion, brand: preBrand } = preVersionIns;

  const decodeFilePath = path.resolve(__dirname, '../rpks/decode', `${preName}-${preVersion}-${preBrand}`, key);

  if (!fs.existsSync(decodeFilePath)) {
    res.status(404).json({ message: '对比文件不存在' });
    return;
  }
  const stats = fs.statSync(decodeFilePath);
  if (stats.isDirectory()) {
    res.status(400).json({ message: '请求的是文件夹路径' });
    return;
  }

  fs.createReadStream(decodeFilePath).pipe(res);
}

// 递归函数，用于生成树形结构
function generateTree(dir, baseDir = dir, parentKey = '') {
  const items = fs.readdirSync(dir);
  const tree = [];

  items.forEach((item) => {
    const fullPath = path.join(dir, item);
    const relativePath = path.relative(baseDir, fullPath); // 获取相对路径
    const stats = fs.statSync(fullPath);

    if (stats.isDirectory()) {
      // 如果是文件夹，递归处理
      tree.push({
        title: item,
        key: relativePath, // 使用相对路径作为 key
        children: generateTree(fullPath, baseDir, relativePath),
      });
    } else {
      // 如果是文件，直接添加到树中
      tree.push({
        title: item,
        key: relativePath, // 使用相对路径作为 key
      });
    }
  });

  return tree;
}

module.exports = {
  getAppVersionFileList,
  getFileContent,
  getPreVersionContent,
};
