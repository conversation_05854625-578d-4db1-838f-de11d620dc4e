<template>
  <div class="material-panel">
    <a-tabs :animation="true" v-model:active-key="activeTab">
      <a-tab-pane v-for="tab in materialTabs" :key="tab.key" :title="tab.title">
        <div class="search-box">
          <a-input v-model="searchKeyword" placeholder="搜索组件" allow-clear>
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </div>
        <div class="material-list">
          <draggable
            :list="currentMaterialList"
            :sort="false"
            :clone="handleClone"
            :group="{ name: 'components', pull: 'clone', put: false }"
            item-key="id"
          >
            <template #item="{ element }">
              <div
                class="component-item"
                v-show="!searchKeyword || isMatch(element, searchKeyword)"
              >
                <div class="component-icon">
                  <icon-apps v-if="!element.thumbnail" />
                  <a-popover title="" v-else>
                    <img
                      class="comp-thumbnail"
                      :src="element.thumbnail"
                      alt=""
                    />
                    <template #content>
                      <img
                        style="width: 200px; height: auto"
                        :src="element.thumbnail"
                        alt=""
                      />
                    </template>
                  </a-popover>
                </div>
                <div class="component-name">{{ element.label }}</div>
              </div>
            </template>
          </draggable>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { IconSearch, IconApps } from '@arco-design/web-vue/es/icon';
  import draggable from 'vuedraggable';
  import { materialTabs } from '../../config/components';
  import { isMatch } from '../../utils/index';

  const searchKeyword = ref('');

  // 当前激活的标签页
  const activeTab = ref(materialTabs[0]?.key);

  // 当前显示的组件列表
  const currentMaterialList = computed(() => {
    const currentTab = materialTabs.find((tab) => tab.key === activeTab.value);
    return currentTab ? currentTab.list : [];
  });

  // 处理组件克隆
  function handleClone(component) {
    return {
      ...component,
      id: `${component.id}_${Date.now()}`,
      props: { ...component.props },
      styles: { ...component.styles },
    };
  }
</script>

<style scoped>
  :deep(.arco-tabs-content) {
    padding-top: 8px;
  }

  .material-panel {
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
  }

  .search-box {
    padding: 0 8px 8px;
  }

  .material-list {
    padding: 0 8px;
  }

  .comp-thumbnail {
    width: 30px;
    height: 30px;
  }

  .component-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #f7f8fa;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s;
  }

  .component-item:hover {
    background: #e8f3ff;
    border-color: rgb(var(--primary-6));
  }

  .component-icon {
    font-size: 20px;
    margin-right: 8px;
    color: rgb(var(--primary-6));
  }

  .component-name {
    font-size: 14px;
    color: #1d2129;
  }
</style>
