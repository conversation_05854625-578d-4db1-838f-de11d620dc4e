const mongoose = require('mongoose');

const versionSchema = new mongoose.Schema(
  {
    // 版本名称
    name: {
      type: String,
      required: true,
    },
    // 版本号
    version: {
      type: String,
      required: true,
    },
    // app 下载地址
    appLink: {
      type: String,
      required: true,
    },
    // 产商
    brand: {
      type: String,
      required: true,
    },
    // 上线日期
    releaseDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
  },
  {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
  }
);

// 创建版本模型
const Version = mongoose.model('AppVersion', versionSchema);

module.exports = Version;
