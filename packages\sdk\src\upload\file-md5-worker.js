import { get } from 'lodash';
import SparkMD5 from 'spark-md5';

onmessage = (event) => {
  const file = get(event, 'data.file');
  if (!(file instanceof File)) return;

  readAsArrayBuffer(file).then((buffer) => {
    const spark = new SparkMD5.ArrayBuffer();
    spark.append(buffer);
    const md5 = spark.end();
    postMessage({ md5 });
  });
};

function readAsArrayBuffer(file) {
  const reader = new FileReader();
  return new Promise((resolve, reject) => {
    reader.onload = (event) => {
      resolve(event.target.result);
    };
    reader.onerror = (event) => {
      reject(event);
    };
    reader.readAsArrayBuffer(file);
  });
}

function readAsBinaryString(file) {
  const reader = new FileReader();
  return new Promise((resolve, reject) => {
    reader.onload = (event) => {
      resolve(event.target.result);
    };
    reader.onerror = (event) => {
      reject(event);
    };
    reader.readAsBinaryString(file);
  });
}

export default null;
