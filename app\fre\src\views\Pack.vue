<script setup lang="ts">
  import { ref, toRaw, onMounted, watch, computed, unref } from 'vue';
  import Dialog from '@/components/Dialog/index.vue';
  import PkgList from '@/components/PkgList/index.vue';
  import type { Key, AppInfo, PakInfo, Message, FormInfo, PackResult } from '@/types/global';
  import { getAppList, submitPack } from '@/api';
  import { message as MessageDialog } from 'ant-design-vue';
  import { areBrandsIdentical } from '@/utils';

  const columns = [
    {
      title: '应用名',
      dataIndex: 'appName',
      align: 'center',
    },
    {
      title: '厂商',
      dataIndex: 'brand',
      align: 'center',
    },
    {
      title: '应用版本',
      dataIndex: 'appVersion',
      align: 'center',
    },
    {
      title: 'SDK版本',
      dataIndex: 'sdkVersion',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'operate',
      align: 'center',
    },
  ];

  let appList = ref<AppInfo[]>([]);
  // 选择的行：应用信息
  const selectedAppList = ref<AppInfo[]>([]);
  const selectedAppKeys = ref<Number[]>([]);

  const onSelectChange = (changableRowKeys: Number[], selectedRows: AppInfo[]) => {
    console.log('selectedRowKeys changed: ', changableRowKeys, selectedRows);
    if (areBrandsIdentical(selectedRows, 'sdkVersion')) {
      selectedAppList.value = selectedRows;
      selectedAppKeys.value = changableRowKeys;
    } else {
      MessageDialog.error('SDK版本不一致');
    }
  };
  const rowSelection = computed(() => {
    return {
      selectedRowKeys: unref(selectedAppKeys),
      onChange: onSelectChange,
    };
  });

  // 操作按钮处理
  const onSubmit = (index: number) => {
    if (selectedAppList.value.length === 0) {
      selectedAppList.value = [appList.value[index]];
    }
    dialogOperate.value = true;
  };

  // 打包弹窗处理
  const dialogOperate = ref(false);
  const handleDialog = async (formState: FormInfo[], clickState = false) => {
    dialogOperate.value = false;
    if (!clickState) return;
    const { status, result }: { status: boolean; result: PackResult } = await submitPack(formState);
    // const status = true
    // const result = {
    //   taskId: '123',
    //   resultList: [
    //     { appName: '1', reason: '@', type: 'fail', key: '1' },
    //     { appName: '测试应用', type: 'success', key: '2' },
    //   ],
    // }
    console.log('提交打包信息结果', status, result);
    if (status) {
      // 提交成功
      MessageDialog.success(result.taskId);
      selectedAppList.value = [];
      selectedAppKeys.value = [];
      const successList = result.resultList.filter((item) => item.type === 'success').map((item) => item.key);
      console.log('successList', successList, formState);
      let newPak = formState.filter((item) => item.key !== undefined && successList.includes(item.key));
      console.log('newPak', newPak);
      pkgList.value = [...pkgList.value, ...newPak];
      return;
    }
    if (result) {
      // MessageDialog.info(result.message)
    }
  };

  onMounted(async () => {
    appList.value = [
      { key: 1, appName: '测试应用', brand: 'xiaomi', appVersion: '1.0.0', sdkVersion: '1.0.0' },
      { key: '2', appName: '测试应用2', brand: 'xiaomi', appVersion: '1.0.0', sdkVersion: '1.0.0' },
      { key: 3, appName: '测试应用3', brand: 'vivo', appVersion: '1.0.1', sdkVersion: '1.0.0' },
    ];
    ({ appList: appList.value } = await getAppList());
    console.log('appList', appList.value);
  });

  import { useWebSocket } from '../utils/useWebSocket.ts';
  const { messages, sendMessage, isConnected } = useWebSocket('/ws', {
    reconnectInterval: 1000,
    maxReconnectAttempts: 1,
  });
  // 监听 messages 变化
  watch(messages, (newMessages: any[]) => {
    const latestMessage = newMessages.at(-1); // 获取最新的消息
    if (!latestMessage || latestMessage.type !== 'taskComplete') return; // 如果消息不存在或类型不匹配，直接返回

    for (let appKey in latestMessage.data) {
      const target = pkgList.value.find((item) => item.key === appKey); // 查找目标项
      if (target) {
        target.downloadUrl = latestMessage.data[appKey]; // 更新 downloadUrl
      }
    }
  });

  const pkgList = ref<PakInfo[]>([
    {
      appName: '宝宝砍价',
      brand: 'xiaomi',
      taskId: '123',
      key: 'com.baobao.home_xm',
      appVersion: '1.0.0',
      sdkVersion: '1.0.0',
      downloadUrl: [],
    },
  ]);

  const sendMessageHandler = (message: string) => {
    if (isConnected.value) {
      sendMessage(message);
    } else {
      console.error('WebSocket 未连接');
    }
  };
</script>

<template>
  <div class="card w-1/2">
    <a-table
      :row-selection="rowSelection"
      :columns="columns"
      :data-source="appList"
      :pagination="false"
      class="w-full h-full"
      :style="{ backgroundColor: 'transparent' }"
    >
      <template #title><div class="font-bold text-center text-lg"> 应用信息 </div></template>
      <template #bodyCell="{ column, index }">
        <template v-if="column.dataIndex === 'operate'">
          <a-button @click="onSubmit(index)">操作</a-button>
        </template>
      </template>
    </a-table>
  </div>

  <PkgList :pkgList="pkgList" />
  <Dialog v-if="dialogOperate" @buttonClick="handleDialog" :selectedData="selectedAppList" />
</template>

<style lang="less">
  .card {
    height: 370px;
    overflow-x: hidden;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(1px);
  }

  // 表单样式更改
  .card {
    :where(.ant-table-wrapper) .ant-table-thead > tr > th {
      background-color: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(1px);
    }
    // :where(.ant-table-wrapper) .ant-table-tbody > tr:hover {
    //   background-color: red;
    // }
  }
</style>
