<template>
  <div style="padding: 16px; background-color: #f5f5f5; height: 100%">
    <DynamicPage
      ref="pageRef"
      :operation="operation"
      :table="tableJson"
      :auto-load="true"
      :batch-operation="batchOperations"
      :filter="filterJson"
      row-key="_id"
    >
    </DynamicPage>
  </div>
</template>

<script lang="ts" setup>
  import { useUserStore } from '@/store'; 
  import { DButton } from '@repo/kola';
  import { watch, onMounted, ref } from 'vue';
  import {
    getAppList,
    getSdkVersion,
    submitPack,
    getPackResult,
    submitUpgrade,
    deletePack,
  } from '@/api/auto-pack';
  import useWebSocket from '@/utils/useWebSocket';
  import tableJson from './config/table';
  import getFilter from './config/filter';

  // import operation from './config/operation'

  interface AppList {
    label: string;
    value: string;
    key: string;
  }

  interface AppListOrigin {
    pkgName: string;
    versionName: string;
    versionCode: number;
    key: string;
  }

  const { messages, sendMessage, isConnected } = useWebSocket('/ws', {
    reconnectInterval: 1000,
    maxReconnectAttempts: 1,
  });

  const { role } = useUserStore();
  const appList = ref<AppList[]>([]);
  const resultList = ref<any[]>([]);
  const appListOrigin = ref<AppListOrigin[]>([]);
  const filterJson = getFilter();

  const getKey = (appName: string) => {
    return appListOrigin.value.find(
      (item) => item.pkgName === appName 
    )?.key;
  };
  const pageRef = ref();
  const refreshTables = () => {
    pageRef.value?.refreshTable();
  };

  const operation: DButton[] = [
    {
      text: '打包',
      props: {
        type: 'primary',
        disabled: false,
        loading: false,
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '创建任务',
          width: 500,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                name: 'appName',
                label: '产品名称',
                placeholder: '请选择产品名称',
                type: 'select',
                format: 'multipleSelect',
                source: {
                  data: () => {
                    return appList.value;
                  },
                },
                rules: [{ required: true, message: '请选择产品' }],
              },
              {
                name: 'brand',
                label: '厂商',
                placeholder: '请选择厂商',
                type: 'select',
                format: 'singleSelect',
                source: {
                  data: [
                    { label: '华为', value: 'hw' },
                    { label: '小米', value: 'xiaomi' },
                    { label: 'oppo', value: 'oppo' },
                    { label: 'vivo', value: 'vivo' },
                    { label: '荣耀', value: 'honor' },
                  ],
                },
                rules: [{ required: true, message: '请选择厂商' }],
              },
              {
                name: 'sdkVersion',
                label: 'sdk版本号',
                placeholder: '请选择sdk版本',
                type: 'select',
                format: 'singleSelect',
                select:{
                  allowSearch: true,
                  allowClear: true
                },
                source: {
                  data: () => {
                    return getSdkVersion();
                  },
                },
                rules: [{ required: true, message: '请选择sdk版本号' }],
              },
              {
                name: 'operations',
                label: '操作',
                type: 'select',
                format: 'checkbox',
                source: {
                  data: [
                    { label: '构建测试包', value: 'dev' },
                    { label: '构建线上包', value: 'release-log' },
                    { label: '构建提审包', value: 'release' },
                  ],
                },
                rules: [{ required: true, message: '请选择操作' }],
              },
            ],
          },
          defaultFormData: {
            appName: [],
            brand: '',
            sdkVersion: '',
            operations: [],
          },
        },
        action: (value: any) => {
          const params = value.formData.appName
            .filter((appName: string) => appName)
            .map((appName: string) => {
              return {
                appName,
                brand: value.formData.brand,
                sdkVersion: value.formData.sdkVersion,
                key: getKey(appName),
                pkgType: value.formData.operations,
                timeStamp: new Date().getTime(),
              };
            });
          submitPack(params).then((res) => {
            refreshTables();
          });
        },
      },
    },
    
    {
      clickActionType: 'batch',
      action: (value: any) => {
        console.log('value', value);
      },
    },
  ];

  const upgradeOperation = {
      text: '升级版本号',
      props: {
        type: 'primary',
        disabled: false,
        loading: false,
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '升级版本号',
          width: 500,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                name: 'appName',
                label: '产品名称',
                placeholder: '请选择产品名称',
                type: 'select',
                format: 'multipleSelect',
                source: {
                  data: () => {
                    return appList.value;
                  },
                },
                rules: [{ required: true, message: '请选择产品' }],
              },
              {
                name: 'branch',
                label: '分支',
                placeholder: '请选择分支',
                type: 'select',
                format: 'singleSelect',
                source: {
                  data: [
                    { label: 'develop', value: 'develop' },
                    { label: 'master', value: 'master' },
                  ],
                },
                rules: [{ required: true, message: '请选择分支' }],
              },
              {
                name: 'brand',
                label: '厂商',
                placeholder: '请选择厂商',
                type: 'select',
                format: 'singleSelect',
                source: {
                  data: [
                    { label: '华为', value: 'hw' },
                    { label: '小米', value: 'xiaomi' },
                    { label: 'oppo', value: 'oppo' },
                    { label: 'vivo', value: 'vivo' },
                    { label: '荣耀', value: 'honor' },
                  ],
                },
                rules: [{ required: true, message: '请选择厂商' }],
              },
              {
                name: 'operations',
                label: '操作',
                type: 'select',
                format: 'singleSelect',
                source: {
                  data: [
                    { label: '加一个版本', value: 'upgrade' },
                    { label: '设置版本', value: 'setup' },
                  ],
                },
              },
              {
                name: 'version',
                label: '版本号',
                placeholder: '请输入版本号',
                type: 'text',
                rules: [{ required: true, message: '请输入版本号' }],
                visibleOn: (value: any, formData: any) => {
                  return formData.operations === 'setup';
                },
              },
            ],
          },
          defaultFormData: {
            appName: [],
            brand: '',
          },
        },
        action: (value: any) => {
          const params = value.formData.appName
            .filter((appName: string) => appName)
            .map((appName: string) => {
              return {
                appName,
                brand: value.formData.brand,
                sdkVersion: value.formData.sdkVersion,
                key: getKey(appName),
                operationType: value.formData.operations,
                timeStamp: new Date().getTime(),
                version: value.formData.version,
                branch: value.formData.branch,
              };
            });
          submitUpgrade(params).then((res) => {
            // refreshTables();
          });
        },
      },
    }

  if (['FRE','admin'].includes(role)){
    operation.push(upgradeOperation)
  }

  const batchOperations = ref([
    {
      text: '删除',
      props: {
        type: 'outline',
        disabled: false,
        loading: false,
      },
      clickActionType: 'modal',
      operationPosition: 'batch',
      modal: {
        props: {
          title: '提示',
          width: 500,
          fullscreen: false,
        },
        contentType: 'text',
        text: '是否确认删除所选数据？',
        action: async ({ record, refreshTable }: any) => {
          console.log('record', record);
          deletePack({ ids: record }).then((res) => {
            console.log('res', res);
            refreshTable();
          });
        },
      },
    },
  ]);

  const delay = (ms: number) => {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  };

  watch(messages, (newMessages: any[]) => {
    const latestMessage = newMessages.at(-1); // 获取最新的消息
    if (!latestMessage || latestMessage.type !== 'taskComplete') return; // 如果消息不存在或类型不匹配，直接返回
    // getTaskList()
    // combineRowSpanFn('taskId')
    // 存储
    // localStorage.setItem('resultList', JSON.stringify(resultList.value))
    console.log('latestMessage', latestMessage);
    pageRef.value?.refreshTable();
    // pageRef.value?.refreshTable()
  });

  onMounted(() => {
    getAppList().then((res) => {
      appList.value = res.data;
      appListOrigin.value = res.originData;
    });
  });
</script>

<style lang="less"></style>
