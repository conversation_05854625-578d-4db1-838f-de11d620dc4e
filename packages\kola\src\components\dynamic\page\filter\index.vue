<template>
  <DynamicForm
    :form-schema="formSchema"
    v-model="filter"
    :reset-form-data="props.resetFormData"
    layout="inline"
    ref="formRef"
    use-responsive
  >
    <template #footer>
      <div class="search-footer">
        <a-button type="primary" @click="query" class="search-btn">
          <template #icon>
            <icon-search />
          </template>
          查询
        </a-button>
        <a-button @click="reset" class="reset-btn">
          <template #icon>
            <icon-refresh />
          </template>
          重置
        </a-button>
        <a-button @click="handleCollapse" type="text" v-if="showCollapseBtn">
          {{ collapsed ? '展开' : '收起' }}
          <icon-down v-if="collapsed" />
          <icon-up v-else />
        </a-button>
      </div>
    </template>
  </DynamicForm>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted, nextTick, onUnmounted } from 'vue';
  import { cloneDeep } from 'lodash';
  import DynamicForm from '../../form/index.vue';
  import { DForm } from '../../types/form';
  import useDispatch from '../../hooks/dispatch';

  defineOptions({ name: 'DynamicFilter' });

  const props = defineProps<{
    formSchema: DForm['formSchema'];
    resetFormData?: DForm['resetFormData'];
    onSearch?: any;
    onReset?: any;
  }>();
  const filter = defineModel<Record<string, any>>({ default: {} });

  const formSchema = computed(() => {
    const clonedFormSchema = cloneDeep(props.formSchema);

    if (firstRowLength.value === 0) return clonedFormSchema;

    if (collapsed.value) {
      clonedFormSchema.fields = clonedFormSchema.fields.map((item, index) => {
        if (index < firstRowLength.value - 1) {
          return {
            ...item,
            hidden: false,
          };
        }
        return {
          ...item,
          hidden: true,
        };
      });
    }
    return clonedFormSchema;
  });

  const formRef = ref();
  const collapsed = ref(false);
  const firstRowLength = ref(0); // 记录第一行有多少个字段，- 1 后即可显示查询按钮

  const showCollapseBtn = computed(() => {
    return props.formSchema.fields.length > firstRowLength.value - 1;
  });

  const dispatch = useDispatch();
  const handleCollapse = () => {
    collapsed.value = !collapsed.value;
  };

  const query = () => {
    dispatch({
      type: 'refreshTable',
      payload: { resetPagination: true, resetPageSize: true },
    });
    props.onSearch?.();
  };

  const reset = () => {
    formRef.value?.reset();
    dispatch({
      type: 'refreshTable',
      payload: { resetPagination: true },
    });
    props.onReset?.();
  };

  const getFilterRows = () => {
    const windowWidth = window.innerWidth;
    if (windowWidth > 1920) {
      firstRowLength.value = 5;
    } else if (windowWidth > 1440) {
      firstRowLength.value = 4;
    } else if (windowWidth > 1280) {
      firstRowLength.value = 3;
    } else {
      firstRowLength.value = 2;
    }
  };

  onMounted(async () => {
    await nextTick();
    getFilterRows();
    window.addEventListener('resize', getFilterRows);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', getFilterRows);
  });
</script>

<style scoped lang="less">
  .search-btn,
  .reset-btn {
    margin-right: 16px;
  }

  .search-footer {
    flex: 1;
    text-align: end;
    height: 48px;
  }
</style>
