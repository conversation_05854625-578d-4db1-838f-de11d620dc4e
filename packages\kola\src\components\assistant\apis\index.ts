import { WebSocketManager, API } from '../utils/websocket';
import { ENV_CONFIG } from '../constants';

// @ts-ignore
const env = process.env.VITE_MODE || 'testing';
// WebSocket管理实例
const wsManager = WebSocketManager.getInstance(
  // @ts-ignore
  ENV_CONFIG[env].WS_ULR
);

// 创建WebSocket API
const websocketApi: API = {
  connect(requestId: string, platformId: string, token: string) {
    return wsManager.connect(requestId, platformId, token);
  },
  send(data: any) {
    wsManager.sendMessage(data);
  },
  on(type: string, callback: (data: any) => void) {
    wsManager.onMessage(type, callback);
  },
  off(type: string, callback: (data: any) => void) {
    wsManager.offMessage(type, callback);
  },
  close() {
    wsManager.close();
  },
};

export default websocketApi;
