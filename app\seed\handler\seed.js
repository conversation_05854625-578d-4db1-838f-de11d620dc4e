const TaskQueue = require('./taskQueue');
const { Worker } = require('worker_threads');
const path = require('path')

function addTask(task) {
  TaskQueue.addTask(() => {
    return new Promise((resolve, reject) => {
      try {
        const workerPath = path.join(__dirname, './worker.js')
        const worker = new Worker(workerPath);
        worker.postMessage(JSON.stringify(task));
        worker.on('message', (message) => {
          if (message.type === 'success') {
            resolve(message);
          } else if (message.type === 'error') {
            reject(message);
          }
        });
      } catch (err) {
        console.log('worker-err', err)
      }
    });
  });
}

module.exports = { addTask };