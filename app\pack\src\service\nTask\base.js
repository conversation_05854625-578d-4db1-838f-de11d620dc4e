const { TASK_STATUS } = require('../../constant');
const { timeFormat } = require('../../utils/tools');
class Task {
  constructor() {
    this.taskId = null;
    this.status = TASK_STATUS.WAITING;
    this.brand = null;
    this.appName = null;
    this.sdkVersion = null;
    this.result = null;
    this.timeCost = null;
    this.key = null;
    this.pkgType = null;
    this.timeStamp = null;
    this.version = null;
    this.failedResult = null;
    this.errorCode = null;
    this.id = null;
  }

  init(params, taskId, role) {
    this.taskId = taskId;
    this.brand = params.brand;
    this.appName = params.appName;
    this.sdkVersion = params.sdkVersion;
    this.key = params.key;
    this.pkgType = params.pkgType;
    this.timeStamp = timeFormat(new Date().getTime());
    this.version = params.version;
    this.username = role.username;   //添加用户名
  }
}

module.exports = Task;
