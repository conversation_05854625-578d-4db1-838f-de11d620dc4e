import { nextTick } from 'vue';

function useScrollTarget(options = {}) {
  // eslint-disable-next-line consistent-return
  function scrollTarget(el, targetClass = '.arco-form-item-error', scrollOptions = {}) {
    if (!el) {
      console.log('el is null  未查询到目标元素');
      return null;
    }
    nextTick(() => {
      const targetNode = el.querySelector(targetClass);
      if (targetNode) {
        targetNode.scrollIntoView({
          block: 'center',
          behavior: 'smooth',
          ...options,
          ...scrollOptions,
        });
      }
    });
  }

  return {
    scrollTarget,
  };
}

export default useScrollTarget;
