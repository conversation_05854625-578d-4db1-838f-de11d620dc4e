import pinyin from 'pinyin';
import { customAlphabet } from 'nanoid';


interface Component {
  id: string;
  name: string;
  label: string;
  pinyin: string;
  props?: Record<string, any>;
  styles?: Record<string, any>;
}

const generateTabId = customAlphabet('1234567890ABCDEF', 8);



// 组件默认样式配置
const defaultStyles = {
  width: { value: '100%', type: 'input' },
  height: { value: 'auto', type: 'input' },
  margin: { value: '0', type: 'input' },
  padding: { value: '0', type: 'input' },
  backgroundColor: { value: '#ffffff', type: 'input' },
  borderRadius: { value: '4px', type: 'input' }
};

export { generateTabId };

// 克隆组件
export function cloneComponent(component: Component) {
  return {
    ...component,
    id: generateTabId(),
    props: { ...component.props },
    styles: { ...component.styles }
  };
}

// 复制到剪贴板
export function copyToClipboard(text: string) {
  return navigator.clipboard.writeText(text);
}

// 检查是否匹配（支持中文、拼音、拼音首字母搜索）
export function isMatch(element: Component, keyword: string) {
  if (!keyword) return true;
  keyword = keyword.toLowerCase();
  
  // 检查原始标签
  if (element.label.toLowerCase().includes(keyword)) return true;
  
  // 检查完整拼音
  if (element.pinyin.toLowerCase().includes(keyword)) return true;
  
  // 检查拼音首字母
  const initials = pinyin(element.label, { style: pinyin.STYLE_FIRST_LETTER }).join('');
  if (initials.toLowerCase().includes(keyword)) return true;
  
  return false;
}

// 转换应用数据格式
export function transformAppData(input: any) {
  // 构建 meta 对象
  const meta = {
    name: input.name || '',
    package: input.packageName || '',
    logoUrl: input.icon || '',
    tabs: input.main.map((page: any, index: number) => ({
      name: page.pageName || '',
      pageName: `Tab${index + 1}`,
      icon: '',
      activeIcon: ''
    })),
    pages: input.subPages.map((page: any) => ({
      name: page.pageName || '',
      pageName: page.pageId || '', 
      path: page.path || `pages/${page.pageId}`
    })),
    agreementUrl: input.agreementUrl || '',
    privacyUrl: input.privacyUrl || ''
  };
  // 构建 pages 数组
  const pages = [
    // main 页面对应的 tabs
    ...input.main.map((page: any, index: number) => ({
      meta: {
        name: page.pageName || '',
        pageName: `Tab${index + 1}`,
        path: `pages/Main?activeTab=${index}`
      },
      components: page.components || []
    })),
    // subPages 页面
    ...input.subPages.map((page: any) => ({
      meta: {
        name: page.pageName || '',
        pageName: page.pageId || '',
        path: page.path || `pages/${page.pageId}`
      },
      components: page.components || []
    }))
  ];
  return {
    meta,
    pages
  };
}


