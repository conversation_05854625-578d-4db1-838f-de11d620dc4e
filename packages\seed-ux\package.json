{"name": "component-<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "component liarbry", "scripts": {"dev": "hap server --watch", "server": "hap server", "build": "hap build", "release": "hap release", "watch": "hap watch", "debug": "hap debug", "gen": "node ./scripts/gen/index.js", "precommit-msg": "echo '🚧 start pre-commit checks ...' && exit 0", "prettier": "node ./scripts/selfCloseInputTag.js && prettier --write \"src/**/*.{js,ux,less,scss,css}\"", "prettier-watcher": "onchange '**/*.md' \"src/**/**/*.{js,ux,less,scss,css}\" -- prettier --write changed"}, "bin": {"convert": "bin/index.js"}, "dependencies": {"commander": "^14.0.0"}, "devDependencies": {"@babel/runtime": "^7.12.5", "husky": "^4.3.0", "lint-staged": "^10.5.1", "onchange": "^5.2.0", "prettier": "^1.15.3", "prettier-plugin-ux": "^0.3.0", "colors": "^1.4.0", "hap-toolkit": "^2.0.1"}, "prettier": {"singleQuote": true, "semi": false, "printWidth": 80, "proseWrap": "never", "tabWidth": 2}, "husky": {"hooks": {"pre-commit": "yarn run precommit-msg && lint-staged"}}, "lint-staged": {"**/**.{ux,js,json,pcss,md}": ["prettier --write", "git add"]}, "keywords": ["快应用", "快应用示例", "快应用模版"], "browserslist": ["chrome 65"]}