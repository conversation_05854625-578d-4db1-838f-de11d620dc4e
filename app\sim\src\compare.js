const fs = require("fs");

/**
 * 计算两个文件的行相似度
 * @param {string} file1Path - 文件1路径
 * @param {string} file2Path - 文件2路径
 */
function compareFiles(file1Path, file2Path) {
  // 读取文件内容
  const file1Lines = fs.readFileSync(file1Path, "utf8").split("\n").map(line => line.trim());
  const file2Lines = fs.readFileSync(file2Path, "utf8").split("\n").map(line => line.trim());
  // console.log(file1Lines.length, file2Lines.length)
  // 找到相同行
  const totalLines = file1Lines.length + file2Lines.length
  const matchingLines = file1Lines.filter(line => {
    const index = file2Lines.indexOf(line);
    if (index === -1) {
      return false;
    }
    file2Lines.splice(index, 1);
    return true

    // return file2Lines.includes(line)
  }).length;
  // console.log('totalLines', totalLines)
  // 计算相似度
  const similarity = (matchingLines  * 2 / totalLines) * 100;

  // console.log(`File1 Total blocks: ${file1Lines.length}`);
  // console.log(`File2 Total blocks: ${file2Lines.length}`);
  // console.log(`Matching blocks: ${matchingLines}`);
  // console.log(`Similarity: ${similarity.toFixed(2)}%`);

  return {
    similarity,
    file1Path,
    file2Path
  }
}

module.exports = compareFiles
