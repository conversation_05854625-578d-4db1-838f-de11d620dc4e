<template>
  <div
    :class="{
      'edit-cell': true,
      'disabled': curBatchStatus || !hasEditAuth || isDisabled,
    }"
    @mouseenter="changeEditIconVisible(true)"
    @mouseleave="changeEditIconVisible(false)"
  >
    <template v-if="props.component">
      <component :is="props.component" :row-data="props.record" class="ellipsis" />
      <icon-edit v-if="canEdit" @click="handleOpen" class="edit-icon" />
    </template>
    <template v-else-if="props.contentType === 'text'">
      <a-spin :loading="loading" class="flex">
        <a-tooltip>
          <template #content>
            {{ renderData(props.record[props.column.dataIndex]) }}
          </template>
          <div class="ellipsis">{{ renderData(props.record[props.column.dataIndex]) }} </div>
        </a-tooltip>
        <icon-edit v-if="canEdit" @click="handleOpen" class="edit-icon" />
      </a-spin>
    </template>
    <template v-else-if="props.contentType === 'image'">
      <template v-if="props.record[props.column.dataIndex]">
        <a-image width="60" height="60" :src="props.record[props.column.dataIndex]" />
        <div class="upload-text reupload-text" v-if="canEdit" @click="handleOpen">重新上传 </div>
      </template>
      <span v-else-if="hasEditAuth" class="upload-text" @click="handleOpen">上传头像</span>
    </template>
  </div>
  <a-modal
    :key="modalKey"
    :visible="modalVisible"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="props.modalWidth || 600"
    :closable="false"
    :title="props.modalTitle"
    :ok-loading="okLoading"
    @close="modalClose"
  >
    <DynamicForm ref="formRef" :form-schema="props.formSchema" v-model="formData" />
  </a-modal>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { computed, defineProps, ref } from 'vue';
  import { isFunction, isNumber, uniqueId } from 'lodash';
  import useBatch from '../../../hooks/batch';
  import useRefreshTable from '../../../hooks/refresh-table';
  import { FormSchema } from '../../../types/form';
  import { RenderProps } from '../type';

  const props = withDefaults(
    defineProps<
      RenderProps & {
        modalTitle: string;
        action: any;
        formSchema: FormSchema;
        defaultFormData: any;
        contentType?: 'image' | 'text';
        component?: any;
        alert?: {
          show: (record: Record<string, any>) => boolean;
          message: string;
        };
        modalWidth?: number;
        render?: (val: any) => string;
        auth?: string;
        isDisabled?: boolean;
        successCallback?: (data) => void;
      }
    >(),
    {
      contentType: 'text',
      isDisabled: false,
    }
  );
  const { curBatchStatus } = useBatch();

  function getModalKey() {
    return uniqueId('modalKey_');
  }

  const modalKey = ref(getModalKey());
  const hasEditAuth = true;
  const changeEditIconVisible = (val: boolean) => {
    if (props.isDisabled) {
      return;
    }
    if (!hasEditAuth) {
      return;
    }
    if (!curBatchStatus.value) {
      editIconVisible.value = val;
    }
  };

  const formRef = ref<any>();
  const formData = ref({});
  const modalVisible = ref(false);
  const editIconVisible = ref(false);
  const loading = ref(false);
  const okLoading = ref(false);
  const refreshTable = useRefreshTable();
  const handleOk = async () => {
    try {
      okLoading.value = true;
      if (formRef.value) {
        await formRef.value.validate();
      }
      const res = await props.action(formData.value, props.record);
      if (isFunction(props.successCallback)) {
        await props.successCallback({
          res,
          formData: formData.value,
          record: props.record,
          closeModal: handleCancel,
          refreshTable,
        });
      } else {
        Message.success('操作成功');
        modalVisible.value = false;
        refreshTable();
      }
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log('e', e);
    } finally {
      okLoading.value = false;
    }
  };
  const handleCancel = () => {
    modalVisible.value = false;
  };

  const handleOpen = async () => {
    if (curBatchStatus.value) {
      return;
    }
    if (props.alert) {
      loading.value = true;
      const show = await props.alert?.show(props.record);
      loading.value = false;
      if (show) {
        Message.warning(props.alert?.message ?? '');
        return;
      }
    }
    editIconVisible.value = false;
    try {
      loading.value = true;
      formData.value = await getDefaultFormData();
      modalVisible.value = true;
    } catch (e) {
      console.log('edit-cell error', e);
    } finally {
      loading.value = false;
    }
  };

  async function getDefaultFormData() {
    if (isFunction(props.defaultFormData)) {
      const resVal = await props.defaultFormData(props.record);
      return resVal;
    }
    return props.defaultFormData;
  }

  const renderData = (val) => {
    if (props.render) {
      return props.render(val);
    }
    return val ?? '-';
  };

  const canEdit = computed(() => hasEditAuth && !props.isDisabled && editIconVisible.value);

  function modalClose() {
    modalKey.value = getModalKey();
  }
</script>

<style lang="less" scoped>
  .edit-cell {
    cursor: pointer;
    position: relative;

    &.disabled {
      cursor: default;
    }
  }

  .flex {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .ellipsis {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 10px;
  }

  .edit-icon {
    position: absolute;
    top: 50%;
    margin-top: -7px;
    right: 0;
  }

  .upload-text {
    color: #fff;
    background: rgb(33 102 255 / 80%);
    border-radius: 5px;
    font-size: 12px;
    display: inline-block;
    line-height: 22px;
    width: 60px;
    text-align: center;
  }

  .reupload-text {
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -11px;
  }
</style>
