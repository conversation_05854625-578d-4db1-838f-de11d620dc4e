const { promises: fs } = require('fs');
const path = require('path');
const main = require('../../../sim/index');

async function simPack(req, res) {
  await Promise.all(
    req.files.map((file) => {
      const rpkName = path.resolve(__dirname, '../../../sim/cache/rpk', file.originalname);
      return fs.writeFile(rpkName, file.buffer);
    })
  );
  const results = await main();

  return res.json({ results });
}

module.exports = {
  simPack,
};
