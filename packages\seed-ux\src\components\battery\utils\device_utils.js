import battery from '@system.battery'
import volume from '@system.volume'
import brightness from '@system.brightness'
import bluetooth from '@system.bluetooth'
import device from '@system.device'
var deviceData = {
  getInfo: null
}
/**
 * 获取设备信息
 * @returns 返回设备信息
 */
function getInfo() {
  return new Promise((resolve, reject) => {
    if (deviceData.getInfo) {
      resolve(deviceData.getInfo)
    } else {
      device.getInfo({
        success: res => {
          // deviceData.getInfo = res
          resolve(res)
        },
        fail: err => {
          reject(err)
        }
      })
    }
  })
}

function getBattery() {
  return new Promise((resolve, reject) => {
    battery.getStatus({
      success: data => {
        console.log(`handling success: ${data.level}`)
        resolve(data)
      },
      fail: (data, code) => {
        resolve(data)
      }
    })
  })
}

/**
 * 获取设备的音量信息
 * @returns 返回设备的音量信息
 */
function getVolume() {
  return new Promise((resolve, reject) => {
    volume.getMediaValue({
      success: res => {
        resolve(res)
      },
      fail: err => {
        reject(err)
      }
    })
  })
}
/**
 * 设置设备的音量信息
 * @returns 设置设备的音量信息
 */
function setVolume(val) {
  return new Promise((resolve, reject) => {
    volume.setMediaValue({
      value: val, //设置的音量，0.0-1.0 之间
      success: res => {
        resolve(res)
      },
      fail: err => {
        reject(err)
      }
    })
  })
}
/**
 * 获取设备的亮度信息
 * @returns 返回设备的亮度信息
 */
function getBrightness() {
  return new Promise((resolve, reject) => {
    brightness.getValue({
      success: res => {
        resolve(res)
      },
      fail: err => {
        reject(err)
      }
    })
  })
}

/**
 * 设置设备的亮度信息
 * @returns 设置设备的亮度信息
 */
function setBrightness(val) {
  return new Promise((resolve, reject) => {
    brightness.setValue({
      value: val, //屏幕亮度，取值范围 0-255
      success: res => {
        resolve(res)
      },
      fail: err => {
        reject(err)
      }
    })
  })
}

/**
 * 获取蓝牙状态
 * @returns 获取蓝牙状态
 */
function getBluetooth() {
  return new Promise((resolve, reject) => {
    bluetooth.getAdapterState({
      success: function (res) {
        resolve(res)
      },
      fail: function (data, code) {
        reject(data)
      },
      complete: function () { }
    })
  })
}

/**
 * 初始化蓝牙
 * @returns 初始化蓝牙
 */
function initBluetooth(val) {
  return new Promise((resolve, reject) => {
    bluetooth.openAdapter({
      operateAdapter: val ? true : false,
      success: function (res) {
        resolve(res)
      },
      fail: function (data) {
        reject(data)
      },
      complete: function () { }
    })
  })
}

/**
 * 关闭蓝牙
 * @returns 关闭蓝牙
 */
function closeBluetooth() {
  return new Promise((resolve, reject) => {
    bluetooth.closeAdapter({
      operateAdapter: true,
      success: function (res) {
        resolve(res)
      },
      fail: function (data) {
        reject(data)
      },
      complete: function () { }
    })
  })
}
export default {
  getInfo,
  getBattery,
  getVolume,
  setVolume,
  getBrightness,
  setBrightness,
  getBluetooth,
  initBluetooth,
  closeBluetooth
}
