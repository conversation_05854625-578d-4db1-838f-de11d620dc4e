// 处理属性绑定转换
function processPropertyBindings($) {
  // 遍历所有元素
  $('*').each((i, el) => {
    const $el = $(el);

    // 获取元素的所有属性
    const attrs = $el[0].attribs;

    // 遍历并处理每个属性
    for (const attrName in attrs) {
      if (attrs.hasOwnProperty(attrName)) {
        const attrValue = attrs[attrName];

        // 检查属性值是否包含插值语法
        if (typeof attrValue === 'string' && attrValue.startsWith('{{') && attrValue.endsWith('}}')) {
          // 移除插值语法标记
          const processedValue = attrValue.replace(/\{\{(.*)\}\}/, '$1').trim();

          // 添加Vue的属性绑定语法
          $el.attr(`:${attrName}`, processedValue);

          // 移除原始属性
          $el.removeAttr(attrName);
        }
      }
    }
  });
}

module.exports = processPropertyBindings;