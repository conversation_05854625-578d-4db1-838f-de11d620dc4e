const axios = require('axios');

async function getHwLink(packageName) {
  const url = 'https://store-drcn.hispace.dbankcloud.cn/hwmarket/api/clientApi';

  const data = `apsid=*************&arkMaxVersion=0&arkMinVersion=0&arkSupport=0&authorization=%7B%22deviceId%22%3A%22601f17c28a95b0b694f5b800d5894b3aee0e8a3022fdb024010cc656064d511d%22%2C%22deviceType%22%3A%229%22%2C%22serviceToken%22%3A%22sid%3A100147161%3A4b3aa31aa40424ca0f2cc725be6db2bda7526a8a858a983b%22%7D&brand=samsung&clientPackage=com.huawei.fastapp&cno=4010001&code=0200&deviceId=601f17c28a95b0b694f5b800d5894b3aee0e8a3022fdb024010cc656064d511d&deviceIdRealType=3&deviceIdType=9&gradeLevel=0&gradeType=&hardwareType=0&manufacturer=HUAWEI&maxResults=25&method=client.getTabDetail&net=1&oaidTrack=-2&recommendSwitch=1&reqPageNum=1&runMode=2&serviceType=28&sid=0&sign=f74010906t0000md20000000000001000a00000005002b0100010000000190000040230b0100011001000%402D635B1C785C4748AF54C04565475F0C&ts=${new Date().getTime()}&uri=searchApp%7C${encodeURIComponent(
    packageName
  )}&ver=1.1`;

  const config = {
    method: 'post',
    url,
    headers: {
      'sysuseragent':
        'Mozilla/5.0 (Linux; Android 7.1.2; LIO-AN00 Build/LIO-AN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36',
      'Content-Type': 'text/plain',
    },
    data,
  };

  try {
    const res = await axios(config);
    if (res.data && res.data.layoutData) {
      const item = res.data.layoutData.find((v) => v.name === '搜索快应用');
      return item.dataList.slice(0, 10);
    }
    return null;
  } catch (error) {
    console.log(error);
    return null;
  }
}

module.exports = {
  getHwLink,
};
