<template>
  <div class="carousel-container" :style="styles">
    <a-carousel
      :style="{
        width: '100%',
        height: styles.height || '240px',
        borderRadius: styles.borderRadius || '8px'
      }"
      :auto-play="autoplayValue"
      :interval="intervalValue * 1000"
      @change="handleChange"
    >
      <a-carousel-item v-for="(image, index) in images" :key="index">
        <img
          :src="image.url"
          :alt="image.alt"
          :style="{
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }"
        />
      </a-carousel-item>
    </a-carousel>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Carousel as ACarousel, CarouselItem as ACarouselItem } from '@arco-design/web-vue';

const props = defineProps({
  autoplay: {
    type: [Boolean, Object],
    default: true
  },
  interval: {
    type: [Number, Object],
    default: 3
  },
  styles: {
    type: Object,
    default: () => ({})
  }
});

// 处理配置对象类型的属性
const autoplayValue = computed(() => {
  if (typeof props.autoplay === 'object' && props.autoplay !== null) {
    return props.autoplay.value ?? true;
  }
  return props.autoplay;
});

const intervalValue = computed(() => {
  if (typeof props.interval === 'object' && props.interval !== null) {
    return props.interval.value ?? 3;
  }
  return props.interval;
});

// 示例图片数据
const images = ref([
  {
    url: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/cd7a1aaea8e1c5e3d26fe2591e561798.png~tplv-uwbnlip3yd-webp.webp',
    alt: '轮播图1'
  },
  {
    url: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/6480dbc69be1b5de95010289787d64f1.png~tplv-uwbnlip3yd-webp.webp',
    alt: '轮播图2'
  },
  {
    url: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/0265a04fddbd77a19602a15d9d55d797.png~tplv-uwbnlip3yd-webp.webp',
    alt: '轮播图3'
  }
]);

const handleChange = (value) => {
  console.log('轮播图切换:', value);
};
</script>

<style scoped>
.carousel-container {
  width: 100%;
  overflow: hidden;
}
</style> 