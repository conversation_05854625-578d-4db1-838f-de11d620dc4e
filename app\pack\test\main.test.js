// example.test.js
const Task = require('../src/service/nTask/base');
const { TASK_STATUS } = require('../src/constant');

beforeEach(() => {
  jest.useFakeTimers(); // 启用模拟定时器
});

afterEach(() => {
  jest.clearAllTimers(); // 清理所有定时器
});

test('task status', () => {
  const task = new Task();
  task.init({
    taskId: '1',
    brand: 'xiaomi',
    newVersion: '1.0.1',
    branch: 'sdk2082',
    appName: 'qkproject',
  });
  expect(task.status).toBe(TASK_STATUS.WAITING);
  expect(task.taskId).toBe('1');
  expect(task.brand).toBe('xiaomi');
  expect(task.branch).toBe('sdk2082');
  expect(task.appName).toBe('qkproject');
});
