const express = require('express');
const bodyParser = require('body-parser');
const mongoose = require('mongoose');
const path = require('path');
const apiRouter = require('./router');
const versionRouter = require('../../version/router');
const seedRouter = require('../../seed/router');

const wsController = require('./controller/ws');
const { authenticateToken } = require('./middleware/auth');
// 任务
require('../../version');

mongoose
  .connect('mongodb://127.0.0.1:27017/fe')
  .then(() => {
    console.log('数据库连接成功');
  })
  .catch((e) => {
    console.log('数据库连接失败');
  });

const app = express();

wsController.initWs(app);
app.use(bodyParser.json());
app.use(express.static(path.resolve(__dirname, '../client')));

app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*'); // 允许的来源
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE'); // 允许的方法
  res.header('Access-Control-Allow-Headers', 'Content-Type'); // 允许的头
  next();
});
// 全局应用 Token 校验中间件
// app.use(authenticateToken);

// api 路由
app.use('/api', authenticateToken, versionRouter);
app.use('/api', authenticateToken, seedRouter);
app.use('/api', authenticateToken, apiRouter);

app.get('/apis/adplan/obs/conf/v1', (req, res) => {
  res.json({
    code: 0,
    msg: 'success',
    data: {
      bucketName: 'cy-video',
      accessKey: 'IFBESCQ6NJRD1MCVI5SU',
      secretKey: 'rMEfY6VYCfKyxZsIH4JqNLpMyLSEWSpOqvbS2j2Z',
      endPoint: 'https://obs.cn-north-4.myhuaweicloud.com',
    },
  });
});

app.get('*', (req, res) => {
  res.sendFile(path.resolve(__dirname, '../client/index.html'));
});

process.on('uncaughtException', (err) => {
  console.error('Unhandled Exception:', err.message);
  process.exit(1); // 退出应用，1 表示异常退出
});

process.on('unhandledRejection', (reason) => {
  console.error('Unhandled Rejection:', reason.message);
  process.exit(1); // 退出应用
});

module.exports = app;
