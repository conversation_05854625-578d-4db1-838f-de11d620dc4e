const express = require('express');
const router = express.Router();
const SeedAppController = require('../controller/SeedApp');

// SeedApp相关路由
router.post('/SeedApps', SeedAppController.createSeedApp); // 创建SeedApp
router.get('/SeedApps', SeedAppController.getAllSeedApps); // 获取SeedApp列表
router.get('/SeedApps/:id', SeedAppController.getSeedAppById); // 获取单个SeedApp
router.put('/SeedApps/:id', SeedAppController.updateSeedApp); // 更新SeedApp
router.delete('/SeedApps/:id', SeedAppController.deleteSeedApp); // 删除SeedApp

module.exports = router;
