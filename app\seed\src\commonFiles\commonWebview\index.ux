<import name="common-header" src="../../components/commonHeader/index"></import>
<template>
  <div class="privacy-wrapper">
    <!-- 状态栏 -->
    <common-header
      page-bg-type="normal"
      page-name="{{title}}"
      is-show-back="{{true}}"
    ></common-header>
    <div class="web_container">
      <web id="web" class="web" src="{{webUrl}}"></web>
    </div>
  </div>
</template>
<script>
export default {
  data: {
    webUrl: "",
    title: "",
  },
  onInit() {
    this.title = this.$page.query.title
  },
  pageBack() {
    this.$page.finish()
  },
}
</script>
<style lang="less">
.privacy-wrapper {
  flex-direction: column;
  background-color: #fff;
  width: 750px;
  height: 100%;
  .web_container {
    width: 100%;
    height: 100%;
    .web {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
