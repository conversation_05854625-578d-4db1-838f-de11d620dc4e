import { concat } from 'lodash';
import { ref } from 'vue';
import websocketApi from '../apis';
import { generateRequestId } from '../utils/uuid';

const chatRecord = ref<any[]>([]);
const pageInfo = ref<any>({
  keyword: '',
  page: 1,
  pageSize: 20,
  total: 0,
  totalPage: 0,
  sort: 'desc',
});
const useChatRecord = () => {
  const queryChatRecord = (
    {
      keyword,
      sort,
    }: {
      keyword?: string;
      sort?: string;
    } = {},
    callback?: any
  ) => {
    if (sort) {
      pageInfo.value.sort = sort;
    }
    if (keyword || keyword === '') {
      pageInfo.value.keyword = keyword;
    }
    const requestId = generateRequestId();
    websocketApi.send({
      messageType: 'onHistoryMessages',
      requestId,
      data: {
        page: pageInfo.value.page,
        pageSize: pageInfo.value.pageSize,
        keyword: pageInfo.value.keyword,
        sort: pageInfo.value.sort,
      },
    });
    websocketApi.on('onHistoryMessages', (res) => {
      const {
        code,
        data: { detail, total, totalPage },
        requestId: resRequestId,
      } = res;
      if (code === 0 && requestId === resRequestId) {
        const newChatRecord = detail
          .map((item: any) => {
            const { id, content, createdAt, contentType } = item;
            return {
              id,
              message: Number(contentType) === 1 ? content.replace(/\n/g, '<br>') : content,
              createdAt,
              type: Number(contentType) === 1 ? 'my' : 'assistant',
            };
          })
          .reverse();
        chatRecord.value = concat(newChatRecord, chatRecord.value);
        pageInfo.value = {
          ...pageInfo.value,
          total,
          totalPage,
        };
        if (callback) {
          callback();
        }
      }
    });
  };
  const resetRecord = () => {
    chatRecord.value = [];
    pageInfo.value = {
      keyword: '',
      page: 1,
      pageSize: 20,
      total: 0,
      totalPage: 0,
      sort: 'desc',
    };
  };
  const nextPage = (callback: any) => {
    if (pageInfo.value.page >= pageInfo.value.totalPage) {
      return;
    }
    pageInfo.value.page += 1;
    queryChatRecord({}, callback);
  };
  const lastPage = () => {
    if (pageInfo.value.page <= 0) {
      return;
    }
    pageInfo.value.page -= 1;
    queryChatRecord();
  };
  return {
    chatRecord,
    queryChatRecord,
    resetRecord,
    nextPage,
    lastPage,
  };
};
export default useChatRecord;
