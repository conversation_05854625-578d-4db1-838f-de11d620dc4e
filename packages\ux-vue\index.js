const fs = require('fs');
const path = require('path');

const { parseVueFile } = require('./split/ux-split');
const { templateRevertV2 } = require('./template-convert/index');
const { styleConvertV2 } = require('./style-convert/index');
const { scriptConvertV2 } = require('./script-convert/index');

// 处理单个文件
function processFile(filePath, outputDir) {
  console.log('outputDir', outputDir);

  const fileName = path.basename(filePath, '.ux');
  const outputPath = path.join(outputDir, `${fileName}.vue`);

  try {
    // 读取UX文件内容
    const uxContent = fs.readFileSync(filePath, 'utf8');
    const parseResult = parseVueFile(uxContent);

    // 使用cheerio解析HTML结构

    const html = templateRevertV2(parseResult.template.content);
    const style = styleConvertV2(parseResult.styles[0].content);
    const script = scriptConvertV2(parseResult.script.content);

    // 写入Vue文件
    fs.writeFileSync(outputPath, `${html}\n${script}\n${style}`, 'utf8');

    console.log(`Converted ${filePath} to ${outputPath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

// 递归处理目录
function processDirectory(dir, outputDir) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  for (const entry of entries) {
    const entryPath = path.join(dir, entry.name);

    if (entry.isDirectory()) {
      // 递归处理子目录
      processDirectory(entryPath, outputDir);
    } else if (path.extname(entry.name) === '.ux') {
      // 处理UX文件
      processFile(entryPath, outputDir);
    }
  }
}

function main(inputDir, outputDir) {
  console.log(inputDir, outputDir);
  if (!fs.existsSync(inputDir)) {
    console.error(`Error: Input directory ${inputDir} does not exist`);
    process.exit(1);
  }

  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // 开始处理
  processDirectory(inputDir, outputDir);
}

module.exports = main;
