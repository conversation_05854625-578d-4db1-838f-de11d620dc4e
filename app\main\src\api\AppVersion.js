import { http } from '@/utils/http';

const AppVersionApi = {
  // 创建AppVersion
  create(data) {
    return http.post('/api/AppVersions', data);
  },

  // 获取AppVersion列表
  list(params) {
    return http.get('/api/AppVersions', { params });
  },

  // 获取单个AppVersion
  getById(id) {
    return http.get(`/api/AppVersions/${id}`);
  },

  // 更新AppVersion
  update(id, data) {
    return http.put(`/api/AppVersions/${id}`, data);
  },

  // 删除AppVersion
  delete(id) {
    return http.delete(`/api/AppVersions/${id}`);
  },

  fileList(params) {
    return http.get('/api/version-file-list', {
      params,
    });
  },
  fileContent(params) {
    return http.get('/api/version-file-content', {
      params,
    });
  },
};

export default AppVersionApi;
