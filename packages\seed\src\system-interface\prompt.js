const prompt = {
  showToast: (options) => {
    // 提取消息，默认为空字符串
    const { message = '' } = options || {};
    
    // 创建toast元素
    const toast = document.createElement('div');
    
    // 设置样式
    toast.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 10px 16px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      border-radius: 4px;
      font-size: 14px;
      z-index: 1000;
      max-width: 80%;
      text-align: center;
      opacity: 0;
      transition: opacity 0.3s;
    `;
    
    // 设置内容
    toast.textContent = message;
    
    // 添加到文档
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
      toast.style.opacity = '1';
    }, 10);
    
    // 3秒后隐藏
    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  },
  showDialog: (options) => {
    // 提取参数，设置默认值
    const {
      title = '提示',
      message = '',
      buttons = [{ text: '确定', color: '#007AFF' }],
      success = () => {},
      cancel = () => {},
      fail = () => {},
      complete = () => {}
    } = options || {};
    
    // 创建对话框元素
    const dialog = document.createElement('div');
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2000;
      background-color: rgba(0, 0, 0, 0.4);
      opacity: 0;
      transition: opacity 0.2s;
    `;
    
    // 创建对话框内容
    const dialogContent = document.createElement('div');
    dialogContent.style.cssText = `
      background-color: white;
      border-radius: 12px;
      width: 80%;
      max-width: 300px;
      overflow: hidden;
      transform: scale(0.95);
      transition: transform 0.2s;
    `;
    
    // 添加标题
    if (title) {
      const titleEl = document.createElement('div');
      titleEl.style.cssText = `
        padding: 16px 16px 8px;
        text-align: center;
        font-weight: bold;
        font-size: 18px;
      `;
      titleEl.textContent = title;
      dialogContent.appendChild(titleEl);
    }
    
    // 添加消息
    if (message) {
      const messageEl = document.createElement('div');
      messageEl.style.cssText = `
        padding: 8px 16px 16px;
        text-align: center;
        font-size: 16px;
        color: #333;
      `;
      messageEl.textContent = message;
      dialogContent.appendChild(messageEl);
    }
    
    // 添加按钮容器
    const buttonsContainer = document.createElement('div');
    buttonsContainer.style.cssText = `
      display: flex;
      border-top: 1px solid #eee;
    `;
    
    // 添加按钮
    buttons.forEach((button, index) => {
      const buttonEl = document.createElement('button');
      buttonEl.style.cssText = `
        flex: 1;
        padding: 12px 0;
        background-color: white;
        border: none;
        font-size: 16px;
        cursor: pointer;
        outline: none;
        color: ${button.color || '#007AFF'};
        ${index > 0 ? 'border-left: 1px solid #eee' : ''}
      `;
      buttonEl.textContent = button.text || '按钮';
      
      // 按钮点击事件
      buttonEl.addEventListener('click', () => {
        // 隐藏对话框
        dialog.style.opacity = '0';
        dialogContent.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
          document.body.removeChild(dialog);
          
          // 触发成功回调
          if (button.role === 'cancel') {
            cancel();
          } else {
            success({
              index,
              tapIndex: index,
              confirm: button.role !== 'cancel',
              cancel: button.role === 'cancel'
            });
          }
          
          // 触发完成回调
          complete();
        }, 200);
      });
      
      buttonsContainer.appendChild(buttonEl);
    });
    
    dialogContent.appendChild(buttonsContainer);
    dialog.appendChild(dialogContent);
    document.body.appendChild(dialog);
    
    // 显示动画
    setTimeout(() => {
      dialog.style.opacity = '1';
      dialogContent.style.transform = 'scale(1)';
    }, 10);
    
    // 返回一个取消函数
    return {
      cancel: () => {
        // 隐藏对话框
        dialog.style.opacity = '0';
        dialogContent.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
          if (dialog.parentNode) {
            document.body.removeChild(dialog);
          }
          
          // 触发取消回调
          cancel();
          complete();
        }, 200);
      }
    };
  },
  showContextMenu: (options) => {
    // 提取参数，设置默认值
    const {
      itemList = [],
      itemColor = '#000000',
      success = () => {},
      cancel = () => {},
      fail = () => {},
      complete = () => {}
    } = options || {};
    
    // 校验参数
    if (!Array.isArray(itemList) || itemList.length === 0) {
      fail({ errMsg: 'itemList参数必须是一个非空数组' }, 10001);
      complete();
      return;
    }
    
    // 创建遮罩层
    const mask = document.createElement('div');
    mask.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
      z-index: 3000;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      opacity: 0;
      transition: opacity 0.2s;
    `;
    
    // 创建菜单容器
    const menuContainer = document.createElement('div');
    menuContainer.style.cssText = `
      background-color: white;
      border-radius: 12px 12px 0 0;
      overflow: hidden;
      transform: translateY(100%);
      transition: transform 0.3s ease-out;
    `;
    
    // 添加菜单项
    itemList.forEach((item, index) => {
      const menuItem = document.createElement('div');
      menuItem.style.cssText = `
        padding: 16px;
        text-align: center;
        font-size: 18px;
        color: ${itemColor};
        border-bottom: 1px solid #f5f5f5;
        cursor: pointer;
      `;
      menuItem.textContent = item;
      
      // 菜单项点击事件
      menuItem.addEventListener('click', () => {
        // 隐藏菜单
        mask.style.opacity = '0';
        menuContainer.style.transform = 'translateY(100%)';
        
        setTimeout(() => {
          document.body.removeChild(mask);
          
          // 触发成功回调
          success({
            tapIndex: index
          });
          
          // 触发完成回调
          complete();
        }, 300);
      });
      
      menuContainer.appendChild(menuItem);
    });
    // 取消按钮点击事件
    cancelButton.addEventListener('click', () => {
      // 隐藏菜单
      mask.style.opacity = '0';
      menuContainer.style.transform = 'translateY(100%)';
      
      setTimeout(() => {
        document.body.removeChild(mask);
        
        // 触发取消回调
        cancel();
        
        // 触发完成回调
        complete();
      }, 300);
    });
    
    menuContainer.appendChild(cancelButton);
    mask.appendChild(menuContainer);
    document.body.appendChild(mask);
    
    // 显示动画
    setTimeout(() => {
      mask.style.opacity = '1';
      menuContainer.style.transform = 'translateY(0)';
    }, 10);
    
    // 返回一个取消函数
    return {
      cancel: () => {
        // 隐藏菜单
        mask.style.opacity = '0';
        menuContainer.style.transform = 'translateY(100%)';
        
        setTimeout(() => {
          if (mask.parentNode) {
            document.body.removeChild(mask);
          }
          
          // 触发取消回调
          cancel();
          complete();
        }, 300);
      }
    };
  }
}
module.exports = prompt