const path = require('path');

const OUT_PUT_DIR = path.join(__dirname, 'output'); // 输出目录

const COMMON_FILES_LIST = [
    { src: './commonFiles/userAgreement', dest: './components/userAgreement' }, // 隐私协议
    { src: './commonFiles/commonModal', dest: './components/commonModal' }, // 协议
    { src: './commonFiles/commonWebview', dest: './pages/commonWebview' }, // 网页
    { src: './commonFiles/commonHeader', dest: './components/commonHeader'},
    { src: './commonFiles/utils', dest: './utils'}, // utils方法
    { src: './commonFiles/style', dest: './style'}, // style样式
]

const COMMON_PAGES_LIST = [
    'pages/CommonWebview',
    'pages/Main'
]



module.exports = {
    OUT_PUT_DIR, // 输出目录
    COMMON_FILES_LIST, // 公共文件列表
    COMMON_PAGES_LIST, // 公共页面列表
}