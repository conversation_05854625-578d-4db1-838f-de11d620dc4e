// 生成页面文件
const fs = require('fs');
const path = require('path');
const { OUT_PUT_DIR } = require('../constant.js');

function generatePage(pageConfig) {
    // const { path } = pageConfig; // 从meta中获取logoUrl
    const pageUXTempDir = path.join(__dirname, '../template/page.ux');
    const pageUXTemplate = fs.readFileSync(pageUXTempDir, 'utf-8');
    const modifications = getModification(pageConfig)
    const pageUXContent = modifyPageUX(pageUXTemplate, modifications); // 下载文件并返回路径
    return pageUXContent
}

function getModification(pageConfig){
    const { meta, components } = pageConfig; // 从pageConfig中获取meta
    const { pageName } = meta; // 从meta中获取pageName
    const componentModifications = getComponents(components)
    componentModifications.push({old:'<!-- pageContent -->', new:`<text>${pageName}</text>`})
    return componentModifications
}

function getComponents(components){
    const modifications = []; // 用于存储组件的修改信息
    let imports = ''
    let tags = ''
    components.forEach(component => {
        const { name, props } = component; // 解构赋值，避免与JavaScript关键字冲突
        const importsModifications = getImports(name, props); // 获取组件的修改信息
        const componentModifications = getComponent(name, props); // 获取组件的修改信息
        imports+=importsModifications; // 拼接组件的修改信息
        tags+=componentModifications; // 拼接组件的修改信息
    })
    modifications.push({old:'<!-- imports -->', new:imports}) // 拼接组件的修改信息
    modifications.push({old:'<!-- tags -->', new:tags}) // 拼接组件的修改信息
    return modifications;
}

function getImports(name, props){
    return `<import name="${name}" src="../../components/${name}/index.ux"></import>\n`; // 生成import语句
}

function getComponent(name, props){
    let res = `<${name} `
    Object.keys(props).forEach(prop => {
        const formatProp = prop.replace(/([A-Z])/g, '-$1').toLowerCase(); // 转换为kebab-case
        let value = props[prop];
        if (Array.isArray(value)) {
            value = JSON.stringify(value);
        }
        res+=`${formatProp}={{${value}}} `
    })
    res+=`></${name}>`
    console.log(res)
    return res; // 生成组件标签
}

function modifyPageUX(pageUXTemplate, modifications){
    modifications.forEach(modification => {
        const { old, new: newValue } = modification; // 解构赋值，避免与JavaScript关键字冲突
        pageUXTemplate = pageUXTemplate.replace(old, newValue); // 替换模板中的占位符
    })
    return pageUXTemplate
}



module.exports = { generatePage }