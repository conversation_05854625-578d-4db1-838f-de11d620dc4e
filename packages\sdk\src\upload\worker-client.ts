import MD5Worker from './file-md5-worker?worker&inline';

// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-shadow
enum ExecStatus {
  Pending,
  Execution,
  Done,
}

function genPromise() {
  let resolve;
  let reject;
  const promise = new Promise((_resolve, _reject) => {
    resolve = _resolve;
    reject = _reject;
  });

  return { promise, resolve, reject };
}

class TaskQueue {
  list: any[] = [];

  push(task) {
    const { promise, resolve, reject } = genPromise();
    const subTask = async () => {
      info.status = ExecStatus.Execution;
      const result = await task();
      info.status = ExecStatus.Done;
      resolve(result);
      this.exec();
    };

    const info = { status: ExecStatus.Pending, task: subTask };
    this.list.push(info);
    this.exec();
    return promise;
  }

  exec() {
    const len = this.list.filter((item) => item.status === ExecStatus.Execution).length;
    if (len < navigator.hardwareConcurrency) {
      const next = this.list.find((item) => item.status === ExecStatus.Pending);
      next?.task();
    }
  }
}

export default function md5WorkerFactory() {
  const queue = new TaskQueue();

  function calc(file) {
    return queue.push(async () => calcFileMD5({ file }));
  }

  return { calc };
}

function calcFileMD5(data) {
  const worker: any = new MD5Worker();
  worker.postMessage(data);
  return new Promise((resolve, reject) => {
    const cancel = () => {
      reject();
      worker?.terminate();
    };
    // abort.push(cancel);

    worker.onmessage = (evt) => {
      resolve(evt.data?.md5);
      worker.terminate();
    };
    worker.onmessageerror = () => {
      cancel();
    };
  });
}
