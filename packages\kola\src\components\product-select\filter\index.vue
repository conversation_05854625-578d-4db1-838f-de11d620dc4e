<template>
  <DynamicForm :form-schema="formSchema" v-model="filter" layout="inline" ref="formRef">
    <template #footer>
      <a-button type="primary" @click="query" class="search-btn">
        <template #icon>
          <icon-search />
        </template>
        查询
      </a-button>
      <a-button @click="reset" class="reset-btn">
        <template #icon>
          <icon-refresh />
        </template>
        重置
      </a-button>
    </template>
  </DynamicForm>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import DynamicForm from '../../form/index.vue';
  import { DForm } from '../../types/form';
  import useRefreshTable from '../../hooks/refresh-table';

  defineOptions({ name: 'DynamicFilter' });

  defineProps<{
    formSchema: DForm['formSchema'];
  }>();

  const filter = defineModel<Record<string, any>>({ default: {} });

  const formRef = ref();

  const refreshTable = useRefreshTable();

  const query = () => {
    refreshTable?.({ resetPagination: true });
  };

  const reset = () => {
    formRef.value?.reset();
    refreshTable?.({ resetPagination: true });
  };
</script>

<style scoped lang="less">
  .search-btn,
  .reset-btn {
    margin-left: 8px;
    margin-bottom: 16px;
  }
</style>
