<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="fieldLabel"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
  >
    <a-tree-select
      :data="sourceData"
      v-model="value"
      :field-names="fieldNames"
      :allow-search="field.select?.allowSearch"
      :filter-tree-node="filterTreeNode"
      :allow-clear="field.select?.allowClear"
      :multiple="field.select?.multiple"
      :label-in-value="field.select?.labelInValue"
      :placeholder="field.placeholder || `请输入${fieldLabel}`"
      :tree-props="treeProps"
    >
    </a-tree-select>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, ref, watchEffect } from 'vue';
  import { isFunction } from 'lodash';
  import { TreeSelectField } from '../../../types/form';

  const props = defineProps<{
    field: TreeSelectField;
    path: string;
  }>();
  const extra = props.field.extra as unknown as string;
  const formData = inject('formData');
  const fieldLabel = computed(() => {
    return isFunction(props.field.label)
      ? props.field.label(props.field, formData?.value as any, props.path)
      : props.field.label;
  });
  const fieldNames = computed(() => {
    const { labelKey, valueKey, childrenKey } = props.field.source;
    return {
      key: valueKey ?? 'value',
      title: labelKey ?? 'label',
      children: childrenKey ?? 'children',
    };
  });

  const treeProps = computed(() => {
    return {
      defaultExpandAll: false,
      ...props.field.select?.treeProps,
    };
  });
  const value = defineModel<any>({
    default: '',
    set(val) {
      props.field.onChange?.(val, formData);
      if (props.field.setter) {
        return props.field.setter(val);
      }
      return val;
    },
  });

  const sourceData = ref<{ value: string | number; label: string; disabled?: boolean }[]>([]);

  function filterTreeNode(keyword, node) {
    if (props.field.select?.filterTreeNode) {
      return props.field.select?.filterTreeNode(keyword, node);
    }
    return node[fieldNames.value.title].includes(keyword);
  }

  async function getSourceData() {
    if (typeof props.field.source.data === 'function') {
      sourceData.value = await props.field.source.data(props.field, formData?.value as any, props.path);
    } else {
      sourceData.value = props.field.source.data || [];
    }
  }

  watchEffect(() => {
    getSourceData();
  });
</script>
