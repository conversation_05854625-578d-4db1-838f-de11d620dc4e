<template>
  <div class="containers">
    <div class="left-bar" v-if="type !== 'all'">
      <div class="title">{{ title }}</div>
      <div class="list">
        <div
          :class="`${item[LIST_KEY] === curListKey ? 'active list-item' : 'list-item'}`"
          v-for="item in listData"
          :key="item[LIST_KEY]"
          @click="handleItemClick(item)"
        >
          <div class="ellipsis">{{ item.name }}</div>
          <icon-check-circle :class="`${get(formData, item.id) ? 'active icon' : 'icon'}`" />
        </div>
      </div>
    </div>
    <div class="right-bar">
      <a-table
        row-key="id"
        :columns="tableColumns"
        :data="tableData"
        bordered
        :row-selection="{ type: 'radio' }"
        :selected-keys="selectedRowKeys"
        @select="handleSelect"
        :pagination="false"
      ></a-table>
    </div>
    {{ formData }}
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, ref, defineModel, watch } from 'vue';
  import type { TableColumnData } from '@arco-design/web-vue';
  import { get } from 'lodash';

  const props = defineProps<{
    title: string;
    listData: Record<string, any>[];
    tableData: Record<string, any>[];
    tableColumns: TableColumnData[];
    listKey?: string;
    type: 'all' | 'single';
  }>();

  const LIST_KEY = props.listKey || 'id';

  const formData = defineModel<Record<string, string>>({
    default: {},
  });

  watch(
    () => props.type,
    () => {
      selectedRowKeys.value = [];
      formData.value = {};
    }
  );

  // =========

  const curListKey = ref<string>(props.listData[0]?.[LIST_KEY] || '');

  const selectedRowKeys = ref([formData.value[curListKey.value]]);

  function handleItemClick(listItem: Record<string, any>) {
    curListKey.value = listItem[LIST_KEY];
    selectedRowKeys.value = [formData.value[curListKey.value]];
  }

  function handleSelect(selectedKeys: string[]) {
    if (props.type === 'single') {
      formData.value[curListKey.value] = selectedKeys[0];
    } else {
      props.listData.forEach((item) => {
        formData.value[item[LIST_KEY]] = selectedKeys[0];
      });
    }
    selectedRowKeys.value = [selectedKeys[0]];
  }
</script>

<style lang="less" scoped>
  .containers {
    display: flex;
    height: 100%;
    border: 1px solid var(--color-neutral-3);
    padding: 0;
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .left-bar {
    width: 300px;
    flex: 0 300px;
    border-right: 1px solid var(--color-neutral-3);

    .title {
      height: 57px;
      line-height: 57px;
      text-align: center;
      font-size: 14px;
      color: #333;
      border-bottom: 1px solid var(--color-neutral-3);
    }

    .list-item {
      display: flex;
      height: 56px;
      padding: 9px 16px;
      font-size: 14px;
      color: #333;
      cursor: pointer;
      border-bottom: 1px solid var(--color-neutral-3);
      align-items: center;
      justify-content: space-between;

      &:hover {
        background-color: var(--color-fill-2);
      }

      &.active {
        background-color: var(--color-fill-2);
        color: rgb(var(--primary-6));
      }
    }

    .icon {
      font-size: 18px;

      &.active {
        color: rgb(var(--success-6));
      }
    }
  }

  .right-bar {
    flex: 1;
  }
</style>
