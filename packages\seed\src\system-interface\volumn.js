const volumn = {
  getMediaValue: (options) => {
    const { success } = options || {};
    
    // 固定返回0.7作为音量值
    const mockVolume = 0.7;
    
    setTimeout(() => {
      if (typeof success === 'function') {
        success({
          value: mockVolume,
          max: 1,
          min: 0
        });
      }
    }, 300);
  },
  setMediaValue: (options) => {
    const { value, success } = options || {};
    
    setTimeout(() => {
      if (typeof success === 'function') {
        success();
      }
    }, 300);
  },
  
}
module.exports = volumn