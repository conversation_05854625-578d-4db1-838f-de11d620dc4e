/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutFooter: typeof import('ant-design-vue/es')['LayoutFooter']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    APopover: typeof import('ant-design-vue/es')['Popover']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATag: typeof import('ant-design-vue/es')['Tag']
    AWatermark: typeof import('ant-design-vue/es')['Watermark']
    Dialog: typeof import('./src/components/Dialog/index.vue')['default']
    PkgList: typeof import('./src/components/PkgList/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
