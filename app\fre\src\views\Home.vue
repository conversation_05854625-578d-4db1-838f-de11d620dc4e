<template>
  <a-layout style="width: 100vw; height: 100vh">
    <a-layout>
      <a-layout-sider width="200" style="background: #fff">
        <a-menu
          mode="inline"
          :openKeys="openKeys"
          v-model:selectedKeys="selectedKeys"
          @openChange="onOpenChange"
          v-for="item in routerConfig.platform"
          :key="item.value"
        >
          <a-sub-menu key="sub1">
            <template #title>{{ item.label }}</template>
            <a-menu-item v-for="child in item.children" :key="child.value" @click="goTo(child.value)">{{
              child.label
            }}</a-menu-item>
          </a-sub-menu>
        </a-menu>
      </a-layout-sider>
      <a-layout style="padding: 0 24px 24px">
        <a-breadcrumb style="margin: 16px 0">
          <a-breadcrumb-item>Home</a-breadcrumb-item>
          <a-breadcrumb-item>auto-build</a-breadcrumb-item>
        </a-breadcrumb>
        <a-layout-content
          :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px', position: 'relative' }"
        >
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script lang="ts">
  import { UserOutlined, LaptopOutlined, NotificationOutlined } from '@ant-design/icons-vue';
  import { defineComponent, ref, reactive, toRefs } from 'vue';
  import routerConfig from '../config/router';
  export default defineComponent({
    components: {
      UserOutlined,
      LaptopOutlined,
      NotificationOutlined,
    },
    setup() {
      interface RouterConfig {
        platform: {
          label: string;
          value: string;
          children: { label: string; value: object }[];
        }[];
      }
      const state = reactive({
        rootSubmenuKeys: ['sub1', 'sub2', 'sub4'],
        openKeys: ['sub1'],
        selectedKeys: [],
      });
      const onOpenChange = (openKeys: string[]) => {
        const latestOpenKey = openKeys.find((key) => state.openKeys.indexOf(key) === -1);
        if (state.rootSubmenuKeys.indexOf(latestOpenKey!) === -1) {
          state.openKeys = openKeys;
        } else {
          state.openKeys = latestOpenKey ? [latestOpenKey] : [];
        }
      };
      return {
        selectedKeys1: ref<string[]>(['2']),
        selectedKeys2: ref<string[]>(['1']),
        collapsed: ref<boolean>(false),
        routerConfig,
        ...toRefs(state),
        onOpenChange,
      };
    },
    methods: {
      goTo(path: string) {
        this.$router.push(path);
      },
    },
  });
</script>
<style>
  #components-layout-demo-top-side-2 .logo {
    float: left;
    width: 120px;
    height: 31px;
    margin: 16px 24px 16px 0;
    background: rgba(255, 255, 255, 0.3);
  }

  .ant-row-rtl #components-layout-demo-top-side-2 .logo {
    float: right;
    margin: 16px 0 16px 24px;
  }

  .site-layout-background {
    background: #fff;
  }
</style>
