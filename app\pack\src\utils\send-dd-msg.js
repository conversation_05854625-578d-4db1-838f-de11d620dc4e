const axios = require('axios');

const accessToken = '5fd24a358e5a1a5090e688456fd0be8a24d43c1a23e9fe030493e3dd9bb4a199';
const sendMsgToDingTalk = ({ mrLink }) => {
  const url = `https://oapi.dingtalk.com/robot/send?access_token=${accessToken}`;

  const requestData = {
    msgtype: 'text',
    at: {
      isAtAll: 'false',
      atMobiles: ['13572072770'],
    },
    text: {
      content: `帮忙合个代码:  ${mrLink}`,
    },
  };

  axios
    .post(url, requestData)
    .then((response) => {
      console.log('sendMsgToDingTalk', response.data);
    })
    .catch((error) => {
      console.error('Error:', error);
    });
};

module.exports = sendMsgToDingTalk;
