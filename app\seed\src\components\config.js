// 在这里注册组件

/*
轮播组件
**/

const SWIPER = {
    name: 'swiper', // 组件名称
    props: { // 组件属性
        imgList: { // 轮播列表
            type: Array, // 类型为数组
            default: [], // 默认值为空数组
            required: true // 必须传递
        },
        autoplay: { // 自动播放
            type: Boolean, // 类型为布尔值
            default: true, // 默认值为true
            required: false // 可选传递
        },
    }
}



const componentsMap = {
    'bannerSwiper': SWIPER, // 轮播组件
}

module.exports = {
    componentsMap
}