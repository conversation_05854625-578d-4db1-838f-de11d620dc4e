import axios from 'axios';
import { http } from '@/utils/http';

export function getAppList() {
  return axios.get('/api/appList').then((res) => {
    // 根据name去重
    const nameMap = new Map();
    res.data.forEach((item: any) => {
      if (!nameMap.has(item.pkgName)) {
        nameMap.set(item.pkgName, {
          label: item.pkgName,
          value: item.pkgName,
          key: item.key,
        });
      }
    });
    return {
      originData: res.data,
      data: Array.from(nameMap.values()),
    };
  });
}

export async function getSdkVersion() {
  return axios.get('/api/sdkVersion').then((res) => {
    return res.data.map((item: string) => ({
      label: item,
      value: item,
    }));
  });
}

export async function submitPack(formDate: any) {
  return axios.post('/api/pack', formDate).then((res) => {
    return res.data;
  });
}


export async function submitUpgrade(formDate: any) {
  return axios.post('/api/upgrade', formDate).then((res) => {
    return res.data;
  });
}

// export async function getPackResult() {
//   return axios.get('/api/packResult').then((res) => {
//     res.data.list.forEach((item:any)=>{
//       // eslint-disable-next-line
//       item.id = item['_id']
//     })
//     return res
//   })
// }

export function getPackResult(params: any) {
  return axios.get('/api/packResult', { params });
}

// 获取打包结果
// async function getPackResult() {
//     try {
//         const response = await fetch('/api/packResult')
//         if (!response.ok) {
//             throw new Error('网络响应不正常')
//         }

//         return await response.json()
//     } catch (error) {
//         console.error('获取数据失败:', error)
//     }
// }

// 删除打包任务
export async function deletePack(params: any) {
  return axios.post('/api/delTask', params).then((res) => {
    return res.data;
  });
}

// // 提交打包信息
// async function submitPack(formDate: any) {
//     try {
//         const response = await fetch('/api/pack', {
//             method: 'POST',
//             headers: {
//                 'Content-Type': 'application/json',
//             },
//             body: JSON.stringify(formDate),
//         })
//         if (!response.ok) {
//             throw new Error('网络响应不正常')
//         }

//         return await response.json()
//     } catch (error) {
//         console.error('获取数据失败:', error)
//     }
// }

// // 更新对接层
// async function updateAdInterface() {
//     try {
//         const response = await fetch('/api/updateAdInterface')
//         return await response.json()
//     } catch (error) {
//         console.error('更新对接层失败:', error)
//     }
// }

// export default { getAppList, getSdkVersion, submitPack, getPackResult, deletePackTask, updateAdInterface }
