const App = require('../model/app.js');

// 创建应用
const createApp = async (req, res) => {
  try {
    const { name, packageName, icon, company } = req.body;
    const newApp = new App({ name, packageName, icon, company });
    await newApp.save();
    res.status(201).json(newApp);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// 获取应用列表（支持分页和名称搜索）
// 请求参数：
// - page: 当前页码（默认1）
// - pageSize: 每页数量（默认10）
// - name: 应用名称（可选，用于模糊搜索）
const getAllApps = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, name } = req.query;
    const query = {};

    // 如果有名称搜索条件，添加模糊查询
    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }

    // 计算分页参数
    const skip = (page - 1) * pageSize;

    // 查询总数
    const total = await App.countDocuments(query);

    // 查询分页数据
    const apps = await App.find(query).skip(skip).limit(Number(pageSize)).sort({ createdAt: -1 });

    res.status(200).json({
      data: apps,
      pagination: {
        page: Number(page),
        pageSize: Number(pageSize),
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// 获取单个应用
const getAppById = async (req, res) => {
  try {
    const app = await App.findById(req.params.id);
    if (!app) {
      return res.status(404).json({ message: '应用未找到' });
    }
    res.status(200).json(app);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// 更新应用
const updateApp = async (req, res) => {
  try {
    const { id } = req.params;
    const updatedApp = await App.findByIdAndUpdate(id, req.body, {
      new: true,
      runValidators: true,
    });
    if (!updatedApp) {
      return res.status(404).json({ message: '应用未找到' });
    }
    res.status(200).json(updatedApp);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// 删除应用
const deleteApp = async (req, res) => {
  try {
    const { id } = req.params;
    const deletedApp = await App.findByIdAndDelete(id);
    if (!deletedApp) {
      return res.status(404).json({ message: '应用未找到' });
    }
    res.status(200).json({ message: '应用删除成功' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createApp,
  getAllApps,
  getAppById,
  updateApp,
  deleteApp,
};
