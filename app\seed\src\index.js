// 主流程
// 生成文件到output目录
const { generateSign } = require('./tasks/generateSign.js');
const { generateLogo } = require('./tasks/generateLogo.js');
const { generateAppUx } = require('./tasks/generateAppUx.js');
const { generateManifest } = require('./tasks/generateManifest.js');
const { generatePages } = require('./tasks/generatePages.js');
const { generateCommonFiles } = require('./tasks/generateCommonFiles.js');
const { generateMain } = require('./tasks/generateMain.js');
const { generateTabs } = require('./tasks/generateTabs.js'); // 生成tab页文件

const appConfig = require('./appConfig.json');

function main(task) {
  const { meta } = task;
  // 生成签名文件
  generateSign(meta);
  // 生成logo文件
  generateLogo(meta);
  // 生成manifest文件
  generateManifest(meta);
  // 生成app.ux文件
  generateAppUx(meta);

  // 生成公共文件
  generateCommonFiles();

  // 生成main/index.ux文件
  generateMain(meta);

  // 生成tab页
  generateTabs(task);

  // 生成pages文件
  generatePages(task);
}

module.exports = { main };
