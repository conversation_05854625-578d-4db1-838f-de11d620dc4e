<template>
  <div class="powersaving">
    <div class="content" style="background-color:{{backColor}}">
      <div class="save-power">
        <text class="device-title">开启省电</text>
        <div class="save-area">
          <div if="{{isXiaomi}}" class="save-item">
            <image
              src="{{nowBluetooth? path +'quickapp/app/ydpower/images/bluetooth-active.png' : path+ 'quickapp/app/ydpower/images/bluetooth.png'}}"
              @click="editBluetooth"></image>
            <text>蓝牙</text>
          </div>
          <div class="save-item">
            <image
              src="{{brightFlag? path + 'quickapp/app/ydpower/images/brightness-active.png' : path + 'quickapp/app/ydpower/images/brightness.png'}}"
              @click="handleSetBrightness"></image>
            <text>屏幕亮度</text>
          </div>
          <div class="save-item">
            <image src="{{path}}quickapp/app/ydpower/images/volume.png" @click="setVolume"></image>
            <text>音量</text>
          </div>
        </div>
        <div class="bottom-area">
          <div class="bottom-item" @click="batteryCare">
            <image src="{{path}}quickapp/app/ydpower/images/battery-bg.png"></image>
            <div class="bottom-info">
              <text>保养技巧</text>
            </div>
          </div>
          <div class="bottom-item" @click="savingSkill">
            <image src="{{path}}quickapp/app/ydpower/images/saving-bg.png"></image>
            <div class="bottom-info">
              <text>省电技巧</text>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
  import router from '@system.router'
  import prompt from '@system.prompt'
  import bluetooth from '@system.bluetooth'
  const __BUS_PUBLIC_OSS_BASE_URL__ = "https://static.chengyouyun.com/"

  export default {
    props: {
      backColor: {
        type: String,
        default: 'transparent'
      },
    },
    data: {
      deviceInfo: "",
      languageText: '',
      nowVolume: "",
      nowBrightness: "",
      brightFlag: true,
      nowBluetooth: "",
      isXiaomi: true,
      path: __BUS_PUBLIC_OSS_BASE_URL__,
    },
    getVolume() {
      return new Promise((resolve, reject) => {
        volume.getMediaValue({
          success: res => {
            resolve(res)
          },
          fail: err => {
            reject(err)
          }
        })
      })
    },
    getBrightness() {
      return new Promise((resolve, reject) => {
        brightness.getValue({
          success: res => {
            resolve(res)
          },
          fail: err => {
            reject(err)
          }
        })
      })
    },
    setVolume(val) {
      return new Promise((resolve, reject) => {
        volume.setMediaValue({
          value: val, //设置的音量，0.0-1.0 之间
          success: res => {
            resolve(res)
          },
          fail: err => {
            reject(err)
          }
        })
      })
    },
    getBluetooth() {
      return new Promise((resolve, reject) => {
        bluetooth.getAdapterState({
          success: function (res) {
            resolve(res)
          },
          fail: function (data, code) {
            reject(data)
          },
          complete: function () { }
        })
      })
    },
    setBrightness(val) {
      return new Promise((resolve, reject) => {
        brightness.setValue({
          value: val, //屏幕亮度，取值范围 0-255
          success: res => {
            resolve(res)
          },
          fail: err => {
            reject(err)
          }
        })
      })
    },
    initBluetooth(val) {
      return new Promise((resolve, reject) => {
        bluetooth.openAdapter({
          operateAdapter: val ? true : false,
          success: function (res) {
            resolve(res)
          },
          fail: function (data) {
            reject(data)
          },
          complete: function () { }
        })
      })
    },
    closeBluetooth() {
      return new Promise((resolve, reject) => {
        bluetooth.closeAdapter({
          operateAdapter: true,
          success: function (res) {
            resolve(res)
          },
          fail: function (data) {
            reject(data)
          },
          complete: function () { }
        })
      })
    },
    onInit() {
      let that = this
      this.getVolume().then(res => {
        console.log("当前音量信息：", res)
        res && (this.nowVolume = res.value)
      })
      this.getBrightness().then(res => {
        console.log("当前亮度信息：", res)
        res && (this.nowBrightness = res.value)
      })
      // DEVICE_UTILS.getBluetooth().then(resIn => {
      //   //当前蓝牙属于开启状态
      //   this.nowBluetooth = resIn.available
      //   console.log("当前蓝牙信息：", resIn)
      // })
      // bluetooth.onadapterstatechange = function (data) {
      //   console.log("当前蓝牙信息状态发生变化：", data)
      //   that.nowBluetooth = data.available
      // }
    },
    // 省电技巧
    savingSkill() {
      router.push({
        uri: '/components/battery/SavingSkill'
      })
    },
    // 电池保养技巧
    batteryCare() {
      router.push({
        uri: '/components/battery/BatteryCare'
      })
    },
    //减少音量
    setVolume() {
      this.setVolume(Number(this.nowVolume) - 0.1).then(res => {
        // if (res == 'success') {
        this.nowVolume = Number(this.nowVolume) - 0.1;
        // $utils.showToast("音量已减少")
        alert("音量已减少")
        // }
      }).catch(() => {
        // $utils.showToast("音量最小了")
        alert("音量最小了")
      })
    },
    //减少亮度
    handleSetBrightness() {
      let that = this;
      if (this.brightFlag) {
        let setBrightness = Number(this.nowBrightness) - 100 <= 1 ? 1 : Number(this.nowBrightness) - 100
        this.setBrightness(setBrightness).then(res => {
          // if (res == 'success') {
          that.nowBrightness = setBrightness;
          that.brightFlag = false;
          // }
        })
      } else {
        //  +亮度
        let setBrightness = Number(this.nowBrightness) + 100 >= 255 ? 200 : Number(this.nowBrightness) + 100
        this.setBrightness(setBrightness).then(res => {
          // if (res == 'success') {
          that.nowBrightness = setBrightness;
          that.brightFlag = true;
          // }
        })
      }
    },
    //蓝牙开关
    editBluetooth() {
      this.initBluetooth().then(res => {
        this.getBluetooth().then(resIn => {
          //当前蓝牙属于开启状态
          this.nowBluetooth = true
          console.log("当前蓝牙信息：", resIn)
          this.closeBluetooth(true).then(res => {
            console.log("关闭蓝牙操作：", res)
            this.nowBluetooth = false
            prompt.showToast({
              message: '蓝牙关闭成功'
            })
          })
        }).catch(err => {
          this.nowBluetooth = false
          this.initBluetooth(true).then(res => {
            console.log("打开蓝牙操作：", res)
            this.nowBluetooth = true
            prompt.showToast({
              message: '蓝牙打开成功'
            })
          })
        })
      }).catch(err => {
        this.nowBluetooth = false
        this.initBluetooth(true).then(res => {
          console.log("打开蓝牙操作：", res)
          this.nowBluetooth = true
          prompt.showToast({
            message: '蓝牙打开成功'
          })
        })
      })
    },
  }
</script>
<style lang="less">
  .powersaving {
    flex-direction: column;
    background-color: #f7f7f7;

    .top-bg {
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      width: 750px;
      height: 228px;
      background: linear-gradient(180deg, #e4ffe6, rgba(245, 245, 245, 0) 100%);
    }

    .content {
      flex-direction: column;

      padding: 30px;

      .device-info {
        margin-top: 20px;
        flex-direction: column;
        background-color: #ffffff;
        border-radius: 20px;
        /* box-shadow: 0px 8px 16px 0px rgba(204, 204, 204, 0.2); */
        padding: 35px 25px;

        .device-title {
          font-size: 32px;
          font-weight: 600;
          color: #000000;
        }

        .device-area {
          margin: 60px auto;
          margin-bottom: 70px;

          /* justify-content: center; */
          .phone-icon {
            width: 49px;
          }

          .phone-info {
            flex-direction: column;
            margin-left: 27px;

            .phone-brand {
              font-size: 40px;
              font-weight: 600;
              color: #000000;
              line-height: 40px;
            }

            .phone-system {
              font-size: 28px;
              line-height: 28px;
              font-weight: 400;
              color: #bebebe;
              margin-top: 20px;
            }
          }
        }

        .screen-info {
          justify-content: space-between;
          width: 80%;
          margin: 0 auto;

          .info-item {
            flex-direction: column;

            .info-num {
              font-size: 28px;
              font-weight: 600;
              color: #333333;
              text-align: center;
              line-height: 28px;
            }

            .info-text {
              font-size: 24px;
              font-weight: 400;
              color: #c0c0c0;
              margin-top: 20px;
              text-align: center;
              line-height: 24px;
            }
          }
        }
      }

      .save-power {
        flex-direction: column;
        background-color: #ffffff;
        border-radius: 20px;
        /* box-shadow: 0px 8px 16px 0px rgba(204, 204, 204, 0.2); */
        padding: 35px 25px;
        margin-top: 20px;

        .device-title {
          font-size: 32px;
          font-weight: 600;
          color: #000000;
        }

        .save-area {
          justify-content: space-between;
          width: 80%;
          margin: 50px auto 40px;

          .save-item {
            flex-direction: column;
            text-align: center;

            image {
              width: 100px;
              height: 100px;
            }

            text {
              font-size: 28px;
              font-weight: 400;
              color: #000000;
              margin-top: 20px;
              text-align: center;
            }
          }
        }

        .bottom-area {
          justify-content: space-between;
          width: 100%;

          .bottom-item {
            image {
              width: 470px;
              border-radius: 20px;
            }

            .bottom-info {
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 50px;
              background-color: rgba(0, 0, 0, 0.3);
              border-bottom-left-radius: 20px;
              border-bottom-right-radius: 20px;

              text {
                font-size: 28px;
                font-weight: 400;
                color: #ffffff;
                margin-left: 20px;
              }
            }
          }
        }
      }
    }
  }
</style>