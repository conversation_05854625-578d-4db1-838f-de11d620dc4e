// 处理条件语句指令
function processConditionalDirectives($) {
  // 处理if指令
  $('[if]').each((i, el) => {
    const $el = $(el);
    const ifAttr = $el.attr('if');

    // 移除插值语法并转换为v-if
    const vIf = ifAttr.replace(/\{\{(.*)\}\}/, '$1').trim();
    $el.attr('v-if', vIf);
    $el.removeAttr('if');
  });

  // 处理elif指令
  $('[elif]').each((i, el) => {
    const $el = $(el);
    const elifAttr = $el.attr('elif');

    // 移除插值语法并转换为v-else-if
    const vElseIf = elifAttr.replace(/\{\{(.*)\}\}/, '$1').trim();
    $el.attr('v-else-if', vElseIf);
    $el.removeAttr('elif');
  });

  // 处理else指令
  $('[else]').each((i, el) => {
    const $el = $(el);
    $el.attr('v-else', '');
    $el.removeAttr('else');
  });
}

module.exports = processConditionalDirectives