let customAlphabet;
(async () => {
  const { customAlphabet: nanoidCustomAlphabet } = await import('nanoid');
  customAlphabet = nanoidCustomAlphabet;
})();

const SeedApp = require('../model/app.js');
const { addTask } = require('../handler/seed.js');

// 创建SeedApp
const createSeedApp = async (req, res) => {
  try {
    const data = req.body;
    const generateTabId = customAlphabet('1234567890ABCDEF', 8);
    if (data.tabs?.length) {
      data.tabs = data.tabs.map((tab) => ({
        ...tab,
        tabId: generateTabId(),
      }));
    }
    // 检查必填字段
    const newRecord = new SeedApp(data);
    await newRecord.save();
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: newRecord,
    });
  } catch (error) {
    res.status(400).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 获取SeedApp列表（支持分页和搜索）
const getAllSeedApps = async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, ...filters } = req.query;
    const query = {};

    // 动态添加过滤条件
    Object.keys(filters).forEach((key) => {
      if (filters[key]) {
        query[key] = { $regex: filters[key], $options: 'i' };
      }
    });

    // 计算分页参数
    const skip = (pageNum - 1) * pageSize;

    // 查询总数
    const total = await SeedApp.countDocuments(query);

    // 查询分页数据
    const records = await SeedApp.find(query).skip(skip).limit(Number(pageSize)).sort({ createdAt: -1 });

    res.status(200).json({
      code: 0,
      msg: 'success',
      data: {
        total,
        list: records,
      },
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};
// 获取单个SeedApp
const getSeedAppById = async (req, res) => {
  try {
    const record = await SeedApp.findById(req.params.id);
    if (!record) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: record,
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 更新SeedApp
const updateSeedApp = async (req, res) => {
  try {
    const updatedRecord = await SeedApp.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updatedRecord) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: updatedRecord,
    });
  } catch (error) {
    res.status(400).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 删除SeedApp
const deleteSeedApp = async (req, res) => {
  try {
    const deletedRecord = await SeedApp.findByIdAndDelete(req.params.id);
    if (!deletedRecord) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: '删除成功',
      data: null,
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

const runSeedApp = async (req, res) => {
  try {
    const { id } = req.params; // 获取URL中的id参数
    const record = await SeedApp.findById(id); // 查找对应的SeedApp记录
    console.log('record', record)
    if (!record) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
      })
    }
    addTask(record); // 将任务添加到任务队列中
    res.status(200).json({
      code: 0,
      msg: '任务已添加到队列中',
    })
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
}

module.exports = {
  createSeedApp,
  getAllSeedApps,
  getSeedAppById,
  updateSeedApp,
  deleteSeedApp,
  runSeedApp
};
