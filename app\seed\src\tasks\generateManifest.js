// 生成manifest.json
const lodash = require('lodash');
const fs = require('fs');
const { OUT_PUT_DIR, COMMON_PAGES_LIST } = require('../constant.js');
const manifestTemplate = require('../template/manifest.json'); // 引入模板文件
const path = require('path');

function generateManifest(meta) {
    const overrides = {};   // 用于覆盖的配置对象
    const { pages } = meta
    const routerConfig = {
        pages: {}
    }
    pages.forEach(page => {                    // 添加页面路由
        const { path } = page;
        routerConfig['pages'][path] = { component: 'index' }
    })

    COMMON_PAGES_LIST.forEach(path => {              // 添加公共页面路由
        if (routerConfig['pages'][path]) {           // 避免重复添加
            return;
        }
        routerConfig['pages'][path] = { component: 'index' }
    })

    Object.assign(overrides, { name: meta.name, package: meta.package }, { router: routerConfig });

    // 深度合并，不会丢失未覆盖的深层属性
    const newConfig = lodash.merge({}, manifestTemplate, overrides);

    const manifestPath = path.join(OUT_PUT_DIR, 'manifest.json');
    fs.writeFileSync(manifestPath, JSON.stringify(newConfig, null, 2));

}

module.exports = { generateManifest }