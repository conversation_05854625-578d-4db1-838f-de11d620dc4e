<!-- create by ya<PERSON><PERSON> 2022-10-31 功能：页面标题栏组件 -->
<template>
  <!-- template里只能有一个根节点 -->
  <div
    class="common-back-header-wrapper"
    style="background-color:{{backColor}};height:128px"
  >
    <div
      class="common-back-header"
      style="padding-top:128px;height:128px"
    >
      <image
        if="backIconShow"
        @click="backClick"
        class="common-back-icon"
        src="{{textColor === '#FFFFFF'? `${baseUrl}quickapp/images/common/image/common-back-white.png` : `${baseUrl}quickapp/images/common/image/common-back-black.png` }}"
      ></image>
      <text
        if="title"
        class="common-title"
        style="color:{{textColor}};font-size:{{fontSize}}px;font-weight:{{textWeight}};text-align:{{textCenter ? 'center' : 'left'}}"
        >{{ title }}</text
      >
    </div>
  </div>
</template>
<script>
export default {
    props: {
        title: {
            type: String,
            default: ''
        },
        backColor: {
            type: String,
            default: 'transparent'
        },
        textCenter: {
            type: Boolean,
            default: true
        },
        textColor: {
            type: String,
            default: '#333333'
        },
        textWeight: {
            type: Number,
            default: 700
        },
        fontSize: {
            type: Number,
            default: 36
        },
        backIconShow: {
            type: Boolean,
            default: true
        },
        backSrc: {
            type: String,
            default: `https://test-fre.ghfkj.cn/quickapp/images/common/image/common-back-black.png`
        },
        isDefaultBack: {
            type: Boolean,
            default: true
        }
    },
    data: {
        baseUrl: 'https://test-fre.ghfkj.cn/'
    },
    onReady() {
    },

    backClick() {
        if (this.isDefaultBack) {
            require('@system.router').back()
        } else {
            this.$emit('backClick')
        }
    }
}
</script>

<style lang="less">
/* .common-back-header-wrapper { */
.common-back-header {
  width: 750px;
  flex-direction: row;
  padding: 0 20px;
  align-items: center;
  .common-back-icon {
    width: 60px;
    height: 48px;
    padding: 0 20px;
    object-fit: cover;
  }
  .common-title {
    font-size: 34px;
    font-weight: bold;
    text-align: center;
    flex: 1;
    padding-right: 68px;
    lines: 1;
    text-overflow: ellipsis;
  }
  .gold-box {
    width: 188px;
    height: 46px;
    align-items: center;
    padding-left: 28px;
    position: relative;
    .gold-bg {
      width: 168px;
      height: 42px;
      background-color: rgba(0, 0, 0, 0.3);
      border-top-right-radius: 22px;
      border-bottom-right-radius: 22px;
    }
    .gold-icon {
      width: 46px;
      height: 46px;
      position: absolute;
      left: 0;
      top: 0;
    }
    .gold-process {
      height: 42px;
      background: linear-gradient(90deg, #ff8c07, #ffc907 100%);
      border-top-right-radius: 22px;
      border-bottom-right-radius: 22px;
    }
    .gold-num {
      font-size: 24px;
      color: #fff;
      width: 168px;
      text-align: center;
      padding-left: 22px;
    }
  }
}
/* } */
</style>
