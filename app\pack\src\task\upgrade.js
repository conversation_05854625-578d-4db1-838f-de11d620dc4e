const user = require('../model/user')
const { Worker } = require('worker_threads');
const path = require('path');

async function upgrade(req, res) {
    const taskList = req.body
    const { userId } = req.user
    const role = await user.findOne({ id: userId })
    const workerPath = path.join(__dirname,'../handler/upgrade-worker.js')
    const worker = new Worker(workerPath);

    worker.postMessage(taskList, role);

    worker.on('message', (result) => {
        // eslint-disable-next-line default-case        
        switch (result.type) {
            case 'success':
                this.taskComplete(task, result);
                resolve(task);
                break;
            case 'error':
                this.taskError(task, result.message, result.errorCode);
                reject(result.message);
                break;
        }
    });
    res.send('upgrade')
}



module.exports = {upgrade}