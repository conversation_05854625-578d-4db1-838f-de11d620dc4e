const axios = require('axios');
const qs = require('qs');
const AppVersion = require('../../model/version');
const { saveRpk } = require('../unzip');

/**
 * 搜索单个 APP，返回版本信息
 * @param appName
 * @param packageName
 * @return {Promise<*|null>}
 */
async function searchAppVersion(appName, packageName, needAll) {
  const searchUrl = `https://center.hybrid.xiaomi.com/v2/app/search/list?deviceType=1&keywords=${encodeURIComponent(
    appName
  )}`;

  try {
    const response = await axios.get(searchUrl);

    if (needAll) {
      return response.data.data;
    }

    if (response.data.code === 0 && response.data.data.length > 0) {
      // 查找最匹配的app
      return response.data.data.find((item) => item.appName === appName && item.packageName === packageName);
    }

    return null;
  } catch (error) {
    console.log(error);
    return null;
  }
}

/**
 * 更具 appName 获取下载链接
 * @param packageName
 * @return {Promise<{}|{link: (*|null), company: (string|null), packageName, version: (*|string)}>}
 */
async function getAppDownloadHref(packageName) {
  try {
    const url = qs.stringify({
      device_model: 'dipper',
      deviceIdType: 'oaid',
      is_global: false,
      check_whitelist: false,
      deviceId: '52e1dcfbf9bc8fca',
      hybrid_version_code: '11060601',
      os_version_type: 'V12.5.2.0.QEACNXM(stable)',
      android_os_version: 29,
      platform_version: 1106,
      imei_md5: '',
      guid: 'fcd28f5d-79d0-4419-995b-1c2e3aa5d099',
      region: 'CN',
      network_type: 'wifi',
      oaid: '52e1dcfbf9bc8fca',
      screen_width: 1080,
      screen_height: 2115,
      screen_density: 2.75,
      app_version_code: -1,
      server_settings_last_modify: -1,
      package_name: packageName,
      device_type: 'phone',
    });
    let { data } = await axios.post(`https://api.hybrid.xiaomi.com/api/app/query.v2?${url}`);

    if (typeof data === 'string') {
      const encoder = new TextEncoder();
      data = encoder.encode(data);
    }

    const decodedData = new TextDecoder('utf-8').decode(new Uint8Array(data));
    const linkPattern = /https:\/\/cdn\.hybrid\.xiaomi\.com\/quickapp-c3\/[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)?/g;
    const companyPattern = /[\u4e00-\u9fa5a-zA-Z0-9]+有限公司/;

    const links = decodedData.match(linkPattern);
    const filteredLinks = links
      ? links.filter(
          (link) =>
            !link.endsWith('.png') && !link.endsWith('.jpg') && !link.endsWith('.jpeg') && !link.endsWith('.webp')
        )
      : [];
    const company = decodedData.match(companyPattern);
    const version = extractVersion(decodedData);

    return {
      link: filteredLinks.length > 0 ? filteredLinks[0] : null,
      company: company ? company[0] : null,
      packageName,
      version,
    };
  } catch (error) {
    console.error('Error fetching app details:', error);
    return {};
  }
}

function extractVersion(input) {
  // 使用正则表达式匹配版本号
  const versionPattern = /\d+(\.\d+)+/g;
  const matches = input.match(versionPattern);

  // 如果找到匹配项，返回第一个匹配的版本号
  if (matches && matches.length > 0) {
    return matches[0];
  }

  // 如果没有找到版本号，返回 null
  return '';
}

function saveXMVersion(apps) {
  return Promise.all(
    apps.map(async (app) => {
      // 搜索是否有此 APP
      const matchedApp = await searchAppVersion(app.name, app.packageName);
      if (!matchedApp) return;

      // 判断数据库是否已有此版本
      const existingVersion = await AppVersion.findOne({
        name: app.name,
        version: matchedApp.appVersionName,
        brand: '小米',
      });
      if (existingVersion) return;

      // 没有再获取下载链接，入库
      const { link, version } = await getAppDownloadHref(matchedApp.packageName);
      if (!version) return;
      // 创建新版本
      await AppVersion.create({
        name: app.name,
        version,
        appLink: link,
        brand: '小米',
      });
      saveRpk(link, `${app.name}-${version}-小米`);
    })
  );
}

module.exports = {
  searchAppVersion,
  getAppDownloadHref,
  saveXMVersion,
};
