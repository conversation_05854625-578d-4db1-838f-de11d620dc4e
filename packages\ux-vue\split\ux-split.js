const parser = require('@babel/parser');
const parse5 = require('parse5');

function parseVueFile(content) {
  const sections = {
    template: null,
    script: null,
    styles: [],
    imports: [],
  };

  const regex = /<(?<tag>template|script|style|import)(?<attributes>[^>]*)>(?<content>[\s\S]*?)<\/\1>/g;
  let match;

  while ((match = regex.exec(content)) !== null) {
    const { tag, content: tagContent, attributes } = match.groups;
    const start = match.index;
    const end = regex.lastIndex;
    // 内容的起始 index 是标签闭合符号 '>' 之后的位置
    const contentStartIndex = start + match[0].indexOf(tagContent.trim());
    const contentEndIndex = contentStartIndex + tagContent.trim().length;

    switch (tag) {
      case 'template':
        sections.template = {
          type: 'template',
          content: tagContent.trim(),
          attrs: parseAttributes(attributes.trim()),
          start,
          end,
          contentStartIndex,
          contentEndIndex,
        };
        break;
      case 'script':
        sections.script = {
          type: 'script',
          content: tagContent.trim(),
          attrs: parseAttributes(attributes.trim()),
          start,
          end,
          contentStartIndex,
          contentEndIndex,
        };
        break;
      case 'style':
        sections.styles.push({
          type: 'style',
          content: tagContent.trim(),
          attrs: parseAttributes(attributes.trim()),
          start,
          end,
          contentStartIndex,
          contentEndIndex,
        });
        break;
      case 'import':
        sections.imports.push({
          type: 'import',
          content: tagContent.trim(),
          attrs: parseAttributes(attributes.trim()),
          start,
          end,
          contentStartIndex,
          contentEndIndex,
        });
        break;
      default:
        break;
    }
  }

  return sections;
}

function parseAttributes(attributeString) {
  const attributes = {};
  const attrRegex = /(\S+)=["']?((?:.(?!["']?\s+(?:\S+)=|[>"']))+.)["']?/g;
  let attrMatch;

  while ((attrMatch = attrRegex.exec(attributeString)) !== null) {
    attributes[attrMatch[1]] = attrMatch[2] || true;
  }

  return attributes;
}

function astReplace(uxCode, replacer) {
  const sfc = parseVueFile(uxCode);
  const scriptCode = sfc.script.content;
  const ast = parser.parse(scriptCode, {
    sourceType: 'module',
    plugins: ['asyncFunctions'],
  });

  /**
   * @type {{start: number, end: number, content: string, type: string}[]}
   * type {insert, remove, replace}
   */
  const replaceList = replacer(ast, scriptCode);

  let modifiedJsCode = baseReplaceHandler(scriptCode, replaceList);

  return uxCode.slice(0, sfc.script.contentStartIndex) + modifiedJsCode + uxCode.slice(sfc.script.contentEndIndex);
}

function importReplace(uxCode, replacer) {
  const sfc = parseVueFile(uxCode);
  const replaceList = replacer(sfc.imports);

  return baseReplaceHandler(uxCode, replaceList);
}

function templateReplace(uxCode, replacer) {
  const sfc = parseVueFile(uxCode);
  const ast = parse5.parse(sfc.template.content, {
    sourceCodeLocationInfo: true,
  });

  const replaceList = replacer(ast, sfc.template.content);

  const templateCode = baseReplaceHandler(sfc.template.content, replaceList);

  return uxCode.slice(0, sfc.template.contentStartIndex) + templateCode + uxCode.slice(sfc.template.contentEndIndex);
}

function baseReplaceHandler(code, replaceList) {
  replaceList.sort((a, b) => b.start - a.start); // 从后往前插入，避免索引偏移

  let modifiedJsCode = code;

  replaceList.forEach(({ start, end, content, type }) => {
    switch (type) {
      case 'remove': {
        modifiedJsCode = modifiedJsCode.slice(0, start) + modifiedJsCode.slice(end);
        break;
      }
      case 'insert': {
        modifiedJsCode = modifiedJsCode.slice(0, start) + content + modifiedJsCode.slice(start);
        break;
      }
      case 'replace': {
        modifiedJsCode = modifiedJsCode.slice(0, start) + content + modifiedJsCode.slice(end);
      }
    }
  });

  return modifiedJsCode;
}

function htmlAstTraver(node, visitors) {
  // 处理不同类型的节点
  if (visitors[node.nodeName]) {
    visitors[node.nodeName](node);
  }

  // 遍历子节点
  if (node.childNodes) {
    for (let child of node.childNodes) {
      htmlAstTraver(child, visitors);
    }
  }
}

module.exports = {
  astReplace,
  parseVueFile,
  importReplace,
  htmlAstTraver,
  templateReplace,
};
