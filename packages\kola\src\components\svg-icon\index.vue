<template>
  <svg :class="svgClass" :style="svgStyle" aria-hidden="true">
    <use :xlink:href="iconName" />
  </svg>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  const props = defineProps<{
    icon: string;
    className?: string;
    size?: string;
    color?: string;
  }>();
  const svgClass = computed(() => `svg-icon ${props.className ?? ''}`);
  const svgStyle = computed(() => {
    let style = '';
    if (props.size) style += `width: ${props.size}px; height: ${props.size}px;`;
    if (props.color) style += `color: ${props.color};`;
    return style;
  });
  const iconName = computed(() => `#icon-${props.icon}`);
</script>

<script setup lang="ts"></script>

<style>
  .svg-icon {
    width: 2em;
    height: 2em;
    vertical-align: -0.15em;
    fill: currentcolor;
    overflow: hidden;
  }
</style>

<style scoped></style>
