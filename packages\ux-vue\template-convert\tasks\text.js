function replaceTextTags($) {
  // 查找所有<text>标签
  $('text').each((index, element) => {
    const $element = $(element);

    // 创建新的<s-text>标签
    const newElement = $('<s-text>');

    // 复制原标签的所有属性
    Object.keys(element.attribs).forEach((attr) => {
      newElement.attr(attr, element.attribs[attr]);
    });

    // 复制原标签的内容
    newElement.html($element.html());

    // 替换原标签
    $element.replaceWith(newElement);
  });
}

module.exports = replaceTextTags;
