import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const DASHBOARD: AppRouteRecordRaw = {
  path: '/user',
  name: 'user',
  components: {
    default: DEFAULT_LAYOUT,
    content: () => import('@/views/user/index.vue'),
  },
  meta: {
    name: '用户管理',
    locale: 'menu.dashboard',
    requiresAuth: true,
    icon: 'icon-user',
    order: 4,
    roles: ['admin'],
  },
};

export default DASHBOARD;
