<template>
    <div style=" width: 100%;height: 250px;">
        <swiper
            indicator="false"
            autoplay="{{autoPlay}}"
            style=" width: 100%;height: 100%;"
            @change="swiperChange"
            id="swiper"
        >
            <image
                for="(index,item) in imgList"
                class="swiper-item"
                src="{{item}}"
            ></image>
        </swiper>
        <div class="indicator-area">
                <text
                for="(index,item) in indicatorArr"
                class="{{index == bannerIndex ? 'active' : ''}}"
                ></text>
        </div>
    </div>
</template>

<script>
export default {
  props:{
    imgList: { // 图片列表
      type: Array,
      default: []
    },
    autoPlay: { // 图片列表
      type: Boolean,
      default: true
    },
  },
  data:{
    bannerIndex: 0, // 当前banner索引
  },
  computed: { // 指示器数组
    indicatorArr() {
      return this.imgList.map((item, index) => ({ index }))
    }
  },
  swiperChange(evt) {
    if (typeof evt.index !== 'undefined') {
      this.bannerIndex = Number(evt.index)
    }
  },
  onInit() {
    console.log(this.imgList)
  }
}
</script>

<style lang="less" scoped>
.swiper-item{
    width: 100%;
    height: 100%;
    border-radius: 20px;
}
.indicator-area {
  position: absolute;
  bottom: 20px;
  width: 100%;
  justify-content: center;
  align-items: center;
  left: 30px;
  text {
    width: 10px;
    height: 10px;
    background-color: #f9f9f9;
    border-radius: 50%;
    margin: 0 5px;
  }
  .active {
    width: 16px;
    height: 16px;
  }
}
</style>