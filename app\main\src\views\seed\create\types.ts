// 组件类型定义
export interface Component {
  id: string;
  name: string;
  label: string;
  props: Record<string, any>;
  styles: Record<string, any>;
}

// 页面类型定义
export interface Page {
  id: string;
  name: string;
  components: Component[];
}

// 页面配置类型定义// 基础页面组件类型
export interface PageComponent {
  id: string;
  label: string;
  pinyin: string;
  props: {
    [key: string]: string | string[];
  };
}
// 页面配置基础类型（提取公共部分）
interface BasePageConfig {
  pageName: string;  // 去掉引号，改为实际类型
  pageId: string;
  themeColor: string;
  backgroundColor: string;
  path: string;
  components: PageComponent[];
}
// 主配置接口
export interface PagesConfig {
  name: string;
  packageName: string;
  icon: string;
  privacyUrl: string;
  agreementUrl: string;
  main: BasePageConfig[];  // 使用BasePageConfig替代重复定义
  subPages: BasePageConfig[];  // 同样使用BasePageConfig
}
// 工具栏按钮类型定义
export interface ToolbarButton {
  key: string;
  title: string;
  type: 'primary' | 'secondary' | 'outline' | 'text';
  icon: string;
  disabled?: boolean;
  handler: () => void;
} 