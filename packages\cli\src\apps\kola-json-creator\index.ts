const path = require('path');
const fs = require('fs');
const process = require('process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const projectDirRootPath = path.resolve(process.cwd(), '../../apps'); // Users/cy/workspace/dmp_admin
const sourceTemplatePath = path.join(path.parse(__dirname).dir, path.parse(__dirname).base, 'template/dynamicPage/');

const projectOptions = ['tactic', 'adp'];
const typeOptions = ['table', 'form'];

(async () => {
  try {
    const projectAnswer = await createPromptList('choose Project directory', rl, projectOptions, false);

    const targetPath = path.join(projectDirRootPath, projectAnswer, 'src/views');
    const targetDirList = getTargetPathOptions(targetPath);

    const answer = await createPromptList('choose target directory? ', rl, targetDirList, false);

    await new Promise((resolve) => {
      rl.question('write target directory name？', (nameAnswer) => {
        const isCreate = createDir(answer, nameAnswer);
        if (isCreate) {
          createPromptList('type？', rl, typeOptions, false).then((res) => {
            copyFolderRecursive(sourceTemplatePath, path.join(answer, nameAnswer));
            console.log('创建成功!');
            rl.close();
            process.exit(0);
          });
        } else {
          console.log('文件名重复，创建失败~');
          rl.close();
          process.exit(0);
        }
        resolve();
      });
    });
  } catch (error) {
    console.error(`出现错误: ${error.message}`);
  }
})();

function getTargetPathOptions(source) {
  const fileList = [];
  const files = fs.readdirSync(source);
  files.forEach((fileItem) => {
    const itemPath = path.join(source, fileItem);
    if (fs.statSync(itemPath).isDirectory()) {
      fileList.push(itemPath);
    }
  });
  return fileList;
}

function createDir(sourcePath, name) {
  const dirList = fs.readdirSync(sourcePath).filter((file) => fs.statSync(path.join(sourcePath, file)).isDirectory());
  if (dirList.includes(name)) {
    return false;
  }

  let isFlag = true;
  try {
    fs.mkdirSync(path.join(sourcePath, name));
  } catch (err) {
    isFlag = false;
  }
  return isFlag;
}

function copyFolderRecursive(source, destination) {
  fs.mkdirSync(destination, { recursive: true });

  const files = fs.readdirSync(source);

  files.forEach((file) => {
    const sourcePath = path.join(source, file);
    const destinationPath = path.join(destination, file);

    const stats = fs.statSync(sourcePath);

    if (stats.isDirectory()) {
      copyFolderRecursive(sourcePath, destinationPath);
    } else {
      fs.copyFileSync(sourcePath, destinationPath);
    }
  });
  return true;
}

// eslint-disable-next-line no-shadow
async function createPromptList(text, rl, options, needClose = false) {
  return new Promise((resolve) => {
    let selectedIndex = 0;

    function displayOptions(init = false) {
      !init && clearLines(options.length);
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < options.length; i++) {
        if (i === selectedIndex) {
          // eslint-disable-next-line prefer-template
          console.log('> ' + options[i]);
        } else {
          // eslint-disable-next-line prefer-template
          console.log('  ' + options[i]);
        }
      }
    }

    function clearLines(count) {
      process.stdout.cursorTo(0);

      process.stdout.moveCursor(0, -count);

      process.stdout.clearScreenDown();
    }

    console.log(text);
    displayOptions(true);

    const keyInputFunc = (str, key) => {
      if (key.name === 'up') {
        selectedIndex = (selectedIndex - 1 + options.length) % options.length;
        displayOptions();
      } else if (key.name === 'down') {
        selectedIndex = (selectedIndex + 1) % options.length;
        displayOptions();
      } else if (key.name === 'return') {
        clearLines(1);
        displayOptions();
        resolve(options[selectedIndex]);
        rl.input.off('keypress', keyInputFunc);
        needClose && rl.close();
      }
    };
    rl.input.on('keypress', keyInputFunc);

    rl.input.setRawMode(true);
    rl.input.resume();
  });
}
