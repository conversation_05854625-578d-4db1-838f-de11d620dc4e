const { TASK_STATUS } = require('../../constant');
const { generateUniqueId } = require('../../utils/tools');
const handler = require('../../handler/pack');
const Task = require('./base');

class NTask {
  constructor() {
    this.maxTaskCount = 1;
    this.taskQueue = [];
    this.excutingQueue = [];
  }

  async addTaskList(paramsList,role) {
    return new Promise((resolve, reject) => {
      const resultList = [];
      const taskId = generateUniqueId();
      paramsList.forEach((params) => {
        const validationResult = this.#isTaskValid(params);
        if (!validationResult.isValid) {
          resultList.push({
            reason: validationResult.reason,
            type: 'fail',
            key: params.key,
          });
          return;
        }
        resultList.push({ type: 'success', key: params.key });
        const task = new Task();
        task.init(params, taskId, role);
        this.taskQueue.push(task);
      });
      resolve({ taskId, resultList });
    });
  }

  async startTask(callback) {
    if (this.excutingQueue.length >= this.maxTaskCount || this.taskQueue.length === 0) {
      return;
    }
    const task = this.taskQueue.shift();
    task.status = TASK_STATUS.EXECUTING;
    this.excutingQueue.push(task);
    handler
      .pack(task)
      .then(() => {
        this.excutingQueue.shift();
        callback(task);
        this.startTask(callback);
      })
      .catch((error) => {
        this.excutingQueue.shift();
        callback(task);
        this.startTask(callback);
      });
  }

  /**
   * 参数校验
   * @param {Object} params
   * @returns {Boolean} status
   */
  #isTaskValid(params) {
    const { brand, appName, key } = params;
    const missingParams = [];
    if (!brand) missingParams.push('brand');
    if (!appName) missingParams.push('appName');
    if (!key) missingParams.push('key');
    if (missingParams.length > 0) {
      return {
        isValid: false,
        reason: `Task is invalid. Missing required parameters: ${missingParams.join(', ')}`,
      };
    }
    return { isValid: true, reason: 'task is valid' };
  }
}

module.exports = new NTask();
