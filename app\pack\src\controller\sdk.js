const { execSync } = require('child_process');
const path = require('path');

const targetDir = path.join(__dirname, '../../build/qkproject');

async function updateAdInterface(req, res) {
  await execSync('npm update ad-interface', {
    stdio: 'inherit',
    cwd: targetDir,
  });

  res.send({ status: true, result: 'success', timeStamp: Date.now() });
}

// 获取应用列表信息
async function fetchSdkGitLabBranches(req, res) {
  console.log('fetchSdkGitLabBranches');
  try {
    let allData = [];
    let page = 1;
    let hasMoreData = true;

    while (hasMoreData) {
      const response = await fetch(
        `https://gitlab.ghfkj.cn/api/v4/projects/112/repository/branches?per_page=100&page=${page}&search=^release`,
        {
          headers: {
            'PRIVATE-TOKEN': '**************************',
          },
        }
      );
      const pageData = await response.json();
      
      if (pageData.length === 0) {
        hasMoreData = false;
      } else {
        allData = [...allData, ...pageData];
        page++;
      }
    }
    const data = allData;
    const regex = /^release-v\d(?:\.(\d+))+[a-z]?$/;
    const releaseBranches = data.filter((branche) => regex.test(branche.name)).map((branche) => branche.name);

    const sdkVersion = sortVersions(releaseBranches);

    res.send({ status: true, data: sdkVersion, timeStamp: Date.now() });
  } catch (e) {
    console.log(e);
    res.send({ status: false, data: '', timeStamp: Date.now() });
  }
}

/**
 * 根据版本字符串的最后四个数字组件对数组进行降序排序。
 *
 * @param {string[]} versions - 版本字符串数组，格式为 'release-vX.X.X.X'。
 * @returns {string[]} 一个新的排序后的版本字符串数组。
 */
function sortVersions(versions) {
  return versions.sort((a, b) => {
    const versionA = a.replace('release-v', '').split('.').map(Number);
    const versionB = b.replace('release-v', '').split('.').map(Number);

    // 填充版本号，使其长度一致
    while (versionA.length < 4) versionA.push(0);
    while (versionB.length < 4) versionB.push(0);

    // 逐位比较版本号
    for (let i = 0; i < 4; i++) {
      if (versionA[i] !== versionB[i]) {
        return versionB[i] - versionA[i];
      }
    }
    return 0; // 相等
  });
}

module.exports = {
  fetchSdkGitLabBranches,
  updateAdInterface,
};
