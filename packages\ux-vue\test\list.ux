<template>
  <div class="powersaving">
    <div class="content">
      <div class="device-info">
        <text class="device-title">手机信息</text>
        <div class="device-area">
          <image src="{{path}}quickapp/app/ydpower/images/phone.png" class="phone-icon"></image>
          <div class="phone-info">
            <text class="phone-brand">{{ deviceInfo.brand }}</text>
            <text class="phone-system"
            >{{ deviceInfo.osType }} {{ deviceInfo.osVersionName }}</text
            >
          </div>
        </div>
        <div class="screen-info">
          <div class="info-item">
            <text class="info-num">{{ deviceInfo.screenWidth }}</text>
            <text class="info-text">屏幕宽度</text>
          </div>
          <div class="info-item">
            <text class="info-num">{{ deviceInfo.screenHeight }}</text>
            <text class="info-text">屏幕高度</text>
          </div>
          <div class="info-item">
            <text class="info-num">{{ languageText }}</text>
            <text class="info-text">系统语言</text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import getLanguageFun from "../utils/languageTrans.js"
import prompt from '@system.prompt'
import bluetooth from '@system.bluetooth'
import DEVICE_UTILS from '../utils/device_utils.js'

const  __BUS_PUBLIC_OSS_BASE_URL__ = "https://static.chengyouyun.com/"

export default {
  data: {
    deviceInfo: "",
    languageText: '',
    isXiaomi: true,
    path:__BUS_PUBLIC_OSS_BASE_URL__,
  },
  onReady() {
    let that = this
    DEVICE_UTILS.getInfo().then(res => {
      console.log("当前设备信息：", res)
      res && (this.deviceInfo = res)
      this.languageText = getLanguageFun.getLanguageFun(res.language)
      let tempBrand = res.brand.toLowerCase()
      if (tempBrand == 'redmi' || tempBrand == 'xiaomi' || tempBrand == '小米' || tempBrand == 'Redmi' || tempBrand == '小米红米') {
        this.isXiaomi = false
      }
    })
  },
}
</script>
<style lang="less">
.powersaving {
  flex-direction: column;
  background-color: #f7f7f7;
  .top-bg {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    width: 750px;
    height: 228px;
    background: linear-gradient(180deg, #e4ffe6, rgba(245, 245, 245, 0) 100%);
  }
  .content {
    flex-direction: column;
    padding: 30px;
    .device-info {
      margin-top: 20px;
      flex-direction: column;
      background-color: #ffffff;
      border-radius: 20px;
      /* box-shadow: 0px 8px 16px 0px rgba(204, 204, 204, 0.2); */
      padding: 35px 25px;
      .device-title {
        font-size: 32px;
        font-weight: 600;
        color: #000000;
      }
      .device-area {
        margin: 60px auto;
        margin-bottom: 70px;
        /* justify-content: center; */
        .phone-icon {
          width: 49px;
        }
        .phone-info {
          flex-direction: column;
          margin-left: 27px;
          .phone-brand {
            font-size: 40px;
            font-weight: 600;
            color: #000000;
            line-height: 40px;
          }
          .phone-system {
            font-size: 28px;
            line-height: 28px;
            font-weight: 400;
            color: #bebebe;
            margin-top: 20px;
          }
        }
      }
      .screen-info {
        justify-content: space-between;
        width: 80%;
        margin: 0 auto;
        .info-item {
          flex-direction: column;
          .info-num {
            font-size: 28px;
            font-weight: 600;
            color: #333333;
            text-align: center;
            line-height: 28px;
          }
          .info-text {
            font-size: 24px;
            font-weight: 400;
            color: #c0c0c0;
            margin-top: 20px;
            text-align: center;
            line-height: 24px;
          }
        }
      }
    }
    
  }
}
</style>

