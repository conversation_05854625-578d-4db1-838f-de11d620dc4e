import { brandList } from '@/constants/pack';
import { getAppList, getSdkVersion } from '@/api/auto-pack';

const getFilter = () => {
  return {
    formSchema: {
      fields: [
        {
          label: '产品名称',
          name: 'appName',
          type: 'select',
          format: 'singleSelect',
          select: {
            allowSearch: true,
            allowClear: true,
          },
          placeholder: '请输入产品名称',
          source: {
            data: () => {
              return getAppList().then((res) => {
                return res.data;
              });
            },
          },
        },
        {
          label: '厂商',
          name: 'brand',
          placeholder: '全部',
          type: 'select',
          format: 'singleSelect',
          select: {
            allowSearch: true,
            allowClear: true,
          },
          source: {
            data: brandList,
          },
        },
        {
          label: 'sdk版本',
          name: 'sdkVersion',
          type: 'select',
          format: 'singleSelect',
          select: {
            allowSearch: true,
            allowClear: true,
          },
          placeholder: '请输入sdk版本',
          source: {
            data: () => {
              return getSdkVersion();
            },
          },
        },
        {
          label: '创建人',
          name: 'username',
          type: 'text',
        },
      ],
    },
  };
};

export default getFilter;
