const fs = require('fs');
const path = require('path');
const os = require('os');
const AdmZip = require('adm-zip');

const { deobfuscate } = require('obfuscator-io-deobfuscator');
const archiver = require('archiver');

const ProcessPool = require('../handler/process-pool');
const { processFiles } = require('../utils/decode');

const processPool = new ProcessPool(os.cpus().length - 2); // 创建最大 4 进程的进程池

// 处理文件

// 创建 ZIP 文件
const createZip = (sourceDir) => {
  const zip = new AdmZip();
  zip.addLocalFolder(sourceDir);
  return zip.toBuffer();
};

// 解压 ZIP 文件
const extractZip = (buffer, targetDir) => {
  const zip = new AdmZip(buffer);
  zip.extractAllTo(targetDir, true);
};

// 清理临时文件夹
const clearTemp = (tempDir) => {
  fs.rmSync(tempDir, { recursive: true, force: true });
};

// 主处理函数
const processUpload = async (fileBuffer, decode) => {
  const tempDir = path.join(__dirname, 'temp', Date.now().toString());
  const extractedDir = path.join(tempDir, 'extracted');
  const rootDir = path.join(tempDir, 'processed');

  // 创建必要目录
  fs.mkdirSync(extractedDir, { recursive: true });
  fs.mkdirSync(rootDir, { recursive: true });

  try {
    // 解压上传的 ZIP 文件
    extractZip(fileBuffer, extractedDir);

    const processList = [];
    // 处理解压后的文件
    processFiles(extractedDir, rootDir, decode, processList);

    // 调用进程池处理每段代码
    const results = await Promise.all(
      processList.map((data) =>
        processPool.runTask(data, path.resolve(__dirname, '../handler/process-pool/decode-worker.js'))
      )
    );

    // 打包处理后的文件
    return createZip(rootDir);
  } finally {
    // 清理临时文件
    clearTemp(tempDir);
  }
};

// 处理上传的文件
async function decodeZip(req, res) {
  try {
    const zipBuffer = await processUpload(req.file.buffer, deobfuscate);

    // 设置响应头并发送文件
    res.set({
      'Content-Type': 'application/zip',
      'Content-Disposition': 'attachment; filename="processed_files.zip"',
      'Content-Length': zipBuffer.length,
    });
    res.send(zipBuffer);
  } catch (error) {
    console.error(error);
    res.status(500).send(`Error processing file: ${error.message}`);
  }
}

function testStream(req, res) {
  const rootDir = path.resolve(__dirname, './temp/1735288178365/processed');

  const archive = archiver('zip', { zlib: { level: 9 } });
  archive.on('error', (err) => res.status(500).send({ error: err.message }));

  archive.pipe(res); // 直接将压缩流写入响应
  archive.directory(rootDir, false); // 添加目录内容
  archive.finalize().then(() => {
    console.log('final');
  });
}

function testStream2(req, res) {
  res.setHeader('Content-Type', 'application/octet-stream');
  fs.createReadStream(path.resolve(__dirname, './temp/1735288178365/extracted/app.js')).pipe(res);
}

module.exports = {
  decodeZip,
  testStream,
  testStream2,
};
