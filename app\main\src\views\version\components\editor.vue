<template>
  <div ref="editorContainer" class="monaco-editor-container"></div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
  import * as monaco from 'monaco-editor';

  const props = defineProps({
    code: {
      type: String,
      default: '', // 默认代码
    },
    diffCode: {
      type: String,
      default: '',
    },
    language: {
      type: String,
      default: 'javascript', // 默认语言
    },
    theme: {
      type: String,
      default: 'vs', // 默认主题
    },
    options: {
      type: Object,
      default: () => ({}), // 编辑器配置
    },
  });

  const editorContainer = ref(null);
  let editor: any = null;

  // 初始化编辑器
  // 初始化diff编辑器
  function initDiffEditor() {
    const originalModel = monaco.editor.createModel(props.code, props.language);
    const modifiedModel = monaco.editor.createModel(
      props.diffCode,
      props.language
    );

    editor = monaco.editor.createDiffEditor(editorContainer.value!, {
      readOnly: true, // 设置为只读
      theme: 'vs',
      renderSideBySide: true, // 并排显示
      enableSplitViewResizing: false, // 禁止调整分割线
      renderIndicators: true,
      ignoreTrimWhitespace: true,
    });

    editor.setModel({
      original: originalModel,
      modified: modifiedModel,
    });
  }

  // 更新diff代码
  function updateDiffCode(newCode: string) {
    if (editor) {
      const modifiedModel = editor.getModifiedEditor().getModel();
      if (modifiedModel) {
        modifiedModel.setValue(newCode);
      }
    }
  }

  // 更新原始代码
  function updateOriginalCode(newCode: string) {
    if (editor) {
      const originalModel = editor.getOriginalEditor().getModel();
      if (originalModel) {
        originalModel.setValue(newCode);
      }
    }
  }

  // 更新语言
  function updateLanguage(newLanguage: string) {
    if (editor) {
      monaco.editor.setModelLanguage(editor.getModel(), newLanguage);
    }
  }

  // 更新主题
  function updateTheme(newTheme: string) {
    if (editor) {
      monaco.editor.setTheme(newTheme);
    }
  }

  // 生命周期钩子
  onMounted(() => {
    initDiffEditor();
  });

  onBeforeUnmount(() => {
    if (editor) {
      editor.dispose(); // 销毁编辑器实例
    }
  });

  // 监听属性变化
  watch(
    () => props.code,
    (newCode) => {
      updateOriginalCode(newCode);
    }
  );

  // 监听属性变化
  watch(
    () => props.diffCode,
    (newCode) => {
      updateDiffCode(newCode);
    }
  );

  watch(
    () => props.language,
    (newLanguage) => {
      console.log('newLanguage', newLanguage);
      updateLanguage(newLanguage);
    }
  );

  watch(
    () => props.theme,
    (newTheme) => {
      console.log('newTheme', newTheme);
      updateTheme(newTheme);
    }
  );
</script>

<style scoped>
  .monaco-editor-container {
    width: 100%;
    height: 500px; /* 设置编辑器高度 */
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
  }
</style>
