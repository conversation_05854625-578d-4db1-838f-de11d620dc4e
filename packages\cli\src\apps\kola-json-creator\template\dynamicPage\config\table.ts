import { formSchema } from './operation';

const editOperation = {
  text: '编辑',
  props: {
    type: 'text',
    disabled: (data) => {
      return data.record.associationCount > 0;
    },
  },
  clickActionType: 'modal',
  modal: {
    props: {
      title: '编辑唤醒路径',
      width: 700,
      fullscreen: false,
    },
    contentType: 'form',
    getDefaultValue: (record: any) => {
      return [];
    },
    form: {
      formSchema,
    },
    action({ refreshTable }: any) {
      refreshTable();
    },
  },
};
const deleteOperation = {
  text: '删除',
  props: {
    type: 'text',
  },
  clickActionType: 'modal',
  modal: {
    props: {
      title: '提示',
      width: 500,
      fullscreen: false,
    },
    contentType: 'text',
    text: '确认删除吗？',
    action: ({ refreshTable, record }) => {
      refreshTable();
    },
  },
};

const operationColumns = {
  title: '操作',
  dataIndex: 'operations',
  width: 250,
  customRender: {
    type: 'operations',
    width: '300px',
    props: {
      operations: [editOperation, deleteOperation],
    },
  },
};
export default {
  load: {
    action: async (filter, pagination) => {
      return [];
    },
  },
  columns: [
    {
      title: '更新时间',
      dataIndex: 'updateAt',
    },
    operationColumns,
  ],
};
