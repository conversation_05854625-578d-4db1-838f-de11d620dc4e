<template>
  <div class="center-panel">
    <div class="phone-frame" :class="{ 'show-grid': showGrid }">
      <div class="phone-content" :style="{ backgroundColor }">
        <draggable
          style="min-height: 1100px"
          :model-value="localRenderList"
          @update:modelValue="onRenderListChange"
          :group="{ name: 'components', pull: true, put: true }"
          item-key="id"
        >
          <template #item="{ element }">
            <div
              class="render-item"
              :class="{ active: selectedComponentId === element.id }"
              :style="getComponentStyles(element.styles)"
              @click="handleSelectComponent(element)"
            >
              <div class="component-wrapper">
                <component
                  :is="element.name"
                  v-bind="formatProps(element.props)"
                  :style="element.styles"
                />
              </div>
              <div class="render-item-actions">
                <a-button
                  type="text"
                  size="large"
                  class="delete-btn"
                  @click.stop="$emit('deleteComponent', element)"
                >
                  <template #icon>
                    <icon-delete style="font-size: 30;" />
                  </template>
                </a-button>
              </div>
            </div>
          </template>
        </draggable>
      </div>
      <!-- 移动端 tabbar -->
      <div class="mobile-tabbar">
        <div
          v-for="page in pageList"
          :key="page.id"
          class="tabbar-item"
          :class="{ active: activePageId === page.id }"
          @click="onPageChange(page.id)"
        >
          <div class="tabbar-icon">
            <icon-apps />
          </div>
          <div class="tabbar-text">{{ page.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import { IconDelete, IconApps } from '@arco-design/web-vue/es/icon';
  import draggable from 'vuedraggable';
  import GridSwitch from '../../../components/grid-switch.vue';

  const props = defineProps({
    renderList: Array,
    selectedComponentId: String,
    pageList: {
      type: Array,
      default: () => [],
    },
    currentPageId: String,
    currentPage: {
      type: Object,
      default: () => null,
    },
  });

  const emit = defineEmits([
    'selectComponent',
    'deleteComponent',
    'update:renderList',
    'update:currentPageId',
  ]);

  const showGrid = ref(true);
  const localRenderList = ref([...props.renderList]);
  const activePageId = ref(props.currentPageId);

  // 使用computed来处理背景颜色
  const backgroundColor = computed(() => {
    console.log('计算背景色，currentPage:', props.currentPage);
    return props.currentPage?.backgroundColor || '#ffffff';
  });

  watch(
    () => props.renderList,
    (val) => {
      localRenderList.value = val.map((item) => ({
        ...item,
        props: { ...item.props },
        styles: { ...item.styles },
      }));
    }
  );

  watch(
    () => props.currentPageId,
    (val) => {
      activePageId.value = val;
    }
  );

  function onShowGridChange(val) {
    showGrid.value = val;
  }

  function onRenderListChange(val) {
    // 强制更新本地渲染列表
    localRenderList.value = val.map((item) => ({
      ...item,
      props: { ...item.props },
      styles: { ...item.styles },
    }));
    // 触发更新
    emit('update:renderList', localRenderList.value);
  }

  function onPageChange(pageId) {
    emit('update:currentPageId', pageId);
  }

  function handleSelectComponent(element) {
    // 确保在选中时不会丢失属性
    const component = {
      ...element,
      props: { ...element.props },
      styles: { ...element.styles },
    };
    emit('selectComponent', component);
  }

  // 从样式对象中提取 value 值
  function getComponentStyles(styles) {
    if (!styles) return {};
    return Object.entries(styles).reduce((acc, [key, value]) => {
      acc[key] = value.value;
      return acc;
    }, {});
  }

  // 监听样式变化
  watch(
    () => props.renderList,
    (newVal) => {
      if (newVal) {
        localRenderList.value = newVal.map((item) => ({
          ...item,
          props: { ...item.props },
          styles: { ...item.styles },
        }));
      }
    },
    { deep: true, immediate: true }
  );

  const formatProps = (propsData) => {
    const result = {};
    Object.keys(propsData).forEach((key) => {
      result[key] = propsData[key].value;
    });
    return result;
  };
</script>

<style scoped>
  .center-panel {
    flex: 1;
    min-width: 400px;
    border-right: 1px solid #e5e6eb;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #fcfcfd;
  }

  .toolbar {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    min-height: 40px;
    margin-bottom: 8px;
    background: #fff;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
    padding: 0 16px;
    border: 1px solid #e5e6eb;
    border-left: none;
    border-right: none;
  }

  .toolbar .arco-btn + .arco-btn {
    margin-left: 8px;
  }

  .phone-frame {
    position: relative;
    margin: 0 auto;
    margin-top: 80px;
    width: 375px;
    height: 660px;
    background: #fff;
    border: 1px solid #ccc;
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.12);
    overflow: hidden;
  }

  .phone-content {
    width: 750px;
    height: 1320px;
    transform: scale(0.5);
    transform-origin: 0 0;
    overflow-y: auto;
    padding-bottom: 100px; /* tabbar 高度 + 底部内边距 */
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  }

  .phone-frame.show-grid .phone-content {
    background: repeating-linear-gradient(
        to right,
        transparent,
        transparent 23px,
        #4f8cff1a 23px,
        #4f8cff1a 24px
      ),
      repeating-linear-gradient(
        to bottom,
        transparent,
        transparent 23px,
        #4f8cff1a 23px,
        #4f8cff1a 24px
      ),
      #fff;
  }

  .render-item {
    position: relative;
    /* padding: 8px; */
    /* margin-bottom: 8px; */
    /* background: #fff; */
    /* border: 1px solid #E5E6EB; */
    /* border-radius: 4px; */
    cursor: grab;
  }

  .component-wrapper {
    position: relative;
    min-height: 24px;
  }

  .render-item.active::after {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    right: 4px;
    bottom: 0px;
    border: 2px dashed #F77234;
    pointer-events: none; /* 确保伪元素不会阻止点击 */
  }

  .render-item.active .component-wrapper {
    pointer-events: none;
  }

  .render-item-actions {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.2s;
    z-index: 1;
  }

  .render-item:hover .render-item-actions {
    opacity: 1;
  }

  .delete-btn {
    color: rgb(var(--danger-6));
    padding: 2px;
  }

  .delete-btn:hover {
    background: rgb(var(--danger-1));
  }

  .mobile-tabbar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: #fff;
    border-top: 1px solid #e5e6eb;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 12px;
    z-index: 99;
  }

  .tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    cursor: pointer;
    transition: all 0.3s;
    color: #86909c;
  }

  .tabbar-item.active {
    color: rgb(var(--primary-6));
  }

  .tabbar-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }

  .tabbar-text {
    font-size: 12px;
    line-height: 1;
  }
</style>
