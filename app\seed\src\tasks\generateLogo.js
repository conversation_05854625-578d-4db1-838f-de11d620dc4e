// 生成Logo

const { OUT_PUT_DIR } = require('../constant.js');
const { downloadFileWithAxios } = require('../utils/downloadFile.js');
const path = require('path');

function generateLogo(meta) {
    const { logoUrl } = meta; // 从meta中获取logoUrl
    const logoPath = path.join(OUT_PUT_DIR, './assets/images/logo.png'); // 输出路径
    return downloadFileWithAxios(logoUrl, logoPath); // 下载文件并返回路径
}

module.exports = { generateLogo }