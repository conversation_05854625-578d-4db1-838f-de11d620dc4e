const user = require('../model/user');
const { sha256 } = require('../utils/tools');
const jwt = require('jsonwebtoken');
const { JWT_SECRET_KEY } = require('../constant');

const login = (req, res) => {
  const { username, password } = req.body;
  console.log('username', username);
  console.log('password', password);
  user.findOne({ username }).then((result) => {
    if (!result) {
      return res.status(401).json({
        status: false,
        data: {
          message: '用户不存在',
        },
      });
    }
    if (sha256(password + result.salt) !== result.hash) {
      return res.status(401).json({
        status: false,
        data: {
          message: '密码错误',
        },
      });
    } else {
      const token = jwt.sign({ userId: result.id }, JWT_SECRET_KEY, { expiresIn: '24h' });
      res.status(200);
      res.header('Access-Control-Allow-Origin', '*');
      res.cookie('token', token, {
        // httpOnly: true, // 防止 JavaScript 访问
        sameSite: 'strict', // 防止 CSRF 攻击
        maxAge: 3600000*24, // 过期时间（24小时）
        path: '/', // Cookie 的有效路径
      });
      res.send({
        status: true,
        data: {
          token: token,
        },
      });
    }
  });

};

const info = (req, res) => {
  const id = req.user.userId;
  user.findOneAndUpdate({ id },{ $set: { lastLogin: Date.now()+ 8 * 60 * 60 * 1000 } }).then((result) => {
    if (!result) {
      return res.status(401).json({
        status: false,
        data: {
          message: '用户不存在',
        },
      });
    }
    return res.status(200).json({
      status: true,
      data: {
        id,
        roleList:[result.role],
        userName: result.username,
        role: result.role
      },
    });
  })
};

const logout = (req, res) => {
  console.log('logout')
  res.clearCookie('token');
  res.status(200).json({    
    status: true,
    data: {
      message: '退出成功',
    },
  });
} 

module.exports = {
  login,
  info,
  logout
};