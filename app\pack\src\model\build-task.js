const mongoose = require('mongoose');

const { Schema } = mongoose;

const TaskSchema = new Schema(
  {
    taskId: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ['success', 'failure', 'pending', 'executing'],
      required: true,
    },
    brand: {
      type: String,
      required: true,
    },
    appName: {
      type: String,
      required: true,
    },
    sdkVersion: {
      type: String,
      required: true,
    },
    devPkg: {
      type: String,
      required: false,
    },
    releaseLogPkg: {
      type: String,
      required: false,
    },
    releasePkg: {
      type: String,
      required: false,
    },
    timeCost: {
      type: Number,
      required: false,
    },
    key: {
      type: String,
      required: true,
    },
    pkgType: [
      {
        type: String,
        required: true,
      },
    ],
    timeStamp: {
      type: String,
      required: true,
    },
    version: {
      type: String,
      required: false,
    },
    failedResult: {
      type: Schema.Types.Mixed,
      default: null,
    },
    errorCode: {
      type: Schema.Types.Mixed,
      default: null,
    },
    username:{
      type:String,
      default:"",
      required: false,
    },
    sdkCommit:{
      type:String,
      default:"",
      required: false,
    },
    appManifestRes:{
      type:String,
      default:"",
      required: false,
    },
    appDefinePluginRes:{
      type:String,
      default:"",
      required: false,
    }
  },
  {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
  }
);

// 创建模型
const Task = mongoose.model('buildTask', TaskSchema);

module.exports = Task;
