export type Key = string | number;

export interface AppInfo {
  key?: Key;
  appName: string;
  appVersion: string;
  sdkVersion: string;
  brand: 'xiaomi' | 'vivo' | 'oppo' | 'hw' | 'honor';
}

export interface FormInfo extends AppInfo {
  pkgType: string;
  versionUp: number;
}

export interface PakInfo extends AppInfo {
  downloadUrl?: { type: string; url: string }[];
  taskId?: string;
}

interface DownloadInfo {
  type: string;
  url: string;
}

export interface Message {
  type: string;
  data: {
    [key: string]: DownloadInfo[];
  };
}

interface AppResult {
  key: Key;
  type: string;
  reason?: string;
}

export interface PackResult {
  taskId: string;
  resultList: AppResult[];
}

export enum BRAND {
  xiaomi,
  vivo,
  oppo,
  hw,
  honor,
}
