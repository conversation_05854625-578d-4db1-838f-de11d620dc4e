<import name="common-header" src="../CommonHeader/index"></import>
<template>
  <div class="saving-skill">
    <common-header
      title="省电技巧"
    ></common-header>
    <div class="content">
      <div class="item" for="item in dataList">
        <div class="desc-box">
          <text class="title" style="font-weight:bold">{{ item.title }}</text>
          <text class="desc">{{ item.desc }}</text>
        </div>
        <image class="icon" src="{{ item.icon }}"></image>
      </div>
    </div>
  </div>
</template>

<script>
const  __BUS_PUBLIC_OSS_BASE_URL__ = "https://static.chengyouyun.com/"

export default {
  data: () => ({
    dataList: [{
      title: '自动调节屏幕亮度',
      desc: '降低屏幕亮度或打开自动调节屏幕亮度可有效延长使用时间',
      icon: __BUS_PUBLIC_OSS_BASE_URL__+'quickapp/app/ydpower/images/savingSkill/saving-1.png',
    },{
      title: '关闭不常用功能',
      desc: '关闭不需要的APP后台刷新功能， 可以提升电池使用时间',
      icon: __BUS_PUBLIC_OSS_BASE_URL__+'quickapp/app/ydpower/images/savingSkill/saving-2.png',
    },{
      title: '定位服务',
      desc: '关闭不需要的APP定位服务可以提升电池的使用时间',
      icon: __BUS_PUBLIC_OSS_BASE_URL__+'quickapp/app/ydpower/images/savingSkill/saving-3.png',
    },{
      title: '开启低电量模式',
      desc: '电量较低时，可以开启低电量模式降低电量消耗',
      icon: __BUS_PUBLIC_OSS_BASE_URL__+'quickapp/app/ydpower/images/savingSkill/saving-4.png',
    },{
      title: '养成良好的充电习惯',
      desc: '不要频繁给手机充电，保持较高的电池健康度',
      icon: __BUS_PUBLIC_OSS_BASE_URL__+'quickapp/app/ydpower/images/savingSkill/saving-5.png',
    },{
      title: '减少屏幕待机超时时间',
      desc: '选择适合的屏幕待机超时时间',
      icon: __BUS_PUBLIC_OSS_BASE_URL__+'quickapp/app/ydpower/images/savingSkill/saving-6.png',
    }]
  }),
  onInit() {
  },

}
</script>
<style lang="less">
.saving-skill {
    background-color: #f7f7f7;
    flex-direction: column;
    .content {
        flex-direction: column;
        padding: 0 30px;
        .item{
            margin-bottom: 20px;
            /* width: 690px; */
            height: 250px;
            background-color: #ffffff;
            border-radius: 20px;
            padding: 55px 40px 0;
            .desc-box {
                flex: 1;
                flex-direction: column;
                .title {
                    font-size: 32px;
                    color: #333;
                    line-height: 48px;
                    margin-bottom: 12px;
                    font-weight:bold;
                }
                .desc {
                    font-size: 26px;
                    color: #BCBCBC;
                    line-height: 40px;
                }
            }
            .icon {
                width: 140px;
                height: 156px;
                margin-left: 78px;
            }
        }
    }
}
</style>
