const axios = require('axios');
const AppVersion = require('../../model/version');
const { saveRpk } = require('../unzip');

async function getApkInfo(appName, needAll) {
  try {
    const response = await axios.get('https://center.jits.heytapmobi.com/jitscenter/center/instants/search', {
      params: {
        name: `%${appName}%`,
        size: 10,
        start: 1,
      },
    });

    if (response.data.code === '200') {
      if (needAll) {
        return response.data.data.instantDto;
      }

      return response.data.data.instantDto.find((v) => v.name === appName);
    }
    return null;
  } catch (error) {
    console.log(error);
    return null;
  }
}

function createOppoLink(apps) {
  return Promise.all(
    apps.map(async (app) => {
      const searchedAppInfo = await getApkInfo(app.name);
      if (!searchedAppInfo) return;

      // 判断数据库是否已有此版本
      const existingVersion = await AppVersion.findOne({
        name: app.name,
        version: searchedAppInfo.vName,
        brand: 'oppo',
      });
      if (existingVersion) return;
      const { url, vName } = searchedAppInfo;
      if (!vName) return;
      // 创建新版本
      setTimeout(() => {
        saveRpk(url, `${app.name}-${vName}-oppo`);
      }, 1);
      // eslint-disable-next-line consistent-return
      return AppVersion.create({
        name: app.name,
        version: vName,
        appLink: url,
        brand: 'oppo',
      });
    })
  );
}

module.exports = {
  getApkInfo,
  createOppoLink,
};
