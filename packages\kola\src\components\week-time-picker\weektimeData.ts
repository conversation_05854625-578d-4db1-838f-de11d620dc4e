const formatDate = (date, fmt) => {
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    'S': date.getMilliseconds(),
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, `${date.getFullYear()}`.substr(4 - RegExp.$1.length));
  }

  // eslint-disable-next-line no-restricted-syntax
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : `00${o[k]}`.substr(`${o[k]}`.length));
    }
  }
  return fmt;
};

const createArr = (len) => {
  return Array.from(Array(len)).map((ret, id) => id);
};

const formatWeektime = (col, number) => {
  const half = number === WeekTimeNumber.TOU_TIAO;
  const timestamp = 1542384000000; // '2018-11-17 00:00:00'
  const beginstamp = timestamp + col * (half ? 1800000 : 3600000); // col * 30 * 60 * 1000
  const endstamp = beginstamp + (half ? 1800000 : 3600000);

  const begin = formatDate(new Date(beginstamp), 'hh:mm');
  const end = formatDate(new Date(endstamp), 'hh:mm');
  return `${begin}~${end}`;
};

const data = (number) =>
  ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'].map((ret, index) => {
    const children = (ret1, row, max) => {
      return createArr(max).map((t, col) => {
        return {
          week: ret1,
          value: formatWeektime(col, number),
          begin: formatWeektime(col, number).split('~')[0],
          end: formatWeektime(col, number).split('~')[1],
          row,
          col,
        };
      });
    };
    return {
      value: ret,
      row: index,
      child: children(ret, index, 24),
    };
  });

export const splicing = (list) => {
  let same;
  let i = -1;
  const len = list.length;
  const arr: string[] = [];

  if (!len) return;
  // eslint-disable-next-line no-plusplus
  while (++i < len) {
    const item = list[i];
    if (item.check) {
      if (item.check !== Boolean(same)) {
        arr.push(...['、', item.begin, '~', item.end]);
      } else if (arr.length) {
        arr.pop();
        arr.push(item.end);
      }
    }
    same = Boolean(item.check);
  }
  arr.shift();
  // eslint-disable-next-line consistent-return
  return arr.join('');
};

function getHour(start) {
  return start.includes('30') ? start.split(':')[0] * 2 + 1 : start.split(':')[0] * 2;
}

// 根据详情反选时间短
export function transformData(scheduleTime, number) {
  const half = number === WeekTimeNumber.TOU_TIAO;
  const result: any[] = [];

  scheduleTime.forEach((item) => {
    const newItem: any = {
      value: item.week,
      row: item.id,
      child: [],
    };

    const checkedHours: boolean[] = Array(number).fill(false);

    if (item.value) {
      const timeslots = item.value.split('、');
      timeslots.forEach((timeslot) => {
        const [start, end] = timeslot.split('~');
        let startCol;
        let endCol;
        if (half) {
          startCol = getHour(start);
          endCol = getHour(end);
        } else {
          const [startHour] = start.split(':');
          const [endHour] = end.split(':');
          startCol = parseInt(startHour, 10);
          endCol = parseInt(endHour, 10);
        }

        // eslint-disable-next-line no-plusplus
        for (let i = startCol; i < endCol; i++) {
          checkedHours[i] = true;
        }
      });

      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < number; i++) {
        const entTime = half ? i / 2 : i;
        const childItem = {
          week: item.week,
          value: `${formatTime(entTime)}~${formatTime(entTime + (half ? 0.5 : 1))}`,
          begin: formatTime(entTime),
          end: formatTime(entTime + (half ? 0.5 : 1)),
          row: item.id,
          col: i,
          check: checkedHours[i],
        };

        newItem.child.push(childItem);
      }
    } else {
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < number; i++) {
        const entTime = half ? i / 2 : i;
        newItem.child.push({
          week: item.week,
          value: `${formatTime(entTime)}~${formatTime(entTime + (half ? 0.5 : 1))}`,
          begin: formatTime(entTime),
          end: formatTime(entTime + (half ? 0.5 : 1)),
          row: item.id,
          col: i,
          check: false,
        });
      }
    }

    result.push(newItem);
  });

  return result;
}

function formatTime(hour) {
  if (hour.toString().includes('.5')) {
    const [wholePart] = hour.toString().split('.');
    const formattedHour = wholePart.padStart(2, '0');
    return `${formattedHour}:30`;
  }
  return `${hour.toString().padStart(2, '0')}:00`;
}

// eslint-disable-next-line no-shadow
export enum WeekTimeNumber {
  TOU_TIAO = 48,
  KUAI_SHOU = 24,
  GDT = 48,
  TENCENT = 48,
}

export default data;
