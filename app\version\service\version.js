const { saveXMVersion } = require('./rpk-link/xiaomi');
const { createVivoLink } = require('./rpk-link/vivo');
const { createOppoLink } = require('./rpk-link/oppo');

const App = require('../model/app');
const Version = require('../model/version');

/**
 * 保存版本信息
 * @param {Object} versionData - 版本数据
 * @param {string} versionData.name - 版本名称
 * @param {string} versionData.version - 版本号
 * @param {string} versionData.appLink - app下载地址
 * @param {Date} [versionData.releaseDate] - 上线日期，默认为当前时间
 * @returns {Promise<Object>} 保存后的版本对象
 */
async function saveVersion() {
  const apps = await App.find({});

  return Promise.all([saveXMVersion(apps), createVivoLink(apps), createOppoLink(apps)]);
}

async function getPreVersion({ name, brand, releaseDate }) {
  // 查找releaseDate之前且name和brand匹配的第一个版本
  const preVersion = await Version.findOne({
    name,
    brand,
    releaseDate: { $lt: releaseDate },
  }).sort({ releaseDate: -1 }); // 按releaseDate降序排列，取第一个

  return preVersion;
}

module.exports = {
  saveVersion,
  getPreVersion,
};
