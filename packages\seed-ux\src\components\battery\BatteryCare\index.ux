<import name="common-header" src="../CommonHeader/index"></import>
<template>
  <div class="battery-care">
    <common-header
      title="电池保养"
    ></common-header> 
    <div class="content">
      <div class="item" for="item in dataList">
        <div class="desc-box">
          <text class="title">{{ item.title }}</text>
          <text class="desc">{{ item.desc }}</text>
        </div>
        <image class="icon" src="{{ item.icon }}"></image>
      </div>
    </div>
  </div>
</template>

<script>
  const __BUS_PUBLIC_OSS_BASE_URL__ = "https://static.chengyouyun.com/"

  export default {
    data: () => ({
      dataList: [{
        title: '避免处于极端的温度环境',
        desc: '介于16℃—22℃是理想的温度带， 避免处于环境温度高于35℃场所',
        icon: __BUS_PUBLIC_OSS_BASE_URL__ + 'quickapp/app/ydpower/images/batteryCare/battery-1.png',
      }, {
        title: '避免不正规的充电配件',
        desc: '使用盗版的充电器和数据线可能会对设备造成不可逆的伤害',
        icon: __BUS_PUBLIC_OSS_BASE_URL__ + 'quickapp/app/ydpower/images/batteryCare/battery-2.png',
      }, {
        title: '避免边充边用',
        desc: '充电时使用手机会使电池温度升高，耗损电池',
        icon: __BUS_PUBLIC_OSS_BASE_URL__ + 'quickapp/app/ydpower/images/batteryCare/battery-3.png',
      }, {
        title: '请保持一半的电量',
        desc: '请勿完全充电或放电，如果长期处于完全充电或放电的设备，可能永久失去部分电池容量',
        icon: __BUS_PUBLIC_OSS_BASE_URL__ + 'quickapp/app/ydpower/images/batteryCare/battery-4.png',
      }, {
        title: '减少使用次数',
        desc: '控制使用时间，减少使用次数避免手机耗电',
        icon: __BUS_PUBLIC_OSS_BASE_URL__ + 'quickapp/app/ydpower/images/batteryCare/battery-5.png',
      }, {
        title: '手机充满电后拔掉充电器',
        desc: '避免长时间充电或长期处于高电量状态加速电池老化',
        icon: __BUS_PUBLIC_OSS_BASE_URL__ + 'quickapp/app/ydpower/images/batteryCare/battery-6.png',
      }]
    }),
    onInit() {
    },

  }
</script>
<style lang="less">
  .battery-care {
    background-color: #f7f7f7;
    flex-direction: column;
    .content {
      flex-direction: column;
      padding: 0 30px;

      .item {
        margin-bottom: 20px;
        /* width: 690px; */
        height: 250px;
        background-color: #ffffff;
        border-radius: 20px;
        padding: 55px 40px 0;

        .desc-box {
          flex: 1;
          flex-direction: column;

          .title {
            font-size: 32px;
            color: #333;
            line-height: 48px;
            margin-bottom: 12px;
            font-weight: bold;
          }

          .desc {
            font-size: 26px;
            color: #BCBCBC;
            line-height: 40px;
          }
        }

        .icon {
          width: 140px;
          height: 156px;
          margin-left: 78px;
        }
      }
    }
  }
</style>