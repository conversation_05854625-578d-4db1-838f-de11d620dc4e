
<import name="common-agreement" src="../../components/userAgreement/index"></import>
<!-- imports -->

<template>
  <div style="width:750px;height:100%;">
    <tabs index="{{activeTab}}" onchange="activeTabChange">
      <tab-content>
        <!-- tabs -->
      </tab-content>
      <tab-bar mode="fixed" class="tab-bar-wrapper">
        <div class="tab-item" for="(index, item) in tabList">
          <image class="tab-icon" src="{{activeTab === index ? item.activeIcon : item.icon}}"></image>
          <text class="tab-title {{activeTab == index ? 'change-tab-color' : ''}}">{{ item.name  }}</text>
        </div>
      </tab-bar>
    </tabs>

    <common-agreement agreement-url="{{agreementUrl}}" privacy-url="{{privacyUrl}}"></common-agreement>

  </div>
</template>
<script>

export default {
  public: {
    activeTab: 0,
    tabList: '__tabList__',
    agreementUrl: '__agreementUrl__',
    privacyUrl: '__privacyUrl__',
  },
  activeTabChange(evt) {
    if (this.activeTab == evt.index) return
    this.activeTab = Number(evt.index)
  },
}
</script>

<style lang="less">
@import '../../style/variables.less';
.tab-bar-wrapper {
  background-color: #ccc;
  bottom: 0px;
}
.change-tab-color {
    color: @theme;
}

.tab-bar-wrapper {
    height: 100px;
}

.tab-item {
    height: 100px;
    flex-direction: column;
}
.tab-icon{
    width: 38px;
    height: 40px;
    object-fit: fill;
}
.tab-title{
    height: 28px;
    font-size: 20px;
}
</style>
