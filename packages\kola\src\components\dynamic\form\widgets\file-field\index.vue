<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="rules"
    :hide-label="field.hideLabel"
    :hide-asterisk="field.hideAsterisk"
  >
    <div>
      <a-upload
        :file-list="fileList"
        :custom-request="customRequest"
        :accept="accept"
        :on-before-remove="beforeRemove"
        :limit="fileLimit"
        :list-type="fileType.isImage === true ? 'picture-card' : ''"
        image-preview
        @before-upload="beforeUpload"
        :style="field.style"
      ></a-upload>
      <a-typography-text type="secondary" v-if="field.tips" style="margin-top: 8px; display: block">
        {{ field.tips }}
      </a-typography-text>
    </div>
    <template #extra v-if="field.extra">
      <component :is="field.extra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject } from 'vue';
  import { uniqueId, isArray, last, isString, forEach, isFunction } from 'lodash';
  import { obsUpload } from '@repo/sdk';
  import { Message, useFormItem } from '@arco-design/web-vue';
  import { FileField } from '../../../types/form';

  const { eventHandlers } = useFormItem();
  const props = defineProps<{
    field: FileField;
    path: string;
  }>();

  const modalValue = defineModel<string | string[]>({});
  const formData: any = inject('formData');

  const fileList = computed(() => {
    if (!modalValue.value) {
      return [];
    }

    return (isArray(modalValue.value) ? modalValue.value : [modalValue.value]).map((url: string) => {
      return {
        uid: uniqueId('uid'),
        name: last(url.split('/')),
        url,
      };
    });
  });

  const isValidImageWidthHeight = computed(() => {
    return props.field.fileType === 'image' && !!props.field?.width && !!props.field?.height;
  });
  const fileType = computed(() => {
    const type = props.field.fileType;
    return {
      isImage: type === 'image' || type === undefined,
      isJson: type === 'json',
      isMp3: type === 'mp3',
      isPem: type === 'pem',
    };
  });
  const accept = computed(() => {
    const type = fileType.value;
    if (type.isImage) {
      return props.field?.fileTypeRange || '.jpeg,.jpg,.png,.webp,.bmp';
    }
    if (type.isJson) {
      return '.json';
    }
    if (type.isMp3) {
      return '.mp3';
    }
    if (type.isPem) {
      return '.pem';
    }
    return '';
  });
  const imgTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/bmp'];
  const textTypes = ['application/json', 'text/plain'];
  const fileLimit = computed(() => {
    return props.field.limit || 1;
  });
  const fieldInfoMap = new Map();
  const rules = computed(() => {
    let newRules: any = [];
    if (!isFunction(props.field.required)) {
      newRules = props.field.rules;
    } else {
      const requiredStatus = props.field.required(props.field, formData?.value as any, props.path);
      newRules = [
        {
          required: requiredStatus,
          message: `${props.field.label}是必填项`,
        },
      ];
    }
    if (isValidImageWidthHeight.value) {
      const { width } = props.field;
      const { height } = props.field;
      newRules.push({
        validator: (value, cb) => {
          return new Promise((resolve) => {
            if (value) {
              if (isString(value)) {
                const info = fieldInfoMap.get(value);
                if (!info) {
                  onGetImageInfo(value).then((imgInfo: any) => {
                    fieldInfoMap.set(value, imgInfo);
                    if (imgInfo.width !== width && imgInfo.height !== height) {
                      cb(`图片尺寸应为${width}*${height}`);
                    } else {
                      cb();
                    }
                    resolve(true);
                  });
                } else if (info.width !== width && info.height !== height) {
                  cb(`图片尺寸应为${width}*${height}`);
                  resolve(true);
                } else {
                  cb();
                  resolve(true);
                }
              } else if (isArray(value)) {
                // eslint-disable-next-line consistent-return
                forEach(value, (imageUrl) => {
                  const info = fieldInfoMap.get(imageUrl);
                  if (!info) {
                    onGetImageInfo(imageUrl).then((imgInfo: any) => {
                      fieldInfoMap.set(imageUrl, imgInfo);
                      if (imgInfo.width !== width && imgInfo.height !== height) {
                        cb(`图片尺寸应为${width}*${height}`);
                      } else {
                        cb();
                      }
                      resolve(true);
                    });
                  } else if (info.width !== width && info.height !== height) {
                    cb(`图片尺寸应为${width}*${height}`);
                    resolve(true);
                    return false;
                  } else {
                    cb();
                    resolve(true);
                  }
                });
              } else {
                resolve(true);
                cb();
              }
            } else {
              cb();
              resolve(true);
            }
          });
        },
        trigger: 'change',
      });
    }
    return newRules;
  });

  const customRequest = (option) => {
    const { onProgress, onError, onSuccess, fileItem, name } = option;
    const { bucket, path, obsKey } = props.field;
    obsUpload({ bucket, path, file: fileItem.file, obsKey })
      .then(async (res: any) => {
        const { url } = res;
        if (isValidImageWidthHeight.value) {
          try {
            const info = await onGetImageInfo(url);
            fieldInfoMap.set(url, info);
          } catch (e) {
            console.log('err', e);
          }
        }
        setUploadValue(url);
        eventHandlers.value.onChange?.();
        onSuccess();
      })
      .catch((err) => {
        console.log('err', err);
        onError();
      });
  };

  function setUploadValue(url) {
    const limitOne = fileLimit.value === 1;
    if (limitOne) {
      modalValue.value = url;
    } else if (modalValue.value) {
      modalValue.value.push(url);
    } else {
      modalValue.value = [url];
    }
  }

  // 获取华为obs图片详细信息
  function onGetImageInfo(url) {
    return new Promise((resolve, reject) => {
      fetch(`${url}?x-image-process=image/info`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'omit',
      })
        .then((res) => res.json())
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          Message.error('获取图详情失败');
          console.log('err', err);
          reject(err);
        });
    });
  }

  const beforeRemove = (fileItem) => {
    const { url } = fileItem;
    const limitOne = fileLimit.value === 1;
    if (limitOne) {
      modalValue.value = '';
    } else {
      modalValue.value = (modalValue.value || []).filter((item) => item !== url);
    }
    return true;
  };

  function beforeUpload(file) {
    if (!props.field?.maxSize) {
      return true;
    }
    const maxFileSize = props.field?.maxSize ?? 200;

    const fileSizeByUnit = file.size / 1024; // 默认KB
    if (fileSizeByUnit > maxFileSize) {
      Message.warning(`文件大小不能超过${maxFileSize}KB`);
      return false;
    }
    return true;
  }
</script>

<style scoped lang="less">
  .arco-upload-picture-card-text {
    background-color: var(--color-neutral-2);
    padding: 15px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
</style>
