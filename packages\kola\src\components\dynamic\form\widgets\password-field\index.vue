<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="fieldLabel"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
  >
    <a-input-password
      v-model="value"
      :placeholder="field.placeholder || `请输入${fieldLabel}`"
      :default-visibility="true"
      allow-clear
      :max-length="field.maxLength"
    />
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject } from 'vue';
  import { isFunction } from 'lodash';
  import { PasswordField } from '../../../types/form';

  const props = defineProps<{
    field: PasswordField;
    path: string;
  }>();

  const formData = inject('formData');
  const fieldLabel = computed(() => {
    return isFunction(props.field.label)
      ? props.field.label(props.field, formData?.value as any, props.path)
      : props.field.label;
  });
  const value = defineModel<string>({
    default: '',
    set(val) {
      props.field.onChange?.(val, formData);
      if (props.field.setter) {
        return props.field.setter(val);
      }
      return val;
    },
  });
</script>
