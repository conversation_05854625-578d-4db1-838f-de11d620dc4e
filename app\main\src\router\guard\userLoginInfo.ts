import type { Router } from 'vue-router';
import NProgress from 'nprogress'; // progress bar

// eslint-disable-next-line import/no-cycle
import { useUserStore } from '@/store';
import { auth } from '@repo/sdk';
import { TOKEN_KEY } from '@/constants/pack';

export default function setupUserLoginInfoGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    // NProgress.start();
    const token = auth.getCookie(TOKEN_KEY);
    if (token) {
      const userStore = useUserStore();

      if (userStore.id) {
        next();
        // NProgress.done();
        return;
      }

      try {
        await userStore.info();
        next();
      } catch (error) {
        // await userStore.logout();
        setTimeout(() => {
          window.location.href = auth.getLoginUrl();
        }, 1500);
      }
    } else {
      // window.location.href = auth.getLoginUrl();
      if(to.name === 'login') {
        next();
        return 
      }
      next({ name: 'login' });
    }
  });
}
