<template>
  <div style="padding: 16px; background-color: #f5f5f5; height: 100%">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" title="版本列表">
        <!-- 版本列表内容 -->
        <VersionList></VersionList>
      </a-tab-pane>
      <a-tab-pane key="2" title="竞品设置">
        <!-- 竞品设置内容 -->
        <AppList></AppList>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
  import AppList from './app-list/index.vue';
  import VersionList from './version-list/index.vue';
</script>

<style scoped>
  /* 样式可以根据需要添加 */
</style>
