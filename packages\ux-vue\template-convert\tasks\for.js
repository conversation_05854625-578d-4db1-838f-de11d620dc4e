// 转换for指令到v-for
function convertForDirective(forAttr) {
  // 处理三种for语法
  if (forAttr.startsWith('(')) {
    // 格式: (index, item) in list 或 (item) in list
    const cleaned = forAttr.replace(/[(]/g, '').trim();
    console.log('cleaned', forAttr, cleaned)
    const parts = cleaned.split(')');
    console.log('parts', parts)
    const part = parts[0].split(',')
    if (part.length >= 2) {
      // 调换index和item的位置
      return `(${[part[1], part[0]].join(', ')})${parts[1]}`;
    }
  } else if (forAttr.includes(' in ')) {
    // 格式: value in list
    const [value, list] = forAttr.split(' in ');
    return `(${value}, $idx) in ${list}`;
  } else {
    // 格式: {{list}}
    const list = forAttr.replace(/{{(.*)}}/, '$1').trim();
    return `($item, $idx) in ${list}`;
  }
}

module.exports = function ($) {
  $('[for]').each((i, el) => {
    const $el = $(el);
    const forAttr = $el.attr('for');

    // 转换for指令到v-for
    const vFor = convertForDirective(forAttr);
    if (vFor) {
      $el.attr('v-for', vFor);

      // 添加:key绑定
      // $el.attr(':key', getKeyBinding(vFor));

      // 移除原始for属性
      $el.removeAttr('for');
    }
  });
}