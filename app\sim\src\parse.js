const fs = require('fs');
const babel = require('@babel/core');
const traverse = require('@babel/traverse').default;

function parseAndWrite(sourcePath, outputPath) {
  return new Promise((resolve) => {
    const code = fs.readFileSync(sourcePath, 'utf8').toString();
    const writeStream = fs.createWriteStream(outputPath, { flags: 'a' }); // 'a' 表示追加模式

    const ast = babel.parseSync(code);

    // 使用 traverse 正确遍历 AST
    traverse(ast, {
      BlockStatement(path) {
        createNewBlock(path);
      },
    });

    function createNewBlock(astPath) {
      const blockStr = astPath.node.body.reduce((pre, cur) => {
        astPath.scope.traverse(cur, {
          enter(innerPath) {
            // 停止遍历条件：遇到特定类型的节点
            if (innerPath.node.type === 'BlockStatement') {
              innerPath.stop(); // 停止遍历 IfStatement
              return;
            }

            pre += innerPath.node.type + '-';
          },
        });
        return pre;
      }, '');

      // 写入到文件中
      writeStream.write(blockStr + '\n', 'utf8', (err) => {
        if (err) console.error('Write error:', err);
      });
    }

    // 关闭流
    writeStream.end(() => {
      // console.log("All data has been written to output.txt");
      resolve();
    });
  });
}

module.exports = parseAndWrite;
