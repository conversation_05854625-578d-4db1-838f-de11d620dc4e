import { v4 as uuidv4 } from 'uuid';
import pinyin from 'pinyin';

interface Component {
  id: string;
  name: string;
  label: string;
  pinyin: string;
  props?: Record<string, any>;
  styles?: Record<string, any>;
}

// 克隆组件
export function cloneComponent(component: Component) {
  return {
    ...component,
    id: uuidv4(),
    props: { ...component.props },
    styles: { ...component.styles }
  };
}

// 复制到剪贴板
export function copyToClipboard(text: string) {
  return navigator.clipboard.writeText(text);
}

// 检查是否匹配（支持中文、拼音、拼音首字母搜索）
export function isMatch(element: Component, keyword: string) {
  if (!keyword) return true;
  keyword = keyword.toLowerCase();
  
  // 检查原始标签
  if (element.label.toLowerCase().includes(keyword)) return true;
  
  // 检查完整拼音
  if (element.pinyin.toLowerCase().includes(keyword)) return true;
  
  // 检查拼音首字母
  const initials = pinyin(element.label, { style: pinyin.STYLE_FIRST_LETTER }).join('');
  if (initials.toLowerCase().includes(keyword)) return true;
  
  return false;
} 