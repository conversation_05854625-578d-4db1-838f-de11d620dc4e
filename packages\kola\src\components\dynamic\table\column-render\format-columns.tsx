import { omit } from 'lodash';
import { TableColumnData } from '@arco-design/web-vue';
import { Column } from '../../types/table';
import resolveRender from './resolve-render';

type RenderColumnsType = Column & {
  render?: any;
};
function formatColumns(columns: RenderColumnsType[]) {
  const results: TableColumnData[] = [
    ...(columns ?? []).map((item) => {
      const width = item.width ?? 80;
      let minWidth = item.minWidth ?? 80;
      if (minWidth > width) {
        minWidth = width;
      }
      return {
        ...omit(item, ['customRender', 'headerToolTip']),
        render: item.render ?? resolveRender(item),
        ellipsis: item.ellipsis ?? true,
        tooltip: item.tooltip ?? true,
        minWidth,
        width,
        slots: {
          title: () => (
            <div>
              {item.title}
              {item.headerToolTip && (
                <a-tooltip content={item.headerToolTip}>
                  <icon-info-circle
                    size={20}
                    style={{
                      marginLeft: '4px',
                      cursor: 'pointer',
                      verticalAlign: 'bottom',
                    }}
                  />
                </a-tooltip>
              )}
            </div>
          ),
        },
      };
    }),
  ];

  return results;
}

export default formatColumns;
