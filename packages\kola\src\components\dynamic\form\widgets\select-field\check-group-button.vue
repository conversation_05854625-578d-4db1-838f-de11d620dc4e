<template>
  <div>
    <a-checkbox
      v-if="showAll"
      :model-value="selectCtx.checkedAll"
      :indeterminate="selectCtx.indeterminate"
      @change="selectCtx.handleChangeAll"
    >
      全选
    </a-checkbox>
    <a-checkbox-group
      v-model="value"
      @change="selectCtx.handleChange"
      :direction="direction"
      :class="{ 'button-checkbox': true }"
      :max="max"
    >
      <a-checkbox
        v-for="item in sourceData"
        :value="item[valueKey]"
        :key="item[valueKey]"
        :disabled="item.disabled"
        class="button-checkbox-item"
      >
        <component :is="item[labelKey]" v-if="isVNode(item[labelKey])" />
        <template v-else>{{ item[labelKey] }}</template>
      </a-checkbox>
    </a-checkbox-group>
  </div>
</template>

<script setup lang="ts">
  import { inject, h, isVNode } from 'vue';
  import { Option } from '../../../types/form';
  import { SelectContext, selectInjectionKey } from './context';

  defineProps<{
    sourceData: Option[];
    valueKey: string;
    labelKey: string;
    format: string;
    showAll: boolean;
    direction: 'vertical' | 'horizontal';
    max: number | void;
  }>();
  const selectCtx = inject<Partial<SelectContext>>(selectInjectionKey, {});

  const value = defineModel<any>();
</script>

<style lang="less" scoped>
  .button-checkbox {
    display: inline-flex;
    padding: 1.5px;
    line-height: 26px;
    background-color: var(--color-fill-2);
    border-radius: var(--border-radius-small);

    :deep(> .arco-checkbox) {
      .arco-icon-hover {
        display: none;
      }

      .arco-checkbox-icon {
        display: none;
      }

      .arco-checkbox-label {
        margin-left: 0;
      }
    }

    .button-checkbox-item {
      position: relative;
      display: inline-block;
      color: var(--color-text-2);
      font-size: 14px;
      line-height: 26px;
      background-color: transparent;
      border-radius: var(--border-radius-small);
      cursor: pointer;
      transition: all 0.1s cubic-bezier(0, 0, 1, 1);
      padding: 0 12px;
      margin: 1.5px;

      &::after {
        position: absolute;
        top: -3px;
        right: 0;
        width: 0;
        height: 0;
        content: '';
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid #dadfe3;
        border-radius: 2px;
        transform: rotate(-45deg);
      }
      //选择情况
      &.arco-checkbox-checked {
        background-color: var(--color-bg-5);

        :deep(.arco-checkbox-label) {
          color: rgb(var(--primary-6));
        }

        &:not(:first-of-type)::before {
          opacity: 0;
        }

        &::after {
          border-left: 6px solid rgb(var(--primary-6));
        }
      }

      &:not(:first-of-type)::before {
        position: absolute;
        top: 50%;
        left: -2px;
        display: block;
        width: 1px;
        height: 14px;
        background-color: var(--color-neutral-3);
        transform: translateY(-50%);
        transition: all 0.1s cubic-bezier(0, 0, 1, 1);
        content: '';
      }

      &.arco-checkbox-checked + .button-checkbox-item {
        &::before {
          opacity: 0;
        }
      }
      //禁用情况
      &.arco-checkbox-disabled {
        cursor: not-allowed;
      }
      //禁用情况 + 选择情况
      &.arco-checkbox-checked.arco-checkbox-disabled {
        &.arco-checkbox-checked {
          background-color: var(--color-bg-5);

          :deep(.arco-checkbox-label) {
            color: var(--color-primary-light-3);
          }
        }
      }
    }
  }
</style>
