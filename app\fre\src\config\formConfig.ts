const formConfig = {
  operationsOptions: [
    { label: '构建测试包', value: 'dev' },
    { label: '构建线上包', value: 'release-log' },
    { label: '构建提审包', value: 'release' },
    // { label: '升级版本号', value: 'git-upgrade' },
    // { label: '提交代码', value: 'git-commit' },
  ],
  brandOptions: [
    { label: '华为', value: 'hw' },
    { label: '小米', value: 'xiaomi' },
    { label: 'oppo', value: 'oppo' },
    { label: 'vivo', value: 'vivo' },
    { label: '荣耀', value: 'honor' },
  ],
  sdkVersionOptions: [
    { label: '1.0.0', value: '1.0.0' },
    { label: '1.1.0', value: '1.1.0' },
  ],
  appNameOptions: [
    { label: '猫眼漫画', value: '猫眼漫画' },
    { label: '趣点阅读', value: '趣点阅读' },
  ],
  columns: [
    {
      key: 'taskId',
      title: '任务ID',
      dataIndex: 'taskId',
      width: 150,
      customCell: (record: any) => {
        return {
          rowSpan: record.taskIdRowSpan,
        };
      },
    },
    { key: 'appName', title: '产品名称', dataIndex: 'appName', width: 150 },
    { key: 'brand', title: '品牌', dataIndex: 'brand', width: 150 },
    { key: 'version', title: '版本号', dataIndex: 'version', width: 150 },
    { key: 'sdkVersion', title: 'SDK版本', dataIndex: 'sdkVersion', width: 150 },
    { key: 'taskStatus', title: '任务状态', dataIndex: 'taskStatus', width: 150 },
    { key: 'timeCost', title: '打包耗时', dataIndex: 'timeCost', width: 150 },
    { key: 'timeStamp', title: '任务创建时间', dataIndex: 'timeStamp', width: 150 },
    { key: 'operations', title: '打包结果', dataIndex: 'operations' },
  ],
};

export default formConfig;
