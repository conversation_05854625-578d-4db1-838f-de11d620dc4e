const Visit = require('../model/visit.js');

// 创建Visit
const createVisit = async (req, res) => {
  try {
    const data = req.body;
    const newRecord = new Visit(data);
    await newRecord.save();
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: newRecord,
    });
  } catch (error) {
    res.status(400).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 获取Visit列表（支持分页和搜索）
const getAllVisits = async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, ...filters } = req.query;
    const query = {};

    // 动态添加过滤条件
    Object.keys(filters).forEach((key) => {
      if (filters[key]) {
        query[key] = { $regex: filters[key], $options: 'i' };
      }
    });

    // 计算分页参数
    const skip = (pageNum - 1) * pageSize;

    // 查询总数
    const total = await Visit.countDocuments(query);

    // 查询分页数据
    const records = await Visit.find(query).skip(skip).limit(Number(pageSize)).sort({ createdAt: -1 });

    res.status(200).json({
      code: 0,
      msg: 'success',
      data: records,
      total: total,
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};
// 获取单个Visit
const getVisitById = async (req, res) => {
  try {
    const record = await Visit.findById(req.params.id);
    if (!record) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: record,
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 更新Visit
const updateVisit = async (req, res) => {
  try {
    const updatedRecord = await Visit.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updatedRecord) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: updatedRecord,
    });
  } catch (error) {
    res.status(400).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 删除Visit
const deleteVisit = async (req, res) => {
  try {
    const deletedRecord = await Visit.findByIdAndDelete(req.params.id);
    if (!deletedRecord) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: '删除成功',
      data: null,
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

module.exports = {
  createVisit,
  getAllVisits,
  getVisitById,
  updateVisit,
  deleteVisit,
};
