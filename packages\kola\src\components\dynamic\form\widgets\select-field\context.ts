import { InjectionKey } from 'vue';

export interface SelectContext {
  checkedAll: boolean;
  indeterminate: boolean;
  handleChangeAll: (flag: boolean) => void;
  handleChange: (value: any) => void;
  onPopupVisibleChange: (flag: boolean) => void;
  handleReverse: () => void; // 梳理反选
  getSourceData: () => void; // 获取源数据
  updateSearchText: (text: string) => void; // 更新搜索文本
  loading: boolean; // 搜索加载
}

export const selectInjectionKey: InjectionKey<SelectContext> = Symbol('KolaSelect');
