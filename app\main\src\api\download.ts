import axios from 'axios';

function xmSearch(data: any) {
  return axios.post<any>('/api/proxy/xm', data);
}

function xmDownload(data: any) {
  return axios.post<any>('/api/proxy/download/xm', data);
}

function oppoDownload(data: any) {
  return axios.post<any>('/api/proxy/oppo', data);
}

function vivoDownload(data: any) {
  return axios.post<any>('/api/proxy/vivo', data);
}
function honorDownload(data: any) {
  return axios.post<any>('/api/proxy/honor', data);
}

function hwDownload(data: any) {
  return axios.post<any>('/api/proxy/hw', data);
}

const downloadAPi = {
  xmDownload,
  xmSearch,
  oppoDownload,
  vivoDownload,
  honorDownload,
  hwDownload,
};

export default downloadAPi;
