<template>
  <a-form-item
    :label="field.label"
    :content-flex="false"
    :field="currentPath"
    :hide-label="field.hideLabel"
    :rules="rules"
    class="array-wrapper"
  >
    <template v-if="!field.format || field.format === 'list'">
      <ListArray
        :field="field"
        :path="currentPath"
        :can-add="canAdd"
        :can-remove="canRemove"
        :hide-operation="field.hideOperation"
        v-model="value"
        @add="handleAdd"
        @remove="handleRemove"
      />
    </template>
    <template v-else-if="field.format === 'table'">
      <TableArray
        :field="field"
        :path="currentPath"
        :can-add="canAdd"
        :can-remove="canRemove"
        v-model="value"
        @add="handleAdd"
        @remove="handleRemove"
      />
    </template>
    <template v-else-if="field.format === 'card'">
      <CardArray
        :field="field"
        :path="currentPath"
        :can-add="canAdd"
        :can-remove="canRemove"
        v-model="value"
        @add="handleAdd"
        @remove="handleRemove"
      />
    </template>
    <template v-else>
      <component
        :is="field.format"
        :field="field"
        :path="currentPath"
        :can-add="canAdd"
        :can-remove="canRemove"
        v-model="value"
        @add="handleAdd"
        @remove="handleRemove"
        :params="field.params"
      />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { cloneDeep, isFunction, isNumber, isUndefined } from 'lodash';
  import { computed, inject, ref } from 'vue';
  import { ArrayField } from '../../../types/form';
  import TableArray from './table-array/index.vue';
  import ListArray from './list-array/index.vue';
  import CardArray from './card-array/index.vue';

  const formData = ref(inject('formData') ?? {});

  const value = defineModel<any[]>({
    default: [],
    set(val) {
      return val;
    },
  });

  const props = defineProps<{
    field: ArrayField;
    path: string;
  }>();

  const currentPath = computed(() => {
    return props.path ? `${props.path}.${props.field.name}` : props.field.name;
  });

  const maxLength = computed(() => {
    if (isNumber(props.field.maxLength)) {
      return props.field.maxLength;
    }

    if (isFunction(props.field.maxLength)) {
      return props.field.maxLength(value.value, formData.value);
    }

    return undefined;
  });

  const canAdd = computed(() => {
    if (isUndefined(maxLength.value)) {
      return true;
    }

    return value.value.length < maxLength.value;
  });

  const handleAdd = (index: number) => {
    const defaultItem = cloneDeep(props.field.defaultItem);
    const isCard = props.field.format === 'card';
    value.value.splice(isCard ? 0 : index + 1, 0, defaultItem || {});

    setTimeout(() => {
      props.field.onChange?.(value.value, formData);
    }, 0);
  };

  const minLength = computed(() => {
    if (isNumber(props.field.minLength)) {
      return props.field.minLength;
    }

    if (isFunction(props.field.minLength)) {
      return props.field.minLength(value.value, formData.value);
    }

    return 1;
  });

  const canRemove = computed(() => {
    return !!(minLength.value && value.value.length > minLength.value);
  });

  const handleRemove = (index: number) => {
    value.value = value.value.filter((item, i) => i !== index);
    setTimeout(() => {
      props.field.onChange?.(value.value, formData);
    }, 0);
  };

  const rules = computed(() => {
    if (isFunction(props.field.rules)) {
      return props.field.rules(props.field, formData?.value as any, props.path);
    }

    // eslint-disable-next-line no-shadow
    const rules = [...(props.field.rules || [])];

    if (isNumber(maxLength.value)) {
      rules.push({
        maxLength: maxLength.value,
        message: `最多只能添加 ${maxLength.value} 项`,
      });
    }

    if (isNumber(minLength.value)) {
      rules.push({
        minLength: minLength.value,
        message: `至少需要添加 ${minLength.value} 项`,
      });
    }

    return rules;
  });
</script>

<style scoped>
  .array-wrapper {
    margin-bottom: 0;
  }
</style>
