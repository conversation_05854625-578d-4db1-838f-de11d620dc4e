<template>
  <DynamicPage
    :filter="filterConfig"
    :table="tableConfig"
    :operation="operations"
    :batch-operation="batchOperations"
    auto-load
    row-key="_id"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import AppVersionApi from '@/api/AppVersion';
  import CompetitorsAppApi from '@/api/CompetitorsApp';
  import FileTree from '../components/file-tree.vue';

  // Filter 配置
  const filterConfig = ref({
    formSchema: {
      fields: [
        {
          name: 'name',
          label: 'name',
          type: 'select',
          format: 'singleSelect',
          placeholder: '请输入name',
          source: {
            data: () => {
              return CompetitorsAppApi.list().then((res) => {
                return res.data.list?.map((v) => ({
                  label: v.name,
                  value: v.name,
                }));
              });
            },
          },
        },
        {
          name: 'version',
          label: 'version',
          type: 'text',
          placeholder: '请输入version',
        },
        {
          name: 'appLink',
          label: 'appLink',
          type: 'text',
          placeholder: '请输入appLink',
        },
        {
          name: 'brand',
          label: 'brand',
          type: 'select',
          format: 'singleSelect',
          source: {
            data: [
              {
                label: 'oppo',
                value: 'oppo',
              },
              {
                label: 'vivo',
                value: 'vivo',
              },
              {
                label: '小米',
                value: '小米',
              },
            ],
          },
          placeholder: '请输入brand',
        },
        {
          name: 'releaseDate',
          label: 'releaseDate',
          type: 'datePicker',
          placeholder: '请输入releaseDate',
        },
      ],
    },
  });

  // 数据加载方法
  async function loadData(filter, pagination) {
    return AppVersionApi.list({
      ...filter,
      ...pagination,
    });
  }

  // Table 配置
  const tableConfig = ref({
    columns: [
      {
        title: 'name',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: 'version',
        dataIndex: 'version',
        ellipsis: true,
      },
      {
        title: 'appLink',
        dataIndex: 'appLink',
        ellipsis: true,
        customRender: {
          type: 'download',
        },
      },
      {
        title: 'brand',
        dataIndex: 'brand',
        ellipsis: true,
      },
      {
        title: 'releaseDate',
        dataIndex: 'releaseDate',
        ellipsis: true,
        customRender: {
          type: 'datetime',
        },
      },
      {
        title: '操作',
        dataIndex: 'operations',
        customRender: {
          type: 'operations',
          props: {
            operations: [
              {
                text: '编辑',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    'title': '编辑',
                    'esc-to-close': false,
                  },
                  contentType: 'form',
                  form: {
                    formSchema: {
                      fields: [
                        {
                          name: 'name',
                          label: 'name',
                          type: 'text',
                          placeholder: '请输入name',
                        },
                        {
                          name: 'version',
                          label: 'version',
                          type: 'text',
                          placeholder: '请输入version',
                        },
                        {
                          name: 'appLink',
                          label: 'appLink',
                          type: 'text',
                          placeholder: '请输入appLink',
                        },
                        {
                          name: 'brand',
                          label: 'brand',
                          type: 'text',
                          placeholder: '请输入brand',
                        },
                        {
                          name: 'releaseDate',
                          label: 'releaseDate',
                          type: 'datePicker',
                          placeholder: '请输入releaseDate',
                        },
                      ],
                    },
                  },
                  getDefaultValue(rowData) {
                    const { _id: id } = rowData;
                    return AppVersionApi.getById(id).then((res) => res.data);
                  },
                  action: async ({ formData, record, refreshTable }) => {
                    const { _id: id } = record;
                    await AppVersionApi.update(id, formData);
                    refreshTable();
                  },
                },
              },
              {
                text: '对比',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    'title': '文件对比',
                    'esc-to-close': false,
                    'fullscreen': true,
                  },
                  contentType: 'custom',
                  custom: FileTree,
                },
              },
              {
                text: '删除',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    title: '确认删除',
                  },
                  contentType: 'text',
                  text: '确认删除？',
                  action: async ({ record, refreshTable }) => {
                    const { _id: id } = record;
                    await AppVersionApi.delete(id);
                    refreshTable();
                  },
                },
              },
            ],
          },
        },
      },
    ],
    isPageable: true,
    pagination: {
      current: 1,
      pageSize: 10,
      showPageSize: true,
      showTotal: true,
      total: 0,
      showJumper: true,
      pageSizeOptions: [10, 20, 50],
    },
    load: {
      action: loadData,
    },
  });

  // 操作按钮配置
  const operations = ref([
    {
      text: '手动抓取',
      props: {
        type: 'primary',
      },
      clickActionType: 'action',
      action: async (data) => {
        await AppVersionApi.create({});
        Message.success('操作成功');
        data.refreshTable();
      },
    },
    {
      text: '批量操作',
      props: {
        type: 'primary',
      },
      clickActionType: 'batch',
    },
  ]); // 单个操作按钮
  const batchOperations = ref([]); // 批量操作按钮
</script>
