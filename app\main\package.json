{"name": "arco-design-pro-vue", "description": "Arco Design Pro for Vue", "version": "1.0.0", "private": true, "author": "ArcoDesign Team", "license": "MIT", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@arco-design/web-vue": "^2.44.7", "@types/pinyin": "^2.10.2", "@types/uuid": "^10.0.0", "@vueuse/core": "^9.3.0", "axios": "^0.24.0", "dayjs": "^1.11.5", "echarts": "^5.4.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "monaco-editor": "^0.33.0", "nanoid": "^5.1.5", "nprogress": "^0.2.0", "pinia": "^2.0.23", "pinyin": "4.0.0-alpha.2", "query-string": "^8.0.3", "sortablejs": "^1.15.0", "uuid": "^9.0.1", "vite-plugin-monaco-editor": "^1.1.0", "vue": "^3.2.40", "vue-echarts": "^6.2.3", "vue-i18n": "^9.2.2", "vue-router": "^4.0.14", "vuedraggable": "^4.1.0"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@repo/kola": "workspace:*", "@repo/sdk": "workspace:^", "@repo/seed": "workspace:^", "@types/lodash": "^4.14.186", "@types/mockjs": "^1.0.7", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.40.0", "@typescript-eslint/parser": "^5.40.0", "@vitejs/plugin-vue": "^3.1.2", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/babel-plugin-jsx": "^1.1.1", "consola": "^2.15.3", "cross-env": "^7.0.3", "eslint": "^8.25.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.6.0", "husky": "^8.0.1", "less": "^4.1.3", "lint-staged": "^13.0.3", "mockjs": "^1.1.0", "postcss-html": "^1.5.0", "prettier": "^2.7.1", "rollup": "^3.9.1", "rollup-plugin-visualizer": "^5.8.2", "stylelint": "^14.13.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "typescript": "^4.8.4", "unplugin-vue-components": "^0.24.1", "vite": "^3.2.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-imagemin": "^0.6.1", "vite-svg-loader": "^3.6.0", "vue-highlight-code": "^0.2.0", "vue-tsc": "^1.0.14"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}}