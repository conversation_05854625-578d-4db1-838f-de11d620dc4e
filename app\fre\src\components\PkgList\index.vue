<template>
  <div class="fixed bottom-0 right-0 bg-green-50 w-[400px] h-[400px] overflow-auto rounded-xl shadow-lg">
    <a-list item-layout="horizontal" :grid="{ column: 1 }" :data-source="pkgList">
      <template #header>
        <div class="text-center text-lg">任务列表</div>
      </template>
      <template #renderItem="{ item, index }">
        <a-list-item>
          <a-card :title="item.title" class="shadow-[0px_4px_8px_rgba(0,0,0,0.2)]">
            <template #title>
              <div>{{ item.appName + '-' + item.brand }}</div>
            </template>
            <div
              v-for="(info, index) in item.downloadUrl"
              :key="index"
              v-if="item.downloadUrl && item.downloadUrl.length"
              class="group"
            >
              <div class="title group-hover:text-pretty group-hover:underline font-bold text-lg">{{ info.type }}</div>
              <a :href="info.url">{{ info.url }}</a>
            </div>
            <div v-else class="terminal-loader">
              <div class="text">Loading...</div>
            </div>
          </a-card>
        </a-list-item>
      </template>
    </a-list>
  </div>
</template>

<script lang="ts" setup>
  import { toRaw } from 'vue';

  const { pkgList } = defineProps<{ pkgList: any[] }>();
  console.log('pkgList', toRaw(pkgList));
</script>

<style lang="less" scoped>
  @keyframes blinkCursor {
    50% {
      border-right-color: transparent;
    }
  }

  @keyframes typeAndDelete {
    0%,
    10% {
      width: 0;
    }
    45%,
    55% {
      width: 6.2em;
    } /* adjust width based on content */
    90%,
    100% {
      width: 0;
    }
  }

  .terminal-loader {
    color: #0f0;
    font-family: 'Courier New', Courier, monospace;
    font-size: 1em;
    width: 12em;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    .text {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      border-right: 0.2em solid green; /* Cursor */
      animation: typeAndDelete 4s steps(11) infinite, blinkCursor 0.5s step-end infinite alternate;
    }
  }
</style>
