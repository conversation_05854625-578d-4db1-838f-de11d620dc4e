{"name": "qk-build", "version": "1.0.0", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "private": true, "workspaces": ["packages/*"], "bin": {"kola": "packages/cli/index.js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@arco-design/web-vue": "^2.56.0", "@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/traverse": "^7.26.5", "@sentry/vite-plugin": "^2.22.6", "@sentry/vue": "^8.40.0", "@vueuse/core": "^9.13.0", "adm-zip": "^0.5.16", "axios": "^0.24.0", "body-parser": "^1.20.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dompurify": "^3.1.6", "echarts": "^5.4.3", "esdk-obs-browserjs": "^3.23.5", "express": "^4.19.2", "highlight.js": "^11.10.0", "husky": "^9.1.6", "jest": "^29.7.0", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "md5": "^2.3.0", "mitt": "^3.0.1", "mongoose": "^8.9.2", "node-schedule": "^2.1.1", "nprogress": "^0.2.0", "obfuscator-io-deobfuscator": "^1.0.4", "pinia": "^2.1.7", "pnpm": "^9.0.1", "qrcode.vue": "^3.4.1", "qs": "^6.13.1", "query-string": "^8.2.0", "sortablejs": "^1.15.2", "supertest": "^6.3.3", "uuid": "^9.0.1", "vue": "^3.5.12", "vue-echarts": "^6.6.8", "vue-i18n": "^9.9.1", "vue-router": "^4.2.5"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/lodash": "^4.14.202", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.7", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^3.2.0", "@vitejs/plugin-vue-jsx": "^2.1.1", "@vue/babel-plugin-jsx": "^1.2.1", "consola": "^2.15.3", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.21.1", "less": "^4.2.0", "lint-staged": "^13.3.0", "mockjs": "^1.1.0", "postcss-html": "^1.6.0", "prettier": "^2.8.8", "rollup": "^2.79.1", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "^15.3.0", "stylelint-config-recommended": "^11.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^25.0.0", "stylelint-config-standard-less": "^3.0.1", "stylelint-order": "^5.0.0", "typescript": "^4.9.5", "unplugin-vue-components": "^0.24.1", "vite": "^3.2.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-imagemin": "^0.6.1", "vite-svg-loader": "^3.6.0", "vue-tsc": "^1.8.27"}, "scripts": {"build": "pnpm -r run build", "test": "pnpm -r run test", "start": "pnpm -r run start", "postinstall": "husky install"}}