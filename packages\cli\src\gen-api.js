/**
 * 生成 API 调用文档
 * @param {string} modelName - 模型名称
 * @param {string} baseUrl - 接口基础路径（例如 /api）
 * @returns {string} - API 调用文档代码
 */
function generateApiDoc(modelName, baseUrl = '/api') {
  const apiDoc = `import { http } from '@/utils/http';

const ${modelName}Api = {
  // 创建${modelName}
  create(data) {
    return http.post('${baseUrl}/${modelName}s', data);
  },

  // 获取${modelName}列表
  list(params) {
    return http.get('${baseUrl}/${modelName}s', { params });
  },

  // 获取单个${modelName}
  getById(id) {
    return http.get(\`${baseUrl}/${modelName}s/\${id}\`);
  },

  // 更新${modelName}
  update(id, data) {
    return http.put(\`${baseUrl}/${modelName}s/\${id}\`, data);
  },

  // 删除${modelName}
  delete(id) {
    return http.delete(\`${baseUrl}/${modelName}s/\${id}\`);
  },
};

export default ${modelName}Api;
`;

  return apiDoc;
}

module.exports = {
  generateApiDoc,
};
