const axios = require('axios');

async function getHonorLink(packageName) {
  const url = 'https://quickapp-gw-drcn.hispace.hihonorcloud.com/appresource/v1/rpk/quickapp/appdetail';
  const params = {
    appType: 0,
    pkgName: packageName,
    verCode: -1,
  };

  try {
    const res = await axios.post(url, params, {
      headers: {
        'x-trace-id': '7872ba7d-91e3-4fa6-b68f-91e7fda509be-1742978964415',
        'x-android-version-code': '34',
        'x-device-model': 'CLK-AN00',
        'x-token':
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ4LXVkaWQiOiI5OThlMTg0Yi04NDU2LTQ0MzEtOThhNy1kOWQzNTUxYTBhOWUiLCJpYXQiOjE3NDI1NDA0NTN9.w2gvG4WFgo9wa6ADtYQ169xAaWkWcxvGkiRuqmlKNvQ',
        'x-udid': '998e184b-8456-4431-98a7-d9d3551a0a9e',
        'x-android-version': 'REL',
        'x-inner-name': 'quick-app',
        'packageName': 'com.xunmei.xuanwen',
        'x-sys-version': 'magic',
        'x-app-version-code': '120012301',
        'x-app-pkg': 'com.hihonor.quickengine',
        'x-app-version': '12.0.12.301',
        'Content-Type': 'application/json',
      },
    });
    if (res.data.code === '0') {
      return res.data.data;
    }
    return null;
  } catch (error) {
    console.log(error);
    return null;
  }
}

module.exports = {
  getHonorLink,
};
