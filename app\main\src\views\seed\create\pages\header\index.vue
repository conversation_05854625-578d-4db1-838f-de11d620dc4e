<template>
  <div class="lowcode-header">
    <div class="lowcode-header-title">
      <span>{{ appInfo.name }}</span>
    </div>
    <div class="lowcode-header-toolbar">
      <a-button
        v-for="button in toolbarButtons"
        :key="button.key"
        :type="button.type"
        :disabled="button.disabled"
        @click="button.handler"
      >
        <template #icon>
          <component :is="button.icon" />
        </template>
        {{ button.title }}
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useRoute } from 'vue-router';
  import SeedApp from '@/api/SeedApp';

  interface ToolbarButton {
    key: string;
    type: 'text' | 'dashed' | 'outline' | 'primary' | 'secondary';
    title: string;
    icon: any;
    disabled: boolean;
    handler: () => void;
  }

  interface Props {
    toolbarButtons?: ToolbarButton[];
  }

  const props = withDefaults(defineProps<Props>(), {
    toolbarButtons: () => []
  });

  const router = useRoute();
  const appInfo = ref<Record<string, string>>({});

  // eslint-disable-next-line no-underscore-dangle
  SeedApp.getById(router.params._id).then((res: any) => {
    console.log('res', res);
    appInfo.value = res.data;
  });
</script>

<style>
  .lowcode-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    min-height: 40px;
    background: #fff;
    margin-left: 1px;
    border-bottom: 1px solid #e5e6eb;
    padding: 0 24px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
  }

  .lowcode-header-title {
    font-weight: 500;
    font-size: 16px;
    color: #1d2129;
  }

  .lowcode-header-toolbar {
    display: flex;
    align-items: center;
  }

  .lowcode-header-toolbar .arco-btn + .arco-btn {
    margin-left: 8px;
  }
</style>
