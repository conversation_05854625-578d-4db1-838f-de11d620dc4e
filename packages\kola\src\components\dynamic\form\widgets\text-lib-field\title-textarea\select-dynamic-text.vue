<template>
  <a-link @click="insertDynamicText">插入动态词库</a-link>
  <a-modal v-model:visible="visible" :footer="false" @cancel="onCancel">
    <template #title> 插入动态词库 </template>
    <div>
      <a-table
        :scroll="{ x: '100%', y: 400 }"
        :loading="loading"
        :columns="columns"
        :data="dynamicTextLib"
        :pagination="false"
      >
        <template #operation="{ record }">
          <a-button type="text" @click="insert(record)">插入</a-button>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue';

  const emit = defineEmits(['insert']);
  const props = defineProps({
    dynamicTextLibRequest: {
      type: Function,
      default: () => Promise.resolve([]),
    },
    textLibColumns: {
      type: Array,
      default: () => undefined,
    },
  });
  const columns = computed(() => {
    return (
      props.textLibColumns || [
        {
          title: '词包名',
          dataIndex: 'name',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '默认词',
          dataIndex: 'defaultWord',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '替换词',
          dataIndex: 'display',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '操作',
          dataIndex: 'name',
          ellipsis: true,
          tooltip: true,
          slotName: 'operation',
        },
      ]
    );
  });
  const loading = ref(false);
  const visible = ref(false);
  const dynamicTextLib = ref([]);

  onMounted(async () => {
    loading.value = true;
    const textLib = await props.dynamicTextLibRequest();
    dynamicTextLib.value = textLib.map((item) =>
      Object.assign(item, {
        display: (item.words || item.replaceWords)?.join(','),
      })
    );
    loading.value = false;
  });

  function insert(word) {
    emit('insert', word);
    onCancel();
  }

  function onCancel() {
    visible.value = false;
  }

  function insertDynamicText() {
    visible.value = true;
  }
</script>
