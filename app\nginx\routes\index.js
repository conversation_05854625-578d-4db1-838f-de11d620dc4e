const express = require('express');

const router = express.Router();
const fs = require('fs');
const { nginx } = require('../config');
const { checkNginx, startNginx } = require('../utils/nginx');

/* GET home page. */
router.get('/', function (req, res) {
  res.render('index', { title: 'Express' });
});

router.get('/nginx/info', function (req, res, next) {
  res.setHeader('Access-Control-Allow-Origin', '*');

  fs.readFile(nginx.configFile, 'utf8', function (err, data) {
    if (err) {
      res.send(err);
      return;
    }
    res.json({ data });
  });
});

router.put('/nginx/info', async function (req, res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  const originText = fs.readFileSync(nginx.configFile, 'utf8').toString();
  try {
    const { data } = req.body;
    fs.writeFileSync(nginx.configFile, data);
    const signal = await checkNginx();
    console.log('signal', signal);
    if (signal.indexOf('is ok') === -1) {
      fs.writeFileSync(nginx.configFile, originText);
      res.send({
        message: 'nginx配置有误, 请检查nginx配置文件是否正确',
      });
      return;
    }
    console.log('w23232323');

    const startMsg = await startNginx();
    console.log(startMsg);
    if (startMsg.indexOf('Successfully') === -1) {
      fs.writeFileSync(nginx.configFile, originText);
      res.send({
        message: 'nginx启动, 请检查nginx配置文件是否正确',
      });
      return;
    }
    res.send({
      data: startMsg,
    });
  } catch (error) {
    console.log('error', error);
    fs.writeFileSync(nginx.configFile, originText);
    res.send(error);
  }
});

module.exports = router;
