<template>
  <div class="page">
    <h1>相似度检测</h1>
    <p>上传需要校验的两个包</p>
    <a-upload v-model:file-list="fileList" :limit="2" :auto-upload="false" />
    <a-button type="primary" style="margin: 24px 0" @click="submit"
      >提交</a-button
    >

    <div>
     <a-table :columns="columns" :data="tableData"></a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const fileList = ref<any[]>([]);
  const tableData = ref<any[]>([])

  const columns = [
    {
      title: 'RPK文件名',
      dataIndex: 'file1Path',
      key: 'file1Path',
    },
    {
      title: '相似文件',
      dataIndex: 'file2Path',
      key: 'file2Path',
    },
    {
      title: '相似度',
      dataIndex: 'similarity',
      key: 'similarity',
    },
  ];

  function submit() {
    console.log(fileList.value);
    if (fileList.value.length !== 2) {
      console.error('请上传两个文件');
      return;
    }

    const formData = new FormData();
    formData.append('file', fileList.value[0].file);
    formData.append('file', fileList.value[1].file);

    fetch('/api/sim-pack', {
      method: 'POST',
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server error: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('相似度检测结果:', data);
        // 这里可以更新表格数据
        tableData.value = data.results.data;
      })
      .catch((error) => {
        console.error('提交失败:', error);
      });
  }
</script>

<style>
  .page {
    padding: 20px;
    background: #fff;
  }
</style>
