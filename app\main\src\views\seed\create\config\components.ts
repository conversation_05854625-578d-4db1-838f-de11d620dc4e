import { metaInfo } from '@repo/seed';

console.log('metaInfo1', metaInfo);

metaInfo.forEach((item) => {
  item.id = item.name;
  item.name = `S${item.name}`;
});

interface IPage {
  id: string;
  name: string;
}

// 页面列表
export const pageList: Array<IPage> = [];


// 物料区 Tabs 配置（每个 list 项为完整组件对象）
export const materialTabs = [
  {
    key: 'battery',
    title: '省电',
    list: metaInfo.filter((item) => item.category === 'battery'),
  },
  {
    key: 'ecommerce',
    title: '电商',
    list: [],
  },
  { key: 'walk', title: '走步', list: [] },
  { key: 'novel', title: '小说', list: [] },
  { key: 'wallpaper', title: '壁纸', list: [] },
];
