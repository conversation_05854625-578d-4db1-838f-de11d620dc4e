<template>
  <router-view v-slot="{ Component, route }">
    <Assistant type="adp" />
    <transition name="fade-transform" mode="out-in" appear>
      <component
        :is="Component ?? route.matched[0]?.components?.content"
        v-if="route.meta.ignoreCache"
        :key="route.path"
      />
      <keep-alive v-else :include="cacheList">
        <component
          :is="Component ?? route.matched[0]?.components?.content"
          :key="route.path"
        />
      </keep-alive>
    </transition>
  </router-view>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import Assistant from '@repo/kola/src/components/assistant/index.vue';
  import { useTabBarStore } from '@/store';

  const tabBarStore = useTabBarStore();
  const cacheList = computed(() => tabBarStore.getCacheList);
</script>

<style scoped lang="less"></style>
