<template>
  <div class="tag-operation">
    <div class="tip">您可以选择标签，查看模块问题</div>
    <div class="items">
      <a-button
        v-for="item in tagsList"
        :key="item.code"
        size="mini"
        :class="getTagClass(item.code)"
        @click="handleClick(item.code)"
        >{{ item.label }}</a-button
      >
    </div>
    <div class="content">
      <ModuleCard v-if="activeTag !== ''" :title="title" :question-list="questionList" type="hot" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, inject, Ref, computed } from 'vue';
  import ModuleCard from '../common/module-card.vue';
  import { scrollToBottom, scrollToTop } from '../../utils/scroll';
  import useDefaultQuestions from '../../hooks/use-default-questions';

  const scrollWrapper = inject<Ref<HTMLElement | null | undefined> | undefined>('scrollWrapper');
  const { getDefaultQuestions } = useDefaultQuestions();
  const activeTag = ref<number | string>('');
  const title = ref('');
  const questionData = getDefaultQuestions();
  const tagsList = computed(() => {
    const arr = questionData.value.map((item: any, index: number) => ({
      code: index,
      label: item.classify,
    }));
    if (arr.length > 1) {
      [arr[0], arr[1]] = [arr[1], arr[0]];
    }
    return arr;
  });
  const questionList = computed(() => {
    return questionData.value[activeTag.value]?.list || [];
  });
  const handleClick = (type: string | number) => {
    if (activeTag.value === type) {
      activeTag.value = '';
      title.value = '';
      scrollToTop(scrollWrapper);
      return;
    }
    activeTag.value = type;
    title.value = tagsList.value.find((item) => item.code === type)?.label || '';
    scrollToBottom(scrollWrapper);
  };

  const getTagClass = (code: number | string) => {
    return activeTag.value === code ? 'tag-operation-item active' : 'tag-operation-item';
  };
</script>

<style scoped>
  .tag-operation {
    margin-top: 16px;
    width: 500px;

    .tip {
      padding: 0 23px;
      margin-bottom: 8px;
      font-weight: 400;
      font-size: 14px;
      color: rgb(0 0 0 / 52%);
      line-height: 18px;
      text-align: left;
      font-style: normal;
    }

    .items {
      padding: 0 23px;
      width: 100%;

      .tag-operation-item {
        display: inline-block;
        margin-left: 10px;
        margin-bottom: 6px;
        color: rgb(75 75 75);
        border-color: rgb(233 233 233);
        border-radius: 4px;
        border: none;

        &.active {
          color: #fff;
          border-color: #165dff;
          background-color: #165dff;
        }
      }

      .tag-operation-item:hover {
        color: rgb(var(--link-4));
        border-color: rgb(var(--primary-3));
      }
    }

    .content {
      padding: 0 23px;
    }
  }
</style>
