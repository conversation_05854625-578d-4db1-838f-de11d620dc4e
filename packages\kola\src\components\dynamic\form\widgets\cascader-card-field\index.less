.cascader-container {
  display: flex;

  .cascader-content {
    flex: 1;
    border-radius: 8px;
    margin-right: 8px;
    height: 500px;
    border: 1px solid var(--color-neutral-3);
    overflow: hidden;
    display: flex;
  }

  .cascader-result {
    width: 240px;
    border-radius: 8px;
    height: 500px;
    border: 1px solid var(--color-neutral-3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .title,
  .result-title {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    color: rgb(var(--gray-10));
    font-weight: 500;
    line-height: 1.5715;
    text-align: left;
    background-color: var(--color-neutral-2);
    border-bottom: 1px solid var(--color-neutral-3);
    flex: none;
    height: 39px;
    display: flex;
    align-content: center;
    justify-content: space-between;
  }
}
