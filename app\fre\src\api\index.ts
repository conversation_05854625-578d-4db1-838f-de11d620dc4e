async function getAppList() {
  try {
    const response = await fetch('/api/appList');
    if (!response.ok) {
      throw new Error('网络响应不正常');
    }

    return await response.json();
  } catch (error) {
    console.error('获取数据失败:', error);
  }
}

async function getSdkVersion() {
  try {
    const response = await fetch('/api/sdkVersion');
    if (!response.ok) {
      throw new Error('网络响应不正常');
    }

    return await response.json();
  } catch (error) {
    console.error('获取数据失败:', error);
  }
}

// 获取打包结果
async function getPackResult() {
  try {
    const response = await fetch('/api/packResult');
    if (!response.ok) {
      throw new Error('网络响应不正常');
    }

    return await response.json();
  } catch (error) {
    console.error('获取数据失败:', error);
  }
}

// 删除打包任务
async function deletePackTask(taskId: string) {
  try {
    const response = await fetch(`/api/delTask  `, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ taskId }),
    });
    return await response.json();
  } catch (error) {
    console.error('删除任务失败:', error);
  }
}

// 提交打包信息
async function submitPack(formDate: any) {
  try {
    const response = await fetch('/api/pack', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formDate),
    });
    if (!response.ok) {
      throw new Error('网络响应不正常');
    }

    return await response.json();
  } catch (error) {
    console.error('获取数据失败:', error);
  }
}

// 更新对接层
async function updateAdInterface() {
  try {
    const response = await fetch('/api/updateAdInterface');
    return await response.json();
  } catch (error) {
    console.error('更新对接层失败:', error);
  }
}

export { getAppList, getSdkVersion, submitPack, getPackResult, deletePackTask, updateAdInterface };
