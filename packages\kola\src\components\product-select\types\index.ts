import { DForm } from '../../dynamic/types/form';
import { Column, DTable, Filter, Pagination } from '../../dynamic/types/table';

export interface DAccount {
  id: string;
  name: string;
}

export interface Apply {
  label: string;
}

export interface ProductSelect {
  tips?: string; // 提示词
  filter: DForm;
  table: DTable;
  getCustomParams?: () => Record<string, any>;
  applyList: Apply[]; // table 应用模式
  accountList: () => Promise<DAccount[]>; // 获取账号列表
  projectList?: () => Promise<DAccount[]>; // 获取项目列表
}
