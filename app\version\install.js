const Version = require('./model/version');
const { saveRpk } = require('./service/unzip');
const mongoose = require('mongoose');
mongoose
  .connect('mongodb://127.0.0.1:27017/fe')
  .then(() => {
    console.log('数据库连接成功');
  })
  .catch((e) => {
    console.log('数据库连接失败');
  });

async function main() {
  const versionList = await Version.find({});

  while (versionList.length > 0) {
    const versionIns = versionList.shift();
    const { appLink, name, version, brand } = versionIns;
    console.log(versionIns);
    await saveRpk(appLink, `${name}-${version}-${brand}`);
    console.log('下载完成');
  }
}

main();
