<template>
  <div class="form-mask" v-if="showForm">
    <a-watermark content="自动打包">
      <a-form
        :model="formData"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 14 }"
        :rules="rules"
        ref="formRef"
        style="background: #fff; width: 700px; border-radius: 10px; padding: 20px"
      >
        <a-form-item label="产品名称" name="appName">
          <a-select
            v-model:value="formData.appName"
            :options="appNameOptions"
            :filter-option="filterOption"
            mode="multiple"
            option-filter-prop="label"
            placeholder="请选择产品名称"
            :rules="rules.appName"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="厂商" name="brand">
          <a-select v-model:value="formData.brand" :options="brandOptions" />
        </a-form-item>
        <a-form-item label="当前版本" name="version">
          <a-select v-model:value="versionOptions" disabled mode="multiple" option-filter-prop="label" />
        </a-form-item>
        <a-form-item label="SDK版本" name="sdkVersion">
          <a-select
            v-model:value="formData.sdkVersion"
            :options="sdkVersionOptions"
            show-search
            placeholder="请选择产品名称"
          />
        </a-form-item>
        <a-form-item label="操作" name="operations">
          <a-checkbox-group v-model:value="formData.operations" :options="operationsOptions" />
        </a-form-item>
        <div style="text-align: right">
          <a-button type="primary" @click="onSubmit">提交</a-button>
          <a-button @click="showForm = false" style="margin-left: 10px">取消</a-button>
        </div>
      </a-form>
    </a-watermark>
  </div>
  <div>
    <a-button type="primary" @click="showForm = true" :disabled="!isConnected">构建</a-button>
    <a-tag color="red" style="float: right" v-if="!isConnected">与服务器断开链接</a-tag>
  </div>
  <a-table
    bordered
    :data-source="resultList"
    :columns="tableColumns"
    :span-method="{ rowspan: 2 }"
    :scroll="{ y: 500 }"
    :row-height="40"
    :pagination="false"
    :style="{ marginTop: '10px', height: 'calc(50vh)' }"
  >
    <template #bodyCell="{ column, record }">
      <a-popover
        @openChange="showQrcode(item.value, $event)"
        title="扫码下载"
        v-if="column.key === 'operations'"
        v-for="item in record.operations"
        :key="item.value"
        type="primary"
      >
        <a-button :href="item.value" target="_blank" type="primary" style="margin-right: 10px">
          {{ item.label }}
        </a-button>
        <template #content>
          <img :src="qrcodeSrc" />
          {{ item.value }}
        </template>
      </a-popover>
      <div v-if="column.key === 'taskId'">
        {{ record.taskId }}
        <a-popconfirm
          title="确定删除该任务吗"
          ok-text="是"
          cancel-text="否"
          @confirm="taskDelConfirm(record.taskId)"
          @cancel="taskDelCancel"
        >
          <a href="#" style="color: #1677ff; display: inline-block">删除</a>
        </a-popconfirm>
      </div>
      <a-popover v-if="column.key === 'taskStatus'">
        {{ record.taskStatus }}
        <template #content>
          {{ record.failedResult || '成功' }}
          <a-button type="link" v-if="record.errorCode === 9" @click="updateInterface()">更新对接层</a-button>
        </template>
      </a-popover>
    </template>
  </a-table>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { generateQrcode } from '../utils/Qrcode.ts';
  import pinyin from 'pinyin';

  import formConfig from '../config/formConfig.ts'; // 表单配置
  import { getAppList, getSdkVersion, submitPack, getPackResult, deletePackTask, updateAdInterface } from '@/api';
  import { useWebSocket } from '../utils/useWebSocket.ts';

  interface FormData {
    appName: string[];
    brand: string;
    sdkVersion: string[];
    operations: string[];
  }
  interface resultList {
    appName: string;
    brand: string;
    sdkVersion: string;
    version: string;
    taskStatus: string;
    timeStamp: string;
    timeCost: string;
    operations: { label: string; value: string }[];
    taskId: string;
    failedResult: string;
    errorCode: number;
  }
  interface VersionInfo {
    name: string;
    key: string;
    versionName: string;
    versionCode: number;
  }
  const formData = reactive<FormData>({
    appName: [],
    brand: '',
    sdkVersion: [],
    operations: [],
  });
  const qrcodeSrc = ref('');
  const tableColumns = ref(formConfig.columns); // 表格列
  const brandOptions = ref(formConfig.brandOptions); // 厂商
  const sdkVersionOptions = ref([]); // sdk版本号
  const versionInfo = ref<VersionInfo[]>([]); // 产品版本信息
  const operationsOptions = ref(formConfig.operationsOptions); // 操作类型
  const appNameOptions = ref(formConfig.appNameOptions); // 产品名称
  const resultList = ref<resultList[]>([]); // 构建结果
  const versionOptions = ref<string[]>([]); // 产品版本号
  const showForm = ref(false); // 是否显示表单
  const { messages, sendMessage, isConnected } = useWebSocket('/ws', {
    reconnectInterval: 1000,
    maxReconnectAttempts: 1,
  });
  const rules = ref({
    appName: [{ required: true, message: '请选择产品名称', trigger: 'change' }],
    brand: [{ required: true, message: '请选择厂商', trigger: 'change' }],
    sdkVersion: [{ required: true, message: '请选择SDK版本', trigger: 'change' }],
    operations: [{ required: true, message: '请选择操作', trigger: 'change' }],
  });
  const formRef = ref();
  // 拼音搜索的过滤函数
  const filterOption = (input: string, option: any) => {
    // 获取拼音并将其转为小写字母
    const pinyinValue = pinyin(option.label, { style: pinyin.STYLE_NORMAL })
      .map((item: any) => item[0])
      .join('')
      .toLowerCase();

    // 获取搜索输入框的值并转为小写
    const searchValue = input ? input.toLowerCase() : '';
    return pinyinValue.includes(searchValue);
  };
  const onSubmit = () => {
    formRef.value
      .validate()
      .then(() => {
        const params = formData.appName.map((appName) => ({
          appName,
          brand: formData.brand,
          sdkVersion: formData.sdkVersion,
          key: appNameOptions.value.find((item: any) => item.label === appName)?.key,
          pkgType: formData.operations,
          timeStamp: new Date().getTime(),
        }));

        submitPack(params)
          .then((res) => {
            console.log(res);
            message.success('创建任务成功');
            getTaskList();
          })
          .catch((err) => {
            message.error('创建任务失败');
          });
        console.log(params);
        showForm.value = false;
      })
      .catch(() => {
        console.log('error');
      });
  };
  const showQrcode = (value: string, event: boolean) => {
    if (event) {
      generateQrcode('http://fe.local' + value).then((res) => {
        qrcodeSrc.value = res;
      });
    }
  };
  watch(formData, () => {
    if (formData.brand === '') {
      return;
    }
    const arr: string[] = [];
    formData.appName.forEach((appName) => {
      arr.push(`${appName}:v${getVersionOptions(appName, formData.brand)}`);
    });
    versionOptions.value = arr.filter((item: string) => item !== undefined);
  });
  const getVersionOptions = (appName: string, brand: string) => {
    return versionInfo.value.find(
      (item: any) => item.name === appName && item.key.indexOf(brand) !== -1 && brand !== ''
    )?.versionName;
  };

  onMounted(() => {
    getAppList().then((res) => {
      versionInfo.value = res.appList;
      // 根据name去重
      const nameMap = new Map();
      res.appList.forEach((item: any) => {
        if (!nameMap.has(item.name)) {
          nameMap.set(item.name, {
            label: item.name,
            value: item.name,
            key: item.key,
          });
        }
      });
      appNameOptions.value = Array.from(nameMap.values());
      console.log('ip', res.ip);
    });
    getSdkVersion().then((res) => {
      sdkVersionOptions.value = res.sdkVersion.map((item: string) => ({
        label: item,
        value: item,
      }));
    });
    // 获取本地存储
    // const historyList = localStorage.getItem('resultList')
    // if (historyList) {
    //   resultList.value = JSON.parse(historyList)
    // }
    getTaskList();
    // 合并单元格
  });
  const getTaskList = () => {
    getPackResult().then((res) => {
      resultList.value = [];
      res.resultList.sort((a: any, b: any) => {
        return new Date(b.timeStamp).getTime() - new Date(a.timeStamp).getTime();
      });
      const reslist = ref<resultList[]>([]);
      res.resultList.forEach((item: any) => {
        reslist.value.push({
          appName: item.appName,
          brand: item.brand,
          sdkVersion: item.sdkVersion,
          version: item.version,
          taskStatus: item.status,
          timeCost: item.timeCost ? item.timeCost + 's' : '',
          operations: item.result?.map((item: any) => ({ label: item.type, value: item.url })),
          timeStamp: new Date(item.timeStamp || 0).toLocaleString(),
          taskId: item.taskId,
          failedResult: item.failedResult,
          errorCode: item.errorCode,
        });
      });
      resultList.value = reslist.value;
      combineRowSpanFn('taskId');
    });
  };
  watch(messages, (newMessages: any[]) => {
    const latestMessage = newMessages.at(-1); // 获取最新的消息
    if (!latestMessage || latestMessage.type !== 'taskComplete') return; // 如果消息不存在或类型不匹配，直接返回
    getTaskList();
    combineRowSpanFn('taskId');
    // 存储
    // localStorage.setItem('resultList', JSON.stringify(resultList.value))
  });
  // 合并单元格的函数
  const combineRowSpanFn = (key: string) => {
    let arr = resultList.value
      .reduce((result, item) => {
        if (result.indexOf(item[key]) < 0) {
          result.push(item[key]);
        }
        return result;
      }, [])
      .reduce((result, keys) => {
        const children = resultList.value.filter((item) => item[key] === keys);
        result = result.concat(
          children.map((item, index) => ({
            ...item,
            [`${key}RowSpan`]: index === 0 ? children.length : 0,
          }))
        );
        return result;
      }, []);
    resultList.value = arr;
  };
  const taskDelConfirm = (value: string) => {
    deletePackTask(value).then((res) => {
      console.log('res', res);
      if (res.status) {
        message.success('删除任务成功');
      } else {
        message.error('删除任务失败,' + res.message);
      }
      getTaskList();
    });
  };
  const taskDelCancel = () => {
    console.log('taskDelCancel');
  };
  const updateInterface = () => {
    updateAdInterface().then((res) => {
      message.success('更新对接层成功');
    });
  };
</script>

<style>
  .form-mask {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
