import { LOGIN_URL } from '../constants';
import { http } from '../http';
import { removeCookie } from './utils';

function goLogin() {
  const { href, search } = window.location;
  removeCookie('token');
  window.location.href = `${LOGIN_URL}${search ? `${search}&` : `?`}redirect=${encodeURIComponent(href)}`;
}

async function logout(isRequest = true) {
  return new Promise((resolve, reject) => {
    if (!isRequest) {
      resolve(true);
      goLogin();
      return;
    }
    http
      .get('/api/user/logout')
      .then((res) => {
        resolve(res);
        goLogin();
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export default logout;
