<template>
  <div class="dynamic-table">
    <div v-if="table?.topComponent" class="top-component">
      <component
        :is="table?.topComponent"
        :table-data="renderData"
        :selected-keys="selectedKeys"
        @set-selected-keys="setSelectedKeys"
      ></component>
    </div>
    <a-table
      ref="tableDomRef"
      :key="tableKey"
      :row-key="rowKey"
      :loading="loading"
      :pagination="isPageable ? pagination : false"
      :columns="computedColumns"
      :data="renderData"
      :row-selection="rowSelection"
      v-model:selectedKeys="selectedKeys"
      v-model:expanded-keys="expandedKeys"
      :scroll="scroll"
      :scroll-bar="true"
      column-resizable
      class="table"
      @page-size-change="handlePageSizeChange"
      @page-change="handlePageChange"
      @sorter-change="handleSorterChange"
      :virtual-list-props="virtualHeight"
      :load-more="table?.loadMore"
      :bordered="{
        headerCell: true,
      }"
    >
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed, watch, reactive, onBeforeUnmount } from 'vue';
  import { isNil } from 'lodash';
  import formatColumns from './column-render/format-columns';
  import { DTable, RefreshTable, Sorter } from '../types/table';
  import usePagination from './hooks/pagination';
  import usePaginationChange from './hooks/pagination-change';
  import useScroll from './hooks/scroll';
  import useLoadData from './hooks/load-data';
  import useBatch from '../hooks/batch';
  import useRegisterEvent from '../hooks/register-event';
  import useToggleExpanded from './hooks/toggle-expanded';
  import useDispatch from '../hooks/dispatch';

  defineOptions({ name: 'DynamicTable' });
  const tableDomRef = ref<any>(null);
  const tableKey = ref<any>(Date.now());
  const props = withDefaults(
    defineProps<{
      table: DTable;
      autoLoad?: boolean;
      rowKey?: string;
      isMultipleSelection?: boolean;
      isSingleSelection?: boolean;
    }>(),
    {
      autoLoad: true,
      rowKey: 'id',
    }
  );
  const isPageable = computed(() => {
    return props.table.isPageable ?? true;
  });
  const emit = defineEmits<{
    select: [selectedKeys: (string | number)[]];
  }>();

  const computedColumns = computed(() => {
    return formatColumns(props.table.columns);
  });

  const virtualHeight = computed(() => {
    if (props.table.virtualHeight) {
      return { height: props.table.virtualHeight };
    }
    return undefined;
  });

  // 批量操作
  const { selectedKeys, batchTableData } = useBatch();

  // ======== function =========
  const renderData = ref<any[]>([]);

  const { pagination } = usePagination(isPageable.value, props.table?.pagination);

  const rowSelection = computed(() => {
    if (props.isMultipleSelection) {
      const config: any = {
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: false,
      };
      if (!isNil(props.table.checkStrictly)) {
        config.checkStrictly = props.table.checkStrictly;
      }
      if (!isNil(props.table.showCheckedAll)) {
        config.showCheckedAll = props.table.showCheckedAll;
      }
      return config;
    }

    if (props.isSingleSelection) {
      return {
        type: 'radio',
        onlyCurrent: false,
      };
    }

    return undefined;
  });

  const sorter = reactive<Sorter>({
    sortBy: '',
    sortDirection: '',
  });
  const { loadData, loading } = useLoadData({
    isPageable: isPageable.value,
    pagination,
    loadAction: props.table.load?.action,
    sorter,
  });

  const refreshTable: RefreshTable = (
    { resetPagination, resetPageSize } = {
      resetPagination: false,
      resetPageSize: true,
    }
  ) => {
    if (resetPagination && isPageable.value) {
      pagination.current = 1;
      // resetPageSize 为true 或者不传 清空分页信息
      if (!resetPageSize) {
        pagination.pageSize = pagination.pageSizeOptions[0];
      }
    }

    if (selectedKeys.value.length) {
      selectedKeys.value = [];
    }

    loadData().then((data: any = []) => {
      if (isPageable.value) {
        renderData.value = data.list;
        pagination.total = data.total;
      } else {
        renderData.value = data;
      }
    });
  };
  const { expandedKeys, changeTableExpanded } = useToggleExpanded(renderData);
  useRegisterEvent('refreshTable', refreshTable);
  useRegisterEvent('changeTableExpanded', changeTableExpanded);
  const { handlePageChange, handlePageSizeChange } = usePaginationChange({
    pagination,
    refreshTable,
  });

  function handleSorterChange(dataIndex: string, direction: string) {
    sorter.sortBy = dataIndex;
    sorter.sortDirection = direction;
    refreshTable();
  }

  const scroll = useScroll();

  onMounted(() => {
    if (props.autoLoad) {
      refreshTable();
    }
  });
  // 将selectedKeys传递给父组件
  watch(selectedKeys, () => {
    emit('select', selectedKeys.value);
  });
  function setSelectedKeys(value: any) {
    selectedKeys.value = value;
  }

  function initColumns() {
    tableKey.value = Date.now();
  }

  defineExpose({
    getCurrentRowsData: () => renderData.value,
    initColumns,
    changeTableExpanded,
    setExpandedKeys: (keys: any[]) => {
      expandedKeys.value = keys;
    },
    getExpandedKeys: () => expandedKeys.value,
    tableDomRef,
    getPaginationValue: (isSimple = true) => {
      if (isSimple) {
        return {
          current: pagination.current,
          pageSize: pagination.pageSize,
        };
      }
      return {
        ...pagination,
      };
    },
    setPaginationValue: (data) => {
      Object.assign(pagination, data);
    },
  });

  const dispatch = useDispatch();
  watch(renderData, () => {
    dispatch({
      type: 'updateTableData',
      payload: {
        data: renderData.value,
      },
    });
    batchTableData.value = renderData.value;
  });

  onBeforeUnmount(() => {
    batchTableData.value = [];
  });
</script>

<style scoped lang="less">
  .dynamic-table {
    height: 100%;

    > .top-component {
      position: relative;
    }

    :deep(.arco-table-border-header-cell .arco-table-th) {
      border-right: unset;
    }

    :deep(thead) {
      &:hover {
        .arco-table-th {
          border-right: 1px solid var(--color-neutral-3);
        }
      }
    }

    :deep(.arco-table) {
      .arco-table-th,
      .arco-table-td {
        font-size: 12px;
      }
    }
  }

  .table {
    height: 100%;

    :deep(.arco-table-td) {
      height: 48px;
    }

    :deep(.arco-pagination-options .arco-select) {
      width: 100px;
    }
  }
</style>
