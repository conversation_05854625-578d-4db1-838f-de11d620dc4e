const brightness = {
  getValue: (options) => {
    const { success } = options || {};
    
    // 固定返回0.8作为亮度值
    const mockBrightness = 0.8;
    
    setTimeout(() => {
      if (typeof success === 'function') {
        success({
          value: mockBrightness,
          max: 1,
          min: 0
        });
      }
    }, 300);
  },
  setValue: (options) => {
    const { value, success } = options || {};
    
    setTimeout(() => {
      if (typeof success === 'function') {
        success();
      }
    }, 300);
  },
  getMode: (options) => {
    const { success } = options || {};
    
    // 固定返回1（自动亮度模式）
    const mockMode = 1;
    
    setTimeout(() => {
      if (typeof success === 'function') {
        success({
          mode: mockMode, // 0: 手动模式, 1: 自动模式
        });
      }
    }, 300);
  },
  
  // 设置亮度模式（始终成功）
  setMode: (options) => {
    const { mode, success } = options || {};
    
    setTimeout(() => {
      if (typeof success === 'function') {
        success();
      }
    }, 300);
  },
  setKeepScreenOn: (options) => {
    const { keepScreenOn, success } = options || {};
    
    setTimeout(() => {
      if (typeof success === 'function') {
        success();
      }
    }, 300);
  }
}
module.exports = brightness