<template>
  <div class="fixed w-full h-full bg-black/50 grid place-items-center" @click="() => onCancel()">
    <div class="content w-[500px]" @click="(e) => e.stopPropagation()">
      <header class="mt-2 mb-2 font-bold">打包信息</header>
      <a-form :model="formState">
        <a-form-item label="应用-版本">
          <a-select
            v-model:value="selectApp"
            mode="multiple"
            style="width: 100%"
            placeholder="Please select"
            :options="appOptions"
            @change="handleChange"
          ></a-select>
        </a-form-item>

        <a-form-item label="厂商">
          <a-input v-model:value="formState.brand" :disabled="true" />
        </a-form-item>

        <a-form-item label="SDK版本">
          <a-input v-model:value="formState.sdkVersion" />
        </a-form-item>

        <a-form-item label="包类型">
          <a-radio-group v-model:value="formState.pkgType" button-style="solid">
            <a-radio-button v-for="(type, index) in PKG_TYPE" :value="type">{{ PKG_TYPE_VALUE[index] }}</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="是否升级版本号">
          <a-radio-group v-model:value="formState.versionUp">
            <a-radio :value="1">否</a-radio>
            <a-radio :value="2">是</a-radio>
          </a-radio-group>
        </a-form-item>
        <div class="flex justify-between">
          <a-button @click="onCancel">取消</a-button>
          <a-button type="primary" @click="onSubmit">提交</a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRaw, onMounted, computed } from 'vue';
  import type { UnwrapRef } from 'vue';
  import type { FormInfo, AppInfo } from '@/types/global';
  let emit = defineEmits(['buttonClick']);
  let { selectedData } = defineProps<{ selectedData: AppInfo[] }>();

  const PKG_TYPE = ['all', 'dev', 'release-log', 'release'];
  const PKG_TYPE_VALUE = ['ALL', '测试包', 'log正式包', '提审包'];

  const formState: UnwrapRef<FormInfo> = reactive({
    appName: '趣点-小米',
    appVersion: '1.0.0',
    sdkVersion: '2071',
    pkgType: 'all',
    brand: 'xiaomi',
    versionUp: 1,
    key: 1111,
  });

  const handleChange = (value: string) => {
    console.log(`selected ${value}`);
  };
  const selectApp = ref<string[]>([]);
  const appOptions = toRaw(selectedData).map((item: AppInfo) => ({
    value: item.key,
    label: item.appName + '-' + item.appVersion,
  }));

  onMounted(() => {
    console.log('selectedData', selectedData);
    // 多选时，sdk版本和厂商固定
    const { sdkVersion, brand } = selectedData[0];
    formState.sdkVersion = sdkVersion ?? formState.sdkVersion;
    formState.brand = brand ?? formState.sdkVersion;
    selectApp.value = selectedData.map((item: any) => item.key);
  });

  const onSubmit = () => {
    let submitData = selectedData
      .filter((appInfo: any) => appInfo.key !== undefined && selectApp.value.includes(appInfo.key))
      .map((item: any) => {
        return {
          ...toRaw(formState),
          appName: item.appName,
          appVersion: item.appVersion,
          key: item.key,
        };
      });
    console.log('打包信息:', submitData);
    emit('buttonClick', submitData, true);
  };
  const onCancel = () => {
    emit('buttonClick');
  };
</script>

<style lang="less" scoped>
  .content {
    min-height: 400px;
    border-radius: 8px;

    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(1px);
  }
</style>
