{"name": "fre", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "format": "prettier --write src/", "start": "vite"}, "dependencies": {"ant-design-vue": "4.x", "pinia": "^2.2.4", "pinyin": "4.0.0-alpha.2", "qrcode": "^1.5.4", "segmentit": "^2.0.3", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^20.16.11", "@vitejs/plugin-vue": "^5.1.4", "@vitest/eslint-plugin": "1.1.7", "@vue/eslint-config-prettier": "^10.0.0", "@vue/eslint-config-typescript": "^14.0.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.29.0", "jsdom": "^25.0.1", "less": "^4.2.0", "npm-run-all2": "^6.2.3", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.14", "typescript": "~5.5.4", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.8", "vitest": "^2.1.2", "vue-tsc": "^2.1.6"}}