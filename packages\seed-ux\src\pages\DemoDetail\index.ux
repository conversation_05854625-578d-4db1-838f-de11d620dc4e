<!-- 
  Copyright (c) 2021-present, the hapjs-platform Project Contributors
  SPDX-License-Identifier: Apache-2.0
-->

<template>
  <!-- template里只能有一个根节点 -->
  <div class="wrapper">
    <text class="title">{{ text }}</text>
    <text class="desc"
      >快应用是移动互联网新型应用生态，与手机系统深度整合，为用户提供更加场景化的体验。具备传统APP完整的应用体验，但无需安装、即点即用。
    </text>
    <text class="desc"
      >标准是由主流手机厂商组成的快应用联盟联合制定。其标准的诞生将在研发接口、能力接入、开发者服务等层面建设标准平台，以平台化的生态模式对个人开发者和企业开发者全品类开放
    </text>
    <input
      class="btn"
      type="button"
      value="欢迎使用"
      onclick="onWelcomeBtnClick"
    />
  </div>
</template>

<script>
export default {
  private: {
    text: '快应用是什么？'
  },

  onWelcomeBtnClick() {
    $utils.showToast('快应用：复杂生活的简单答案，让生活更顺畅')
  }
}
</script>

<style lang="scss">
@import './../../assets/styles/style.scss';

.wrapper {
  @include flex-box-mixins(column, center, center);
  margin: 0 16 * $size-factor;
  .title {
    font-size: 18 * $size-factor;
    text-align: center;
    color: $black;
  }
  .desc {
    margin-top: 18 * $size-factor;
    color: $grey;
    font-size: 16 * $size-factor;
  }
  .btn {
    width: 150 * $size-factor;
    height: 42 * $size-factor;
    border-radius: 21 * $size-factor;
    background-color: $brand;
    color: $white;
    font-size: 16 * $size-factor;
    margin-top: 20 * $size-factor;
  }
}
</style>
