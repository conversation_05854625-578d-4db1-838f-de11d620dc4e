import { createRouter, createWebHistory } from 'vue-router';
import type { Router } from 'vue-router';
import PackView from '../views/Pack.vue';
import HomeView from '../views/Home.vue';
import ContentView from '../views/AutoPack.vue';
import LockerView from '../views/Locker.vue';
import WelcomeView from '../views/Welcome.vue';

console.log('import.meta.env', import.meta.env);
const router: Router = createRouter({
  history: createWebHistory('build'),
  routes: [
    {
      path: '/pack',
      name: 'pack',
      component: PackView,
    },
    {
      path: '/',
      component: HomeView,
      children: [
        {
          path: '/auto-build',
          component: ContentView,
        },
        {
          path: '/locker',
          component: LockerView,
        },
        {
          path: '',
          component: WelcomeView,
        },
      ],
    },
  ],
});

export default router;
