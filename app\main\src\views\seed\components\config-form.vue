<template>
  <a-form :model="modelValue" layout="vertical">
    <a-row :gutter="16">
      <template v-for="(item, key) in schema" :key="key">
        <a-col :span="12">
          <a-form-item :label="item.label || key">
            <component 
              :is="componentMap[item.type]" 
              v-model="modelValue[key]"
              v-bind="getComponentProps(item.type)"
            >
              <template v-if="item.type === 'select'">
                <a-option v-for="opt in item.options" :key="opt" :value="opt">{{ opt }}</a-option>
              </template>
            </component>
          </a-form-item>
        </a-col>
      </template>
    </a-row>
  </a-form>
</template>

<script setup lang="ts">
import { Input as AInput, InputNumber as AInputNumber, Select as ASelect, Switch as ASwitch } from '@arco-design/web-vue';

interface ConfigItem {
  label?: string;
  type: 'input' | 'number' | 'select' | 'switch' | 'color';
  options?: string[];
}

interface Props {
  modelValue: Record<string, any>;
  schema: Record<string, ConfigItem>;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue']);

// 组件映射表
const componentMap = {
  input: AInput,
  number: AInputNumber,
  select: ASelect,
  switch: ASwitch,
  color: AInput
};

// 获取组件属性
const getComponentProps = (type: string) => {
  const propsMap = {
    color: { type: 'color' }
  };
  return propsMap[type] || {};
};
</script> 