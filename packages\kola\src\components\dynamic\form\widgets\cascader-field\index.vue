<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="field.rules"
    :hide-label="field.hideLabel"
    :disabled="field.disabled"
    :style="field.style"
    ref="cascaderFormItemRef"
  >
    <a-cascader
      :style="style"
      v-model="value"
      :options="sourceData"
      :placeholder="field.placeholder"
      :multiple="field.multiple"
      :allow-clear="true"
      :format-label="field.formatLabel"
      :path-mode="field.pathMode || false"
      :max-tag-count="field.maxTagCount"
      :trigger-props="field.triggerProps"
      :field-names="field?.fieldNames"
      :value-key="field?.valueKey"
      :popup-container="field?.popupContainer"
      @clear="handleClear"
      @on-remove="handleRemove"
      @popup-visible-change="popupVisibleChange"
    />
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, ref, watchEffect } from 'vue';
  import { CascaderField } from '../../../types/form';
  import useCascaderRemove from './hooks/cascaderRemove';

  const props = defineProps<{
    field: CascaderField;
    path: string;
  }>();
  const formData = inject('formData');
  const value = defineModel<any>({
    default: '',
    set(val) {
      if (props.field.onChange) {
        setTimeout(() => {
          props.field.onChange?.(val, formData);
        }, 0);
      }
      return val;
    },
  });

  const sourceData = ref<any[]>([]);

  const style = computed<any>(() => {
    return {
      width: '100%',
      ...(props.field.style ?? {}),
    };
  });

  async function getSourceData() {
    if (typeof props.field.source.data === 'function') {
      sourceData.value = await props.field.source.data(props.field, formData?.value, props.path);
    } else {
      sourceData.value = props.field.source.data || [];
    }
  }

  const handleClear = () => {
    props.field?.onClear?.({
      value: value.value,
      formData,
    });
  };

  function handleRemove() {
    props.field?.onRemove?.({
      value: value.value,
      formData,
    });
  }

  async function popupVisibleChange(visible) {
    props.field?.onPopupVisibleChange?.(visible, formData, value.value);
  }

  const cascaderFormItemRef = ref();

  useCascaderRemove({ handleRemove, props, cascaderFormItemRef });
  watchEffect(() => {
    getSourceData();
  });
</script>
