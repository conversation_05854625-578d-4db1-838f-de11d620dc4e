class Semaphore {
  private count: number;

  private waiting: (() => void)[];

  constructor(initialCount: number) {
    this.count = initialCount;
    this.waiting = [];
  }

  async acquire(): Promise<void> {
    if (this.count > 0) {
      this.count -= 1;
    } else {
      await new Promise<void>((resolve) => {
        this.waiting.push(resolve);
      });
    }
  }

  release(): void {
    this.count += 1;
    const waiter = this.waiting.shift();
    if (waiter) {
      waiter();
      this.count -= 1;
    }
  }
}
export default Semaphore;
