<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="field.rules"
    :hide-label="!!field.hideLabel"
    :disabled="disabled"
    :hide-asterisk="!!field.hideAsterisk"
  >
    <div class="c-card">
      <div v-if="!!field.select?.cardTitle" class="c-card_title"
        >{{ field.select?.cardTitle }}
        <a-button v-if="field.select?.reverse" size="mini" type="text" class="reverse-btn" @click="handleReverseSelect"
          >反选
        </a-button>
      </div>
      <a-space class="search-box" direction="vertical" size="large">
        <a-input-search placeholder="搜索" v-model="searchKey" />
      </a-space>
      <div class="c-card_vertical c-card_radio" v-if="field.format === 'singleSelect'">
        <a-radio-group direction="vertical" v-model="value">
          <template v-for="item in sourceData" :key="item[valueKey]">
            <a-radio :value="item[valueKey]" :disabled="item.disabled">
              {{ item[labelKey] }}
            </a-radio>
          </template>
        </a-radio-group>
      </div>
      <div class="c-card_vertical" v-else>
        <a-checkbox
          style="margin-bottom: 0"
          :model-value="checkedAll"
          :indeterminate="indeterminate"
          @change="handleChangeAll"
          >全选
        </a-checkbox>
        <a-checkbox-group
          direction="vertical"
          v-model="value"
          :disabled="!!field.disabled"
          :placeholder="field.placeholder || `请选择${field.label}`"
          :allow-search="field.select?.allowSearch"
          :allow-clear="field.select?.allowClear"
          :limit="field.select?.limit"
          :max-tag-count="field.select?.maxTagCount"
          :style="field.select?.style"
          :value-key="field.select?.valueKey ?? 'value'"
          :multiple="field.format === 'multipleSelect'"
        >
          <a-checkbox
            v-for="item in filteredSourceData"
            :value="item[valueKey]"
            :key="item[valueKey]"
            :disabled="item.disabled"
          >
            {{ item[labelKey] }}
          </a-checkbox>
        </a-checkbox-group>
      </div>
    </div>
  </a-form-item>
</template>

<script setup lang="ts">
  import { inject, ref, Ref, computed, watchEffect } from 'vue';
  import { FormData, SelectField } from '../../../types/form';

  const props = defineProps<{
    field: SelectField;
    path: string;
  }>();

  const formData = inject<Ref<FormData>>('formData');
  const disabled = computed(() => {
    return props.field.disabled;
  });
  const formatCheckAll = () => {
    // 全选设置
    if (value.value.length === sourceData.value.length) {
      checkedAll.value = true;
      indeterminate.value = false;
    } else if (value.value.length === 0) {
      checkedAll.value = false;
      indeterminate.value = false;
    } else {
      checkedAll.value = false;
      indeterminate.value = true;
    }
  };
  const value = defineModel<any>({
    default: '',
    set(val) {
      if (props.field.onChange) {
        setTimeout(() => {
          props.field.onChange?.(val, formData);
        }, 0);
      }
      // 全选设置
      if (val.length === sourceData.value.length) {
        checkedAll.value = true;
        indeterminate.value = false;
      } else if (val.length === 0) {
        checkedAll.value = false;
        indeterminate.value = false;
      } else {
        checkedAll.value = false;
        indeterminate.value = true;
      }

      // 返回
      return val;
    },
  });
  const searchKey = ref('');
  const indeterminate = ref(false);
  const checkedAll = ref(false);
  const handleChangeAll = (val) => {
    indeterminate.value = false;
    if (val) {
      // value.value = sourceData.value.map((item) => item[valueKey.value]);
      value.value = sourceData.value.filter((item) => !item.disabled).map((item) => item[valueKey.value]);
      checkedAll.value = true;
    } else {
      value.value = [];
      checkedAll.value = false;
    }
  };
  const handleReverseSelect = () => {
    value.value = sourceData.value
      .filter((item) => {
        return !item.disabled && !value.value.includes(item[valueKey.value]);
      })
      .map((item) => item[valueKey.value]);
  };
  const sourceData = ref<{ value: string | number; label: string }[]>([]);
  const labelKey = computed(() => {
    return props.field.source.labelKey ?? 'label';
  });
  const valueKey = computed(() => {
    return props.field.source.valueKey ?? 'value';
  });
  const filteredSourceData = computed(() => {
    return sourceData.value.filter((item: any) => {
      return item[labelKey.value].includes(searchKey.value);
    });
  });

  async function getSourceData() {
    if (typeof props.field.source.data === 'function') {
      sourceData.value = await props.field.source.data(props.field, formData?.value as any, props.path);
    } else {
      sourceData.value = props.field.source.data || [];
    }
    // todo 暂时修复编辑回显时全选没有勾选问题
    formatCheckAll();
  }

  watchEffect(() => {
    getSourceData();
  });
</script>

<style lang="less" scoped>
  .c-card {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--color-border-2);
    width: 300px;

    &_title {
      padding: 4px 6px 8px;
      border-bottom: 1px solid var(--color-border-2);
      margin-bottom: 8px;
      font-weight: 400;
      font-size: 16px;
    }

    &_vertical {
      display: flex;
      flex-direction: column;
      max-height: 500px;
      overflow-y: auto;
    }
  }

  .search-box {
    width: 100%;
    padding-bottom: 8px;
  }

  .reverse-btn {
    float: right;
  }
</style>
