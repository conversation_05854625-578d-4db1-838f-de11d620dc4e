const fs = require('fs');
const path = require('path');
const schedule = require('node-schedule');
const buildTask = require('../model/build-task');
const FOLDER_PATH = path.join(__dirname, '../../dist'); // 目标文件夹路径
const MAX_SIZE_IN_MB = 100; // 文件夹最大大小，单位MB

const deleteFile = () => {
  console.log('开始检查文件夹大小', FOLDER_PATH);
  // 获取文件夹大小
  calculateSize(FOLDER_PATH)
    .then((sizeInBytes) => {
      const sizeInMB = sizeInBytes / (1024 * 1024); // 转换为MB
      console.log(`文件夹大小: ${sizeInMB} MB`);

      // 如果文件夹大小超过设定的最大值
      if (sizeInMB > MAX_SIZE_IN_MB) {
        console.log('文件夹大小超过限制. 开始清理...');

        // 获取并按时间顺序排序文件
        getFilesInFolder(FOLDER_PATH, (errs, files) => {
          if (errs) {
            console.error('获取文件失败:', errs);
            return;
          }

          // 计算需要删除的文件数量
          const filesToDelete = Math.ceil(files.length / 2);
          console.log(`删除 ${filesToDelete} 个旧文件...`);

          // 按时间顺序删除最旧的文件
          // eslint-disable-next-line no-plusplus
          for (let i = 0; i < filesToDelete; i++) {
            const filePath = files[i];
            console.log('删除文件夹', filePath);
            const taskId = filePath.split('/').pop();
            buildTask.deleteMany({ taskId }).then((result) => {
              console.log('mongod删除成功');
              fs.rm(filePath, { recursive: true }, (error) => {
                if (error) {
                  console.error(`删除文件失败: ${filePath}:`, error);
                } else {
                  console.log(`文件已删除: ${filePath}`);
                }
              });
            });
          }
        });
      }
    })
    .catch((err) => {
      console.error('获取文件夹大小失败:', err);
    });
};

/**
 * 计算文件或文件夹的大小（以字节为单位）
 * @param {string} targetPath 目标路径（文件或文件夹）
 * @returns {Promise<number>} 返回文件或文件夹的大小（字节）
 */
async function calculateSize(targetPath) {
  const stats = await fs.promises.stat(targetPath);

  if (stats.isFile()) {
    // 如果是文件，直接返回文件大小
    return stats.size;
  }
  if (stats.isDirectory()) {
    // 如果是文件夹，递归计算文件夹内所有文件的大小
    const files = await fs.promises.readdir(targetPath);
    const filePaths = files.map((file) => path.join(targetPath, file));

    // 使用Promise. all并行处理所有文件/文件夹
    const sizes = await Promise.all(filePaths.map((filePath) => calculateSize(filePath)));
    return sizes.reduce((total, size) => total + size, 0);
  }
  // 如果是其他类型（如符号链接），返回 0
  return 0;
}

// 获取文件夹中文件的路径，按时间排序
function getFilesInFolder(folderPath, callback) {
  fs.readdir(folderPath, (err, files) => {
    if (err) return callback(err);

    const filePaths = files.map((file) => path.join(folderPath, file));
    filePaths.sort((a, b) => {
      return fs.statSync(a).mtime - fs.statSync(b).mtime; // 按修改时间升序排序
    });

    callback(null, filePaths);
  });
}

// 定义任务
schedule.scheduleJob('0 0 10 * * 1', () => {
  console.log(new Date(), '每周一上午 10:00 执行的任务-删除旧文件');
  // 在这里写你的任务逻辑
  deleteFile();
});
