const fs = require('fs');
const { emptyDirSync, ensureDirSync, copySync } = require('fs-extra');

const path = require('path');
const parseAndWrite = require('./src/parse.js');
const compareFiles = require('./src/compare.js');
const getFilesRecursive = require('./src/utils/get-files-rec.js');
const { unzip } = require('./src/utils/unzip');
const compare = require('./p');

// parseAndWrite('./test/qcf-app.js', './dist/output.txt')
// parseAndWrite('./test/hsd-app.js', './dist/output-2.txt')

// compareFiles('./dist/output.txt', './dist/output-2.txt')

function init() {
  const cacheDir = path.resolve(__dirname, './cache');
  // ensureDirSync(cacheDir)

  const unzipDir = path.resolve(cacheDir, 'unzip');
  ensureDirSync(unzipDir);
  emptyDirSync(unzipDir);

  const blockDir = path.resolve(cacheDir, 'block');
  ensureDirSync(blockDir);
  emptyDirSync(blockDir);

  return {
    unzipDir,
    rpkDir: path.resolve(cacheDir, 'rpk'),
    blockDir,
  };
}

init();

const run = async () => {
  const { unzipDir, rpkDir, blockDir, outputDir } = init();


  const rpkFiles = getFilesRecursive(rpkDir);

  // 解压 rpk
  const unzipList = rpkFiles.map((f) => {
    const fileName = f.replace(rpkDir, '').replace('.rpk', '').slice(1);
    unzip(f, path.resolve(unzipDir, fileName));
    return path.resolve(unzipDir, fileName);
  });

  // const unzipList = fs.readdirSync(unzipDir).map(v => path.resolve(unzipDir, v))

  let index = 0;
  while (index < unzipList.length) {
    await createBlocks(getFilesRecursive(unzipList[index], '.js'), blockDir, unzipDir);
    index++;
  }
  emptyDirSync(rpkDir);
  return compare();
};

function createBlocks(jsFileList, blockDir, unzipDir) {
  const workList = jsFileList.map((f) => {
    const textFile = f.replace(unzipDir, '').replace('.js', '.txt').slice(1);
    const textPath = path.resolve(blockDir, textFile);
    ensureDirSync(path.dirname(textPath));

    return parseAndWrite(f, textPath);
  });
  return Promise.all(workList);
}

module.exports = run;
