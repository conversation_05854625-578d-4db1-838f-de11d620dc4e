<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
    :label-col-flex="field.labelWidth"
  >
    <a-textarea
      v-model="value"
      :placeholder="field.placeholder || `请输入${field.label}`"
      :max-length="field.maxLength"
      :readonly="field.readonly"
      :disabled="field.disabled"
      :auto-size="field.autoSize"
      :show-word-limit="field.showWordLimit"
      :style="field.style"
    />
  </a-form-item>
</template>

<script setup lang="ts">
  import { TextareaField } from '../../../types/form';

  const value = defineModel<string>({ default: '' });
  defineProps<{
    field: TextareaField;
    path: string;
  }>();
</script>
