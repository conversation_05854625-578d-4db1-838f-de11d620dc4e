<template>
  <div class="table-operation-column">
    <DynamicButton
      :config="item"
      :key="item.text"
      :record="props.record"
      v-for="item in operations?.slice(0, operationCount)"
    ></DynamicButton>
    <a-dropdown position="bottom" v-if="isNil(showOperationCount) && operations?.length > 2" trigger="hover">
      <a-button type="text">
        <template #icon>
          <icon-more />
        </template>
      </a-button>
      <template #content>
        <a-doption :key="index" v-for="(item, index) in operations.slice(2, operations.length)">
          <DynamicButton :config="item" :key="item.text" :record="props.record" />
        </a-doption>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
  import { defineProps } from 'vue';
  import { isNil } from 'lodash';
  import type { DButton } from '../../../types/button';
  import { RenderProps } from '../type';

  const props = defineProps<
    RenderProps & {
      operations: DButton[];
      showOperationCount?: number;
    }
  >();

  const operationCount = props.showOperationCount ?? 2;
</script>

<style scoped lang="less">
  .table-operation-column {
    margin-left: -8px;
  }

  .table-operation-column-button {
    padding: 0 8px;
    font-size: 13px;
  }
</style>
