import { ref } from 'vue';
import websocketApi from '../apis';
import { generateRequestId } from '../utils/uuid';

const defaultQuestions = ref([]);
const isRequestSent = ref(false);

const useDefaultQuestions = () => {
  const queryDefaultQuestions = () => {
    if (!isRequestSent.value) {
      isRequestSent.value = true;
      websocketApi.send({
        messageType: 'onDefaultQuestions',
        requestId: generateRequestId(),
        data: {},
      });
      websocketApi.on('onDefaultQuestions', (res) => {
        const { code, data } = res;
        if (code === 0) {
          defaultQuestions.value = data;
          isRequestSent.value = false;
        }
      });
    }
  };

  const getDefaultQuestions = () => {
    if (defaultQuestions.value.length === 0 && !isRequestSent.value) {
      queryDefaultQuestions();
    }
    return defaultQuestions;
  };

  return {
    getDefaultQuestions,
    queryDefaultQuestions,
  };
};

export default useDefaultQuestions;
