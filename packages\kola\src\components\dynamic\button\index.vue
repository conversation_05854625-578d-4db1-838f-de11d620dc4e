<template>
  <a-tooltip v-if="props.config.tooltip" :content="props.config.tooltip">
    <a-button
      :class="computedClass"
      v-bind="config.props"
      @click="handleClick"
      :disabled="buttonDisabled"
      :loading="loading"
      v-auth="auth"
    >
      <template #icon v-if="props.config.icon">
        <component :is="props.config.icon"></component>
      </template>
      {{ config.text }}
    </a-button>
  </a-tooltip>
  <a-button
    v-else
    :class="computedClass"
    v-bind="config.props"
    @click="handleClick"
    :disabled="buttonDisabled"
    :loading="loading"
    v-auth="auth"
  >
    <template #icon v-if="props.config.icon">
      <component :is="props.config.icon"></component>
    </template>
    {{ config.text }}
  </a-button>
</template>

<script setup lang="ts">
  import { Ref, computed, defineOptions, defineProps, inject, ref, shallowRef } from 'vue';
  import { isFunction, noop, keys, isBoolean } from 'lodash';
  import { DButton } from '../types/button';
  import useOperationModal from './hooks/operation-modal';
  import useRefreshTable from '../hooks/refresh-table';
  import useDispatch from '../hooks/dispatch';
  import useRegisterEvent from '../hooks/register-event';
  import { Filter } from '../types/table';

  const filter = inject<Ref<Filter>>('filter', ref({}));
  const initColumns = inject('initColumns', undefined);
  defineOptions({
    name: 'DynamicButton',
  });
  const props = defineProps<{
    config: DButton;
    record?: Record<string, any>;
    batchTableData?: any[];
  }>();
  const loading = ref(false);
  const computedClass = computed(() => {
    if (props.config.icon && !props.config.text) {
      return 'only-icon';
    }
    return 'nor-btn';
  });
  const { showOperationModal } = useOperationModal({
    modalOptions: props.config.modal,
  });
  const auth = props.config.auth ?? [];

  const modalRef = shallowRef<any>(null);
  const refreshTable = useRefreshTable();

  const dispatch = useDispatch();

  const tableData = ref([]);
  useRegisterEvent('updateTableData', ({ data }) => {
    tableData.value = data;
  });
  useRegisterEvent('setModalOkLoading', ({ isLoading }) => {
    if (modalRef.value && modalRef.value.setOkLoading) {
      modalRef.value.setOkLoading(isLoading);
    }
  });

  const buttonDisabled = computed(() => {
    const { disabled } = props.config.props ?? {};
    // 批量操作，无数据时禁用
    if (props.config.operationPosition === 'batch') {
      if (isFunction(disabled)) {
        return (
          disabled({
            filter: filter.value,
            record: props?.record,
            tableData: props.batchTableData || [],
          }) || keys(props.record).length === 0
        );
      }
      return keys(props.record).length === 0;
    }
    if (isFunction(disabled)) {
      return disabled({
        filter: filter.value,
        record: props?.record,
        getCustomParams,
        tableData: props.batchTableData || [],
      });
    }
    if (isBoolean(disabled)) {
      return disabled || props.config.operationPosition === 'batch';
    }
    return false;
  });

  async function handleClick(event) {
    if (props.config.stopPropagation) {
      event.stopPropagation();
    }
    switch (props.config.clickActionType) {
      case 'modal':
      case 'drawer': {
        const defaultFormData = await getDefaultFormData();
        if (defaultFormData === false) {
          return;
        }
        modalRef.value = showOperationModal({
          record: props.record,
          defaultFormData,
          isDrawer: props.config.clickActionType === 'drawer',
        });
        break;
      }
      case 'link': {
        window.open(props.config.linkUrl, '_blank');
        break;
      }
      // case 'batch': {
      //   changeBatchStatus(true);
      //   break;
      // }
      case 'action': {
        loading.value = true;
        try {
          await props.config.action?.({
            record: props.record,
            refreshTable,
            dispatch,
            tableData: tableData.value,
            filter: filter.value,
            initColumns,
            batchTableData: props.batchTableData,
          });
        } finally {
          loading.value = false;
        }
        break;
      }
      default: {
        break;
      }
    }
  }

  const getCustomParams = inject('getCustomParams', noop) ?? noop;

  async function getDefaultFormData() {
    const defaultFormData = props.config.modal?.getDefaultValue ?? {};
    if (!isFunction(defaultFormData)) {
      return defaultFormData;
    }
    try {
      loading.value = true;
      return await defaultFormData(props.record, getCustomParams);
    } catch (e) {
      console.log('e', e);
      return false;
    } finally {
      loading.value = false;
    }
  }
  defineExpose({
    handleClick,
  });
</script>

<style lang="less" scoped>
  .only-icon {
    padding: 0;
    text-align: center;

    :deep(.arco-btn-icon) {
      padding: 0 8px;
      margin-right: 0;
    }
  }
</style>
