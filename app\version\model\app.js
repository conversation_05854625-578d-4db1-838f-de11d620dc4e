const mongoose = require('mongoose');

const appSchema = new mongoose.Schema(
  {
    // 应用名称
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    // 应用包名
    packageName: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    // 应用图标（可选）
    icon: {
      type: String,
      default: '',
    },
    // 所属公司
    company: {
      type: String,
      required: true,
      trim: true,
    },
  },
  {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
  }
);

module.exports = mongoose.model('CompetitorsApp', appSchema);
