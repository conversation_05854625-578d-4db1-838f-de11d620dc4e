// 组件属性类型定义
export interface ComponentProp {
  value: any;
  type: 'input' | 'select' | 'switch' | 'number' | 'color';
  options?: string[];
}

// 组件样式类型定义
export interface ComponentStyle {
  value: string;
  type: 'input' | 'color';
}

// 组件类型定义
export interface Component {
  id: string;
  name: string;
  label: string;
  pinyin: string;
  props: Record<string, ComponentProp>;
  styles: Record<string, ComponentStyle>;
}

// 页面配置类型定义
export interface PageConfig {
  pageName: string;
  pageId: string;
  themeColor: string;
  backgroundColor: string;
  path: string;
  components: Component[];
}

// 页面配置集合类型定义
export interface PagesConfig {
  name: string,
  packageName: string,
  icon: string,
  privacyUrl: string,
  agreementUrl: string,
  main: PageConfig[];
  subPages: PageConfig[];
} 