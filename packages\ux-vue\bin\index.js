#!/usr/bin/env node
const { program } = require('commander');
const mainHandler = require('../index');

// 配置命令行参数
program
  .version('1.0.0')
  .description('Convert .ux files to .vue files')
  .option('-i, --input <path>', 'Input directory containing .ux files')
  .option('-o, --output <path>', 'Output directory for .vue files')
  .parse(process.argv);

const options = program.opts();

// 确保输入和输出目录存在
if (!options.input || !options.output) {
  console.error('Error: Both input and output directories are required');
  program.help();
}

mainHandler(options.input, options.output);
