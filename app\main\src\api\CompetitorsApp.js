import { http } from '@/utils/http';

const CompetitorsAppApi = {
  // 创建CompetitorsApp
  create(data) {
    return http.post('/api/CompetitorsApps', data);
  },

  // 获取CompetitorsApp列表
  list(params) {
    return http.get('/api/CompetitorsApps', { params });
  },

  // 获取单个CompetitorsApp
  getById(id) {
    return http.get(`/api/CompetitorsApps/${id}`);
  },

  // 更新CompetitorsApp
  update(id, data) {
    return http.put(`/api/CompetitorsApps/${id}`, data);
  },

  // 删除CompetitorsApp
  delete(id) {
    return http.delete(`/api/CompetitorsApps/${id}`);
  },
};

export default CompetitorsAppApi;
