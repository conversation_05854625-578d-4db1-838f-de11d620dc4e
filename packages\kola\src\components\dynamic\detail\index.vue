<template>
  <div>
    <div class="loading" v-if="loading">
      <a-spin />
    </div>
    <a-descriptions
      v-else
      v-for="item in sourceData"
      :key="item.title"
      :data="item.data"
      :size="item.size || 'medium'"
      :title="item.title"
      :label-style="item.labelStyle"
      :value-style="item.valueStyle"
      :column="item.column || 1"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, provide, ref, watchEffect } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { cloneDeep } from 'lodash';
  import { DDetailData, DDetail } from '../types/detail';

  defineOptions({ name: 'DynamicDetail' });

  const props = defineProps<DDetail>();

  const sourceData = ref<DDetailData>([]);
  const loading = ref<boolean>(false);
  async function getData() {
    loading.value = true;
    if (typeof props.data === 'function') {
      sourceData.value = await props.data(props.record);
    } else {
      sourceData.value = props.data || [];
    }
    loading.value = false;
  }

  watchEffect(() => {
    getData();
  });
</script>

<style lang="less" scoped>
  .loading {
    height: 100px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
