const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generator = require('@babel/generator').default;
const {systemReplace } = require('./system-replace');

function scriptConvert(scriptContent) {
  // 解析JavaScript代码为AST

  // 解析JavaScript代码为AST
  const ast = parseJavaScript(scriptContent);

  // 转换AST
  convertUXToVue(ast);

  // 生成转换后的代码
  const { code } = generator(ast, {
    // retainLines: true,
    compact: false,
  });

  return systemReplace(code);
}

// 使用Babel解析JavaScript
function parseJavaScript(scriptContent) {
  try {
    return parser.parse(scriptContent, {
      sourceType: 'module',
    });
  } catch (error) {
    throw new Error(`Failed to parse JavaScript: ${error.message}`);
  }
}

function convertUXToVue(ast) {
  // 查找export default对象
  let exportDefaultNode = null;
  traverse(ast, {
    ExportDefaultDeclaration(path) {
      if (path.node.declaration.type === 'ObjectExpression') {
        exportDefaultNode = path.node.declaration;
        path.stop();
      }
    },
  });

  if (!exportDefaultNode) {
    throw new Error('Could not find export default object');
  }

  // 存储所有方法
  const methods = [];
  // 存储private和public属性
  const dataProperties = [];
  // 查找data属性
  let dataProperty = null;

  // 处理每个属性
  exportDefaultNode.properties = exportDefaultNode.properties
    .map((prop) => {
      const key = prop.key.name;
      // 处理onInit方法
      if (key === 'onInit') {
        return {
          ...prop,
          key: {
            ...prop.key,
            name: 'created',
          },
        };
      }
      if (key === 'props') {
        return prop;
      }
      // 函数模式的 data 处理
      if (isFunction(prop) && key === 'data') {
        dataProperty = prop;
        return null;
      }
      // 收集所有方法
      if (isFunction(prop)) {
        methods.push(prop);
        return null; // 从原位置移除
      }

      // 处理private和public属性
      if (prop.type === 'ObjectProperty') {
        // 处理data属性
        if (key === 'data' || key === 'private' || key === 'public') {
          if ( prop.value.type === 'ArrowFunctionExpression'){
            prop.value.body.properties.forEach((privateProp) => {
              dataProperties.push(privateProp);
            });
            return false; // 从原位置移除private对象
          }
          prop.value.properties.forEach((privateProp) => {
            dataProperties.push(privateProp);
          });
          return false; // 从原位置移除private对象
        }
      }

      return prop;
    })
    .filter(Boolean); // 过滤掉null值

  // 如果没有data属性，创建一个
  if (!dataProperty) {
    dataProperty = {
      type: 'ObjectProperty',
      key: {
        type: 'Identifier',
        name: 'data',
      },
      value: {
        type: 'FunctionExpression',
        params: [],
        body: {
          type: 'BlockStatement',
          body: [
            {
              type: 'ReturnStatement',
              argument: {
                type: 'ObjectExpression',
                properties: [],
              },
            },
          ],
        },
      },
      computed: false,
    };

    // 将data属性添加到组件对象中
    exportDefaultNode.properties.unshift(dataProperty);
  }

  // 获取data函数的返回对象
  const dataReturnObject = dataProperty.value.body.body[0].argument;

  // 将private和public属性添加到data返回对象中
  [...dataProperties].forEach((prop) => {
    dataReturnObject.properties.push(prop);
  });

  // 如果有方法，添加methods对象
  if (methods.length > 0) {
    exportDefaultNode.properties.push({
      type: 'ObjectProperty',
      key: {
        type: 'Identifier',
        name: 'methods',
      },
      value: {
        type: 'ObjectExpression',
        properties: methods,
      },
      computed: false,
    });
  }
}

// 判断是否为函数
function isFunction(node) {
  if (!node) return false;
  return (
    node.type === 'FunctionExpression' ||
    node.type === 'ArrowFunctionExpression' ||
    node.type === 'ObjectMethod' ||
    node.type === 'FunctionDeclaration'
  );
}

function scriptConvertV2(scriptContent) {
  return `<script>
  ${scriptConvert(scriptContent)}
</script>`;
}

module.exports = {
  scriptConvert,
  scriptConvertV2,
};
