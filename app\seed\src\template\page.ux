<!-- imports -->
<template>
  <div class="mainWrap">
    <!-- tags -->
    <!-- pageContent -->
  </div>
</template>
<script>
export default {
  props:{
    activeTab: {
      type: [Number, String],
      default: 0
    }
  },
  data: {
    pageInfo: '',
  },
  onInit() {
  },
}
</script>
<style lang="less" scoped>
@import '../../style/variables.less';
  .mainWrap {
    height: 100%;
    width: 100%;
    flex-direction: column;
  }
</style>

