import { getPackResult } from '@/api/auto-pack';
import ToolTip from '../toolTip.vue';
import toolTip from '../components/toolTip.vue'
import Download from '../download.vue';
import codeInspector from '../components/codeInspector.vue';


export default {
  load: {
    action: async (filter: any, pagination: any) => {
      const res = await getPackResult({ ...filter, ...pagination });
      console.log('res', res);
      return res;
    },
  },
  columns: [
    {
      title: '产品名称',
      dataIndex: 'appName',
    },
    {
      title: '厂商',
      dataIndex: 'brand',
      customRender: {
        type: 'CodeToName',
        props: {
          map: [],
        },
      },
    },
    {
      title: '版本号',
      dataIndex: 'version',
    },
    {
      title: 'sdk版本',
      dataIndex: 'sdkVersion',
      customRender: {
        type: toolTip,
        props: {
          content: 'sdkVersion',
        },
      }
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      customRender: {
        type: ToolTip,
        props: {
          content: '任务状态',
        },
      },
    },
    {
      title: '任务创建时间',
      dataIndex: 'timeStamp',
    },
    {
      title: '创建人',
      dataIndex: 'username',
    },
    {
      title: '测试包',
      dataIndex: 'devPkg',
      customRender: {
        type: Download,
        props: {
          content: '测试包',
        },
      },
    },
    {
      title: '线上log包',
      dataIndex: 'releaseLogPkg',
      customRender: {
        type: Download,
        props: {
          content: '线上log包',
        },
      },
    },
    {
      title: '线上包',
      dataIndex: 'releasePkg',
      customRender: {
        type: Download,
        props: {
          content: '线上包',
        },
      },
      visibleOn: (record: any) => record.releasePkg,
    },
    {
      title:'操作',
      dataIndex:'operations',
      customRender:{
        type:'operations',
        props:{
          operations:[{
            text: '检查',
            props: {
              type:'text',
            },
            clickActionType: 'modal',
            modal:{
              props:{
                'title':'代码检查',
                'esc-to-close':false,
                'fullscreen':false,
                'width':1000
              },
              contentType:'custom',
              custom:codeInspector
            }
          }]
        }
      }
 
       
     }
  ],
};
