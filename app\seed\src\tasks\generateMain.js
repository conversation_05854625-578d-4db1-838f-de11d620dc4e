const fs = require('fs');
const path = require('path');
const { OUT_PUT_DIR } = require('../constant.js');


function generateMain(meta) {
    const mainTempDir = path.join(__dirname, '../template/main.ux');
    const mainTemplate = fs.readFileSync(mainTempDir, 'utf-8');
    const modifications = getModification(meta)
    const mainContent = modifyMain(mainTemplate, modifications); // 下载文件并返回路径
    const mainPath = path.join(OUT_PUT_DIR,'/pages/Main');
    if (!fs.existsSync(mainPath)) { // 如果目录不存在，则创建目录
        fs.mkdirSync(mainPath, { recursive: true }); // 递归创建目录
    }
    fs.writeFileSync(path.join(mainPath,'index.ux'), mainContent); // 写入文件
}

function getModification(meta){
    const { agreementUrl, privacyUrl, tabs } = meta; // 从meta中获取log
    const modifications = []
    let imports = ``;
    let tabsContent = ``;
    modifications.push({old: '__agreementUrl__', new: agreementUrl})
    modifications.push({old: '__privacyUrl__', new: privacyUrl})
    tabs.forEach(tab => {
        const { pageName } = tab; // 从meta中获取log
        imports += `<import name="${pageName}" src="./${pageName}.ux"></import>\n`; // 生成import语句
        tabsContent += `<${pageName} active-tab="{{activeTab}}"></${pageName}>\n`; // 生成tab内容
    })
    modifications.push({old: '<!-- imports -->', new: imports})
    modifications.push({old: '<!-- tabs -->', new: tabsContent})
    modifications.push({old: `'__tabList__'`, new: JSON.stringify(tabs)})
    return modifications
}

function modifyMain(mainTemplate, modifications) {
    modifications.forEach(modification => {
        const { old, new: newValue } = modification; // 解构赋值，避免与JavaScript关键字冲突
        mainTemplate = mainTemplate.replace(old, newValue); // 替换模板中的占位符
    })
    return mainTemplate
}


module.exports = { generateMain }