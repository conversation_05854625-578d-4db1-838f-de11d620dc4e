// 生成 DForm 筛选表单字段
function generateFilterFields(fields) {
  return fields
    .map((field) => {
      let type = 'text'; // 默认类型
      if (field.type === 'Date') type = 'datePicker';
      if (field.type === 'Number') type = 'number';

      return `{
        name: '${field.name}',
        label: '${field.name}',
        type: '${type}',
        placeholder: '请输入${field.name}',
      }`;
    })
    .join(',\n          ');
}

/**
 * 根据字段生成默认值对象
 * @param {Array} fields - 字段列表
 * @returns {Object} - 默认值对象
 */
function generateDefaultValues(fields) {
  const defaultValues = {};

  fields.forEach((field) => {
    switch (field.type) {
      case 'String':
        defaultValues[field.name] = field.default || '';
        break;
      case 'Number':
        defaultValues[field.name] = field.default || 0;
        break;
      case 'Date':
        defaultValues[field.name] = 'new Date()';
        break;
      case 'Boolean':
        defaultValues[field.name] = field.default || false;
        break;
      default:
        defaultValues[field.name] = field.default || null;
        break;
    }
  });

  // 将对象转换为字符串
  const entries = Object.entries(defaultValues)
    .map(([key, value]) => {
      if (typeof value === 'string' && !value.startsWith('new Date')) {
        return `${key}: '${value}'`; // 字符串值加引号
      }
      return `${key}: ${value}`; // 其他类型直接使用
    })
    .join(',\n    ');

  return `{
    ${entries}
  }`;
}

// 生成 DTable 表格列
function generateTableColumns(modelName, fields) {
  const columns = fields.map(
    (field) => `{
      title: '${field.name}',
      dataIndex: '${field.name}',
      width: 150,
      ellipsis: true,
    }`
  );

  columns.push(generateTableOperation(modelName, fields));

  return columns.join(',\n          ');
}

function generateOperations(modelName, fields) {
  const apiName = `${modelName}Api`;
  return `[
    {
      text: '新增',
      props: {
        type: 'primary',
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '新增',
          width: 800,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              ${generateFilterFields(fields)}
            ],
          },
        },
        getDefaultValue: () => {
          return ${generateDefaultValues(fields)}
        },
        close: (data) => {
          data.refreshTable();
        },
        action: async (data) => {
          await ${apiName}.create(data.formData);
          Message.success('操作成功');
          data.refreshTable();
        },
      },
    },
    {
      text: '批量操作',
      props: {
        type: 'primary',
      },
      clickActionType: 'batch',
    },
  ]`;
}

function generateTableOperation(modelName, fields) {
  const apiName = `${modelName}Api`;
  return `{
    title: '操作',
    dataIndex: 'operations',
    customRender: {
      type: 'operations',
      props: {
        operations: [
          {
            text: '编辑',
            props: {
              type: 'text',
            },
            clickActionType: 'modal',
            modal: {
              props: {
                'title': '编辑',
                'esc-to-close': false,
              },
              contentType: 'form',
              form: {
                formSchema: {
                  fields: [
                    ${generateFilterFields(fields)}
                  ],
                },
              },
              getDefaultValue(rowData) {
                const { _id: id } = rowData;
                return ${apiName}.getById(id).then((res) => res.data);
              },
              action: async ({ formData, record, refreshTable }) => {
                const { _id: id } = record;
                await ${apiName}.update(id, formData);
                refreshTable();
              },
            },
          },
          {
            text: '删除',
            props: {
              type: 'text',
            },
            clickActionType: 'modal',
            modal: {
              props: {
                title: '确认删除',
              },
              contentType: 'text',
              text: '确认删除？',
              action: async ({ record, refreshTable }) => {
                const { _id: id } = record;
                await ${apiName}.delete(id);
                refreshTable();
              },
            },
          },
        ],
      },
    },
  }`;
}

// 生成使用 setup 语法的 DynamicPage 组件
function generateDynamicPage(modelName, fields) {
  const filterFields = generateFilterFields(fields);
  const tableColumns = generateTableColumns(modelName, fields);
  const operationsConfig = generateOperations(modelName, fields);
  const apiName = `${modelName}Api`;

  const dynamicPageCode = `<template>
  <DynamicPage
    :filter="filterConfig"
    :table="tableConfig"
    :operation="operations"
    :batch-operation="batchOperations"
    auto-load
    row-key="_id"
  />
</template>

<script setup>
import { ref } from 'vue';
import { Message } from '@arco-design/web-vue';
import ${apiName} from '@/api/${modelName}';


// Filter 配置
const filterConfig = ref({
  formSchema: {
    fields: [
      ${filterFields}
    ],
  },
});

// 数据加载方法
async function loadData(filter, pagination) {
  return ${apiName}.list({
    ...filter,
    ...pagination
  });
}
// Table 配置
const tableConfig = ref({
  columns: [
    ${tableColumns}
  ],
  isPageable: true,
  pagination: {
    current: 1,
    pageSize: 10,
    showPageSize: true,
    showTotal: true,
    total: 0,
    showJumper: true,
    pageSizeOptions: [10, 20, 50],
  },
  load: {
    action: loadData,
  },
});

// 操作按钮配置
const operations = ref(${operationsConfig}); // 单个操作按钮
const batchOperations = ref([]); // 批量操作按钮


</script>
`;

  return dynamicPageCode;
}

module.exports = generateDynamicPage;
