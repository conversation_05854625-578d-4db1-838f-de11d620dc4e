const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function downloadFileWithAxios(url, destPath) {
  try {
    // 获取文件名
    const filename = path.basename(url);
    const filePath = path.join(destPath, filename);
    
    // 确保目录存在
    if (!fs.existsSync(destPath)) {
      fs.mkdirSync(destPath, { recursive: true });
    }
    
    // 发送请求获取图片数据
    const response = await axios({
      method: 'get',
      url: url,
      responseType: 'stream'
    });
    
    // 创建写入流
    const writer = fs.createWriteStream(filePath);
    
    // 管道传输数据
    response.data.pipe(writer);
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => resolve(filePath));
      writer.on('error', reject);
    });
  } catch (error) {
    throw error;
  }
}

module.exports = {
  downloadFileWithAxios,
};