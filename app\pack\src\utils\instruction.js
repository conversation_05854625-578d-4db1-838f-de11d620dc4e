const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

// 克隆仓库
function cloneRepo(repoUrl, targetDir) {
  if (fs.existsSync(targetDir)) {
    console.log(`${targetDir} 已存在`);
  } else {
    execSync(`git clone ${repoUrl} ${targetDir}`, { stdio: 'inherit' });
  }
}

// 更新代码
function updateCode(targetDir) {
  execSync(`git restore .&& git pull`, { cwd: targetDir, stdio: 'inherit' });
}

// 切换分支
function checkoutBranch(targetDir, branchName) {
  execSync(`git checkout ${branchName}`, { cwd: targetDir, stdio: 'inherit' });
}

// 克隆仓库指定分支
function cloneSpecifiedBranch(repoUrl, targetDir, branchName) {
  if (fs.existsSync(targetDir)) {
    console.log(`${targetDir} 已存在`);
    return;
  }

  try {
    console.log(`正在克隆 ${branchName} 分支到 ${targetDir}...`);
    execSync(`git clone -b ${branchName} --single-branch ${repoUrl} ${targetDir}`, { stdio: 'inherit' });
    console.log('克隆成功！');
  } catch (error) {
    console.error(`克隆失败: ${error.message}`);
    // 克隆失败后删除部分创建的文件
    if (fs.existsSync(targetDir)) {
      console.log('删除部分创建的目录...');
      // fs.rmSync(targetDir, { recursive: true, force: true })
      // console.log(`${targetDir} 已被清理`)
    }
  }
}

// 安装依赖
function installDependencies(targetDir) {
  execSync('npm install', { cwd: targetDir, stdio: 'inherit' });
}

// 运行代码提取脚本 sdk
// function runCodeExtractSdk(targetDir, brand) {
//   // execSync("npx ad run", { cwd: targetDir, stdio: "inherit" })
//   execSync(`npm run ${brand}`, { cwd: targetDir, stdio: "inherit" })
// }

// 运行代码提取脚本 app
function runCodeExtractApp(targetDir, brand, appName, sdkVersion) {
  const command = [
    'npx',
    'ad',
    'run',
    `--sdk ${sdkVersion}`,
    `--app ${appName}`,
    `--brand ${brand}`,
    '--exit',
  ]
    .filter(Boolean)
    .join(' ');

  const output = execSync(command, { cwd: targetDir, stdio: 'pipe' }).toString();
  fs.writeFileSync(path.join(targetDir, 'ad.log'), output);
  // console.log('resultsssssssss,', output)
  const ManifestRegex = /(?<=manifest---start\s*)\{[\s\S]*?\}(?=\s*manifest---end)/g
  const appConfigRegex = /(?<=app-define-plugin---start\s*)\{[\s\S]*?\}(?=\s*app-define-plugin---end)/g
  const appManifest = output.match(ManifestRegex)?.[0].trim()
  const appDefinePlugin = output.match(appConfigRegex)?.[0].trim()
  return {appManifest,appDefinePlugin}
}

function autoUpgrade(targetDir, brand, appName) {
  const command = [
    'npx',
    'ad',
    'run',
    `--app ${appName}`,
    `--brand ${brand}`,
    '--upgrade',
    '--sdk latest',
    '--exit'
  ]
    .filter(Boolean)
    .join(' ');
  const output = execSync(command, { cwd: targetDir, stdio: 'inherit' });
  console.log('自动升级版本号:', output);
}



// 更新ad-interface
function updateAdInterface(targetDir) {
  const interfaceTargetDir = path.join(targetDir, './node_modules/ad-interface');
  execSync('git pull', { cwd: interfaceTargetDir, stdio: 'inherit' });
}

function checkouNewBranch(targetDir, branchName) {
  try{
    execSync(`git branch -D ${branchName}`, { cwd: targetDir, stdio: 'inherit' });
    execSync(`git push origin --delete ${branchName}`, { cwd: targetDir, stdio: 'inherit' });
  }catch(e){
    console.log('分支不存在',e)
  }
  execSync(`git pull`, { cwd: targetDir, stdio: 'inherit' });
  execSync(`git checkout -b ${branchName}`, { cwd: targetDir, stdio: 'inherit' });
  execSync(`git push --set-upstream origin ${branchName}`, { cwd: targetDir, stdio: 'inherit' });
}

function gitPullOrigin(targetDir, branchName) {
  execSync(`git pull origin ${branchName}`, { cwd: targetDir, stdio: 'inherit' });
}

/*************  ✨ Codeium Command ⭐  *************/
/**
 * Switch the target directory to master branch.
 * @param {string} targetDir The target directory.
 */
/******  dc31c61b-a112-4495-a2f8-237a9fa7df15  *******/
function gitCheckout(targetDir, branchName) {
  execSync(`git checkout ${branchName}`, { cwd: targetDir, stdio: 'inherit' });
}
// 运行打包命令
function runDevBuild(targetDir) {
  const result = execSync('npm run build', {
    cwd: targetDir,
    stdio: 'pipe',
  }).toString();
  console.log('打包输出', result);
}

function replaceFun(targetfile, oldStr, newStr) {
  const content = fs.readFileSync(targetfile, 'utf-8');
  const newContent = content.replace(oldStr, newStr);
  fs.writeFileSync(targetfile, newContent, 'utf-8');
}


function replaceBeforeBuild(targetDir, type) {
  console.log('replaceBeforeBuild-type', type)
  if (type === 'dev') {
    replaceFun(path.join(targetDir, './quickapp.config.js'), '//...appPluginConfig.dev', '...appPluginConfig.dev')
    replaceFun(path.join(targetDir, './quickapp.config.js'), '//...pluginConfig.dev', '...pluginConfig.dev')
  } else {
    replaceFun(path.join(targetDir, './quickapp.config.js'), '...appPluginConfig.dev', '//...appPluginConfig.dev')
    replaceFun(path.join(targetDir, './quickapp.config.js'), '...pluginConfig.dev', '//...pluginConfig.dev')
  }
}

function runReleaseLogBuild(targetDir) {
  execSync('npm run release-log', { cwd: targetDir, stdio: 'pipe' });
  execSync('npm run release', { cwd: targetDir, stdio: 'pipe' });
}

function runReleaseBuild(targetDir) {
  execSync('node release.js', { cwd: targetDir, stdio: 'pipe' });
  execSync('npm run release', { cwd: targetDir, stdio: 'pipe' });
}

function cdPath(dir) {
  execSync(`cd ${dir}`, { stdio: 'inherit' });
}

function clearDist(targetDir) {
  // 清除dist目录
  const distDir = path.join(targetDir, './dist');
  if (fs.existsSync(distDir)) {
    fs.rmSync(distDir, { recursive: true, force: true });
  }
}

function clearCaches(targetDir) {
  // 清除nodeModules里.cache缓存
  const cacheDir = path.join(targetDir, './node_modules/.cache');
  if (fs.existsSync(cacheDir)) {
    fs.rmSync(cacheDir, { recursive: true, force: true });
  }
}

function installHap() {
  execSync('npm install -D hap-toolkit', { stdio: 'inherit' });
}

function gitCommit(targetDir, message) {
  // execSync(`git checkout -- ./package.json`, { cwd: targetDir, stdio: 'inherit' });
  execSync(`git add . && git commit -m ${message} && git push`, { cwd: targetDir, stdio: 'inherit' });
}

module.exports = {
  cloneRepo,
  checkoutBranch,
  cloneSpecifiedBranch,
  installDependencies,
  runCodeExtractApp,
  updateAdInterface,
  runDevBuild,
  runReleaseLogBuild,
  runReleaseBuild,
  cdPath,
  updateCode,
  clearDist,
  clearCaches,
  replaceBeforeBuild,
  installHap,
  autoUpgrade,
  gitCommit,
  checkouNewBranch,
  gitCheckout,
  gitPullOrigin
};
