<template>
  <div class="powersaving">
    <div class="content">
      <div class="device-info">
        <text class="device-title">手机信息</text>
        <div class="device-area">
          <image src="{{path}}quickapp/app/ydpower/images/phone.png" class="phone-icon"></image>
          <div class="phone-info">
            <text class="phone-brand">{{ deviceInfo.brand }}</text>
            <text class="phone-system">{{ deviceInfo.osType }} {{ deviceInfo.osVersionName }}</text>
          </div>
        </div>
        <div class="screen-info">
          <div class="info-item">
            <text class="info-num">{{ deviceInfo.screenWidth }}</text>
            <text class="info-text">屏幕宽度</text>
          </div>
          <div class="info-item">
            <text class="info-num">{{ deviceInfo.screenHeight }}</text>
            <text class="info-text">屏幕高度</text>
          </div>
          <div class="info-item">
            <text class="info-num">{{ languageText }}</text>
            <text class="info-text">系统语言</text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import device from '@system.device'

  const __BUS_PUBLIC_OSS_BASE_URL__ = "https://static.chengyouyun.com/"
  const codeArr = [
    "aa",
    "fr",
    "li",
    "se",
    "ab",
    "fy",
    "ln",
    "sg",
    "ae",
    "ga",
    "lo",
    "sh",
    "af",
    "gd",
    "lt",
    "si",
    "ak",
    "gl",
    "lu",
    "sk",
    "am",
    "gn",
    "lv",
    "sl",
    "an",
    "gu",
    "mg",
    "sm",
    "ar",
    "gv",
    "mh",
    "sn",
    "as",
    "ha",
    "mi",
    "so",
    "av",
    "he",
    "mk",
    "sq",
    "ay",
    "hi",
    "ml",
    "sr",
    "az",
    "ho",
    "mn",
    "ss",
    "ba",
    "hr",
    "mo",
    "st",
    "be",
    "ht",
    "mr",
    "su",
    "bg",
    "hu",
    "ms",
    "sv",
    "bh",
    "hy",
    "mt",
    "sw",
    "bi",
    "hz",
    "my",
    "ta",
    "bm",
    "ia",
    "na",
    "te",
    "bn",
    "id",
    "nb",
    "tg",
    "bo",
    "ie",
    "nd",
    "th",
    "br",
    "ig",
    "ne",
    "ti",
    "bs",
    "ii",
    "ng",
    "tk",
    "ca",
    "ik",
    "nl",
    "tl",
    "ce",
    "io",
    "nn",
    "tn",
    "ch",
    "is",
    "no",
    "to",
    "co",
    "it",
    "nr",
    "tr",
    "cr",
    "iu",
    "nv",
    "ts",
    "cs",
    "ja",
    "ny",
    "tt",
    "cu",
    "jv",
    "oc",
    "tw",
    "cv",
    "ka",
    "oj",
    "ty",
    "cy",
    "kg",
    "om",
    "ug",
    "da",
    "ki",
    "or",
    "uk",
    "de",
    "kj",
    "os",
    "ur",
    "dv",
    "kk",
    "pa",
    "uz",
    "dz",
    "kl",
    "pi",
    "ve",
    "ee",
    "km",
    "pl",
    "vi",
    "el",
    "kn",
    "ps",
    "vo",
    "en",
    "ko",
    "pt",
    "wa",
    "eo",
    "kr",
    "qu",
    "wo",
    "es",
    "ks",
    "rm",
    "xh",
    "et",
    "ku",
    "rn",
    "yi",
    "eu",
    "kv",
    "ro",
    "yo",
    "fa",
    "kw",
    "ru",
    "za",
    "ff",
    "ky",
    "rw",
    "zh",
    "fi",
    "la",
    "sa",
    "zu",
    "fj",
    "lb",
    "sc",
    "fo",
    "lg",
    "sd"
  ]
  const strArr = [
    "阿法尔语",
    "法语",
    "林堡语",
    "北萨米语",
    "阿布哈兹语",
    "弗里西亚语",
    "林加拉语",
    "桑戈语",
    "阿维斯陀语",
    "爱尔兰语",
    "老挝语",
    "塞尔维亚 - 克罗地亚语",
    "南非语",
    "苏格兰盖尔语",
    "立陶宛语",
    "僧加罗语",
    "阿坎语",
    "加利西亚语",
    "卢巴语",
    "斯洛伐克语",
    "阿姆哈拉语",
    "瓜拉尼语",
    "拉脱维亚语",
    "斯洛文尼亚语",
    "阿拉贡语",
    "古吉拉特语",
    "马达加斯加语",
    "萨摩亚语",
    "阿拉伯语",
    "马恩岛语",
    "马绍尔语",
    "绍纳语",
    "阿萨姆语",
    "豪萨语",
    "毛利语",
    "索马里语",
    "阿瓦尔语",
    "希伯来语",
    "马其顿语",
    "阿尔巴尼亚语",
    "艾马拉语",
    "印地语",
    "马拉亚拉姆语",
    "塞尔维亚语",
    "阿塞拜疆语",
    "希里莫图语",
    "蒙古语",
    "斯瓦特语",
    "巴什基尔语",
    "克罗地亚语",
    "摩尔达维亚语",
    "南索托语",
    "白俄罗斯语",
    "海地克里奥尔语",
    "马拉提语",
    "巽他语",
    "保加利亚语",
    "匈牙利语",
    "马来语",
    "瑞典语",
    "比哈尔语",
    "亚美尼亚语",
    "马耳他语",
    "斯瓦希里语",
    "比斯拉马语",
    "赫雷罗语",
    "缅甸语",
    "泰米尔语",
    "班巴拉语",
    "国际语A",
    "瑙鲁语",
    "泰卢固语",
    "孟加拉语",
    "印尼语",
    "书面挪威语",
    "塔吉克斯坦语",
    "藏语",
    "国际语E",
    "北恩德贝勒语",
    "泰语",
    "布列塔尼语",
    "伊博语",
    "尼泊尔语",
    "提格里尼亚语",
    "波斯尼亚语",
    "四川彝语",
    "恩敦加语",
    "土库曼语",
    "加泰隆语",
    "依努庇克语",
    "荷兰语",
    "他加禄语",
    "车臣语",
    "伊多语",
    "新挪威语",
    "塞茨瓦纳语",
    "查莫罗语",
    "冰岛语",
    "挪威语",
    "汤加语",
    "科西嘉语",
    "意大利语",
    "南恩德贝勒语",
    "土耳其语",
    "克里语",
    "因纽特语",
    "纳瓦霍语",
    "宗加语",
    "捷克语",
    "日语",
    "尼扬贾语",
    "塔塔尔语",
    "古教会斯拉夫语",
    "爪哇语",
    "奥克语",
    "特威语",
    "楚瓦什语",
    "格鲁吉亚语",
    "奥吉布瓦语",
    "塔希提语",
    "威尔士语",
    "刚果语",
    "奥洛莫语",
    "维吾尔语",
    "丹麦语",
    "基库尤语",
    "奥利亚语",
    "乌克兰语",
    "德语",
    "宽亚玛语",
    "奥塞梯语",
    "乌尔都语",
    "迪维希语",
    "哈萨克语",
    "旁遮普语",
    "乌兹别克语",
    "不丹语",
    "格陵兰语",
    "巴利语",
    "文达语",
    "埃维语",
    "高棉语",
    "波兰语",
    "越南语",
    "现代希腊语",
    "卡纳达语",
    "普什图语",
    "沃拉普克语",
    "英语",
    "韩语",
    "葡萄牙语",
    "沃伦语",
    "世界语",
    "卡努里语",
    "凯楚亚语",
    "沃洛夫语",
    "西班牙语",
    "克什米尔语",
    "罗曼什语",
    "科萨语",
    "爱沙尼亚语",
    "库尔德语",
    "基隆迪语",
    "依地语",
    "巴斯克语",
    "科米语",
    "罗马尼亚语",
    "约鲁巴语",
    "波斯语",
    "康沃尔语",
    "俄语",
    "壮语",
    "富拉语",
    "吉尔吉斯语",
    "卢旺达语",
    "中文",
    "芬兰语",
    "拉丁语",
    "梵语",
    "祖鲁语",
    "斐济语",
    "卢森堡语",
    "萨丁尼亚语",
    "法罗语",
    "卢干达语",
    "信德语"
  ]
  export default {
    data: {
      deviceInfo: "",
      languageText: '',
      isXiaomi: true,
      path: __BUS_PUBLIC_OSS_BASE_URL__,
    },
    getLanguageFun(codeStr) {
      let nowIndex = codeArr.findIndex(item => item == codeStr)
      let nowLanguage = strArr[nowIndex]
      return nowLanguage ? nowLanguage : codeStr
    },

    onInit() {
      // const seed = await SEED
      // console.log('SEED==============',SEED)
      device.getInfo().then(res => {
        console.log("当前设备信息：", res)
        res && (this.deviceInfo = res.data)
        this.languageText = this.getLanguageFun(res.data.language)
        let tempBrand = res.brand.toLowerCase()
        if (tempBrand == 'redmi' || tempBrand == 'xiaomi' || tempBrand == '小米' || tempBrand == 'Redmi' || tempBrand == '小米红米') {
          this.isXiaomi = false
        }
      })
    },
  }
</script>
<style lang="less">
  .powersaving {
    flex-direction: column;
    background-color: #f7f7f7;

    .top-bg {
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      width: 750px;
      height: 228px;
      background: linear-gradient(180deg, #e4ffe6, rgba(245, 245, 245, 0) 100%);
    }

    .content {
      flex-direction: column;
      padding: 30px;

      .device-info {
        margin-top: 20px;
        flex-direction: column;
        background-color: #ffffff;
        border-radius: 20px;
        /* box-shadow: 0px 8px 16px 0px rgba(204, 204, 204, 0.2); */
        padding: 35px 25px;

        .device-title {
          font-size: 32px;
          font-weight: 600;
          color: #000000;
        }

        .device-area {
          margin: 60px auto;
          margin-bottom: 70px;

          /* justify-content: center; */
          .phone-icon {
            width: 49px;
          }

          .phone-info {
            flex-direction: column;
            margin-left: 27px;

            .phone-brand {
              font-size: 40px;
              font-weight: 600;
              color: #000000;
              line-height: 40px;
            }

            .phone-system {
              font-size: 28px;
              line-height: 28px;
              font-weight: 400;
              color: #bebebe;
              margin-top: 20px;
            }
          }
        }

        .screen-info {
          justify-content: space-between;
          width: 80%;
          margin: 0 auto;

          .info-item {
            flex-direction: column;

            .info-num {
              font-size: 28px;
              font-weight: 600;
              color: #333333;
              text-align: center;
              line-height: 28px;
            }

            .info-text {
              font-size: 24px;
              font-weight: 400;
              color: #c0c0c0;
              margin-top: 20px;
              text-align: center;
              line-height: 24px;
            }
          }
        }
      }

    }
  }
</style>