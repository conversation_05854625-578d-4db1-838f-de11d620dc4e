<template>
  <DynamicForm
    v-model="localValue"
    :form-schema="formSchema"
    :layout="layout"
    :arco-form-props="arcoFormProps"
  />
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue';

interface ConfigItem {
  label?: string;
  type: 'String' | 'number' | 'select' | 'switch' | 'color' | 'http';
  options?: string[];
  value?: any;
  required?: boolean;
  placeholder?: string;
}

interface Props {
  modelValue: Record<string, any>;
  schema: Record<string, ConfigItem>;
  layout?: 'vertical' | 'inline' | 'horizontal';
}

const props = withDefaults(defineProps<Props>(), {
  layout: 'vertical'
});

const emit = defineEmits(['update:modelValue']);

// 本地值
const localValue = ref<Record<string, any>>({});
// 标志位，防止递归更新
const isUpdating = ref(false);

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  if (!isUpdating.value) {
    localValue.value = { ...newVal };
  }
}, { immediate: true, deep: true });

// 监听 localValue 变化，触发事件
watch(localValue, async (newVal) => {
  if (!isUpdating.value) {
    isUpdating.value = true;
    console.log('formData changed:', newVal);
    emit('update:modelValue', newVal);
    // 使用 nextTick 确保在下一个事件循环中重置标志位
    await nextTick();
    isUpdating.value = false;
  }
}, { deep: true });

// 转换 schema 为 DynamicForm 需要的格式
const formSchema = computed(() => {
  const fields: any[] = [];
  
  Object.entries(props.schema).forEach(([key, item]) => {
    const field = {
      name: key,
      label: item.label || key,
      type: item.type,
      required: item.required,
      placeholder: item.placeholder,
    };

    fields.push(field);
  });

  return { fields };
});

// Arco Form 属性配置
const arcoFormProps = {
  labelAlign: 'left' as const,
  style: {
    width: '100%'
  }
};
</script> 