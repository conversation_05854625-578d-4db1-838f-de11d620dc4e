const jwt = require('jsonwebtoken');
const { JWT_SECRET_KEY } = require('../constant');

function authenticateToken(req, res, next) {
  const token = req.headers.authorization;
  const whiteList = ['login', 'download', 'file-content', 'decode', 'ai'];

  if (whiteList.some((v) => req.path.includes(v))) {
    next();
    return;
  }

  if (!token) {
    res.status(401).json({ message: '未提供 Token' });
    return;
  }

  jwt.verify(token.replace('Bearer ', ''), JWT_SECRET_KEY, (err, decoded) => {
    if (err) {
      res.status(403).json({ message: '无效的 Token' });
      return;
    }
    req.user = decoded;
    next();
  });
}

module.exports = {
  authenticateToken,
};
