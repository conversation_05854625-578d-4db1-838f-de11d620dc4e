import ObsClient from 'esdk-obs-browserjs/src/obs';

import { v4 as uuIdV4 } from 'uuid';

let tokenCatch: any = null;
const options: any = { credentials: 'include' };

async function loadParams() {
  if (tokenCatch) {
    return Promise.resolve(tokenCatch);
  }
  return fetch(`/apis/adplan/obs/conf/v1`, options)
    .then((response) => {
      return response.json();
    })
    .then((res) => {
      const { data } = res;
      const params = {
        access_key_id: data.accessKey,
        secret_access_key: data.secretKey,
        server: data.endPoint,
      };
      tokenCatch = params;
      return params;
    });
}

const defaultBucket = 'cy-pic';
let obsClient: any = null;

function obsUpload({ bucket = defaultBucket, ...option }) {
  return loadParams().then((params) => {
    if (obsClient === null) {
      tokenCatch.Bucket = bucket;
      obsClient = new ObsClient(tokenCatch);
    }
    return clientPut(bucket, option);
  });
}

function clientPut(bucket, option) {
  const { file, filename, ProgressCallback, cancel } = option;
  let cp;
  return new Promise((resolve, reject) => {
    const fileName = filename || `${uuIdV4()}_${file.name}`;

    const requestOption = {
      Bucket: bucket, // 桶名
      Key: fileName, // 文件名    此处用的是uuid
      SourceFile: file, // 流文件
      PartSize: 3 * 1024 * 1024, // 分片大小
      TaskNum: 10, // 并发上传分片数
      ProgressCallback,
      ResumeCallback(resumeHook, uploadCheckpoint) {
        // 获取取消断点续传上传任务控制参数
        cancel.value = resumeHook.cancel;

        // 记录断点
        cp = uploadCheckpoint;
      },
    } as Record<string, any>;

    obsClient.uploadFile(requestOption, (err, result) => {
      if (!err) {
        success(result, fileName, resolve);
        return;
      }
      const cancelMessage = 'the process of uploadFile is suspened, you can retry with the uploadCheckpoint';

      if (err?.message === cancelMessage) {
        reject(err);
        return;
      }

      obsClient.uploadFile({ UploadCheckpoint: cp, ProgressCallback }, (errs, results) => {
        if (!errs) {
          success(results, fileName, resolve);
          return;
        }

        reject(err);
      });
    });
  });

  function success(result: any, fileName, resolve) {
    if (result.CommonMsg.Status < 300 && result.InterfaceResult) {
      // url  https://Bucket.server/file.name    server不要https
      const url = `https://${bucket}.obs.cn-north-4.myhuaweicloud.com/${fileName}`;
      resolve({
        url,
        file,
        obsFileName: fileName,
      });
    }
  }
}

export default obsUpload;
