// 处理style属性绑定转换
function processStyleBindings($, tagName) {
  // 遍历所有包含style属性的元素
  $('[style]').each((i, el) => {
    const $el = $(el);
    const styleValue = $el.attr(tagName);

    // 检查style属性值是否包含插值语法
    if (typeof styleValue === 'string' && styleValue.includes('{{') && styleValue.includes('}}')) {
      // 处理样式插值
      const processedStyle = processStyleInterpolation(styleValue);

      // 添加Vue的样式绑定语法
      $el.attr(`:${tagName}`, processedStyle);

      // 移除原始style属性
      $el.removeAttr(tagName);
    }
  });
}

// 处理样式插值
function processStyleInterpolation(styleValue) {
  // 替换所有插值为模板字符串语法
  return '`' + styleValue.replace(/\{\{(.*?)\}\}/g, '${$1}') + '`';
}

module.exports = processStyleBindings;
