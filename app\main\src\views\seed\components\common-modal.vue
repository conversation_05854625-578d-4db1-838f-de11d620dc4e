<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :ok-text="okText"
    :cancel-text="cancelText"
    :footer="footer"
    :width="width"
    @ok="$emit('ok')"
    @cancel="$emit('cancel')"
    @after-open="$emit('afterOpen')"
  >
    <slot />
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Modal as AModal } from '@arco-design/web-vue';

const props = defineProps({
  visible: Boolean,
  title: String,
  okText: {
    type: String,
    default: '确认'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  footer: {
    type: [Boolean, null],
    default: undefined
  },
  width: {
    type: [String, Number],
    default: undefined
  }
});
const emit = defineEmits(['update:visible', 'ok', 'cancel', 'afterOpen']);

const visible = ref(props.visible);
watch(() => props.visible, val => { visible.value = val; });
watch(visible, val => { emit('update:visible', val); });
</script> 