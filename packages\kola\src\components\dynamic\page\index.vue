<template>
  <div class="dynamic-page" id="dynamicPage">
    <header v-if="title">
      <div class="title">{{ title }}</div>
    </header>
    <div class="filter" v-if="props.filter">
      <Filter
        v-model="filter"
        :form-schema="props.filter.formSchema"
        :reset-form-data="props.filter.resetFormData"
        :on-search="handleSearch"
        :on-reset="handleReset"
      />
    </div>
    <OperationBar
      class="operation-bar"
      :operation="operations"
      :filter="filter"
      v-if="props.operation?.length > 0 && selectedKeys.length == 0"
      :reset-columns="initColumns"
      :hide-reset-columns-width="hideResetColumnsWidth"
    ></OperationBar>
    <BatchOperationBar
      :operations="props.batchOperation"
      :hide-selected-tip="props.hideSelectedTip"
    ></BatchOperationBar>
    <div class="table">
      <DynamicTable
        ref="tableRef"
        :table="table"
        :auto-load="autoLoad"
        :row-key="rowKey"
        @select="onSelect"
        :is-multiple-selection="useMultipleSelection"
        :is-single-selection="isSingleSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, provide, computed } from 'vue';
  import { uniqueId, isFunction, cloneDeep } from 'lodash';
  import OperationBar from './operation-bar/index.vue';
  import Filter from './filter/index.vue';
  import DynamicTable from '../table/index.vue';
  import BatchOperationBar from './batch-operation-bar/index.vue';

  import { DPage } from '../types/page';
  import { RefreshTable } from '../types/table';
  import useDispatch from '../hooks/dispatch';
  import useBatch from '../hooks/batch';

  defineOptions({ name: 'DynamicPage' });

  const props = withDefaults(defineProps<DPage>(), {
    autoLoad: true,
    batchOperation: () => [],
    hideSelectedTip: false,
    rowKey: 'id',
  });

  const emit = defineEmits(['select', 'search', 'reset']);

  const { selectedKeys } = useBatch();

  const defaultFormData = isFunction(props.filter?.defaultFormData)
    ? props.filter?.defaultFormData()
    : props.filter?.defaultFormData;
  const filter = ref(cloneDeep(defaultFormData) ?? {});

  const tableRef = ref<{ refreshTable: RefreshTable }>();
  const id = uniqueId();
  provide('pageId', id);
  const dispatch = useDispatch(id);

  function refreshTable(resetPagination) {
    dispatch({
      type: 'refreshTable',
      payload: {
        resetPagination,
      },
    });
  }

  function setFilter(val) {
    Object.keys(val).forEach((key) => {
      filter.value[key] = val[key];
    });
  }

  provide('filter', filter);

  provide('getCustomParams', props.getCustomParams);

  function onSelect(rows) {
    emit('select', rows);
  }

  function initColumns() {
    tableRef.value.initColumns();
  }

  const handleSearch = () => {
    emit('search', filter);
  };
  const handleReset = () => {
    emit('reset', filter);
  };
  provide('initColumns', initColumns);
  defineExpose({
    refreshTable,
    tableRef,
    setFilter,
    getFilter: () => filter.value,
  });

  const operations = computed(() => {
    const operation = cloneDeep(props.operation);
    return operation.filter((item) => item.clickActionType !== 'batch');
  });

  const useMultipleSelection = computed(() => {
    return props.operation.some((item) => item.clickActionType === 'batch') || props?.isMultipleSelect;
  });
</script>

<style scoped lang="less">
  .dynamic-page {
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .filter {
      margin-bottom: 16px;
      padding: 16px 24px;
      background-color: #fff;
      position: relative;
      padding-bottom: 0;

      :deep(.arco-form-layout-inline .arco-form-item) {
        width: 100% !important;
        min-width: 100% !important;
      }

      :deep(.arco-select) {
        width: 100% !important;
        min-width: 100% !important;
      }

      :deep(.arco-input-wrapper) {
        width: 100% !important;
      }
    }

    .operation-bar {
      background-color: #fff;
      padding: 16px 24px;
      padding-bottom: 0;
    }

    header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .title {
        color: var(--color-text-1);
        font-size: 16px;
        font-weight: 500;
        margin-right: 16px;
        margin-top: 4px;
      }
    }
  }

  .table {
    overflow: hidden;
    flex: 1;
    background-color: #fff;
    padding: 16px 24px;
  }
</style>
