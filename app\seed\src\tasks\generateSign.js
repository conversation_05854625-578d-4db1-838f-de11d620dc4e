// 生成签名文件

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');
const { OUT_PUT_DIR } = require('../constant.js');


function generateSign(meta) {
    const { pkg } = meta;  // 从meta中获取pkg
    // 创建 sign 文件夹
    const signDir = path.join(OUT_PUT_DIR, 'sign');
    if (!fs.existsSync(signDir)) {
        fs.mkdirSync(signDir, { recursive: true });
    }

    // 定义 OpenSSL 命令参数
    const options = {
        days: 36500,
        nodes: true,
        subj: `/CN=${pkg}`
    };

    // 构建 OpenSSL 命令
    const opensslCommand = `openssl req -x509 -newkey rsa:4096 \
  -keyout ${path.join(signDir, 'private.pem')} \
  -out ${path.join(signDir, 'certificate.pem')} \
  -days ${options.days} \
  ${options.nodes ? '-nodes' : ''} \
  -subj "${options.subj}"`;
    try {
        // 执行 OpenSSL 命令
        execSync(opensslCommand, { stdio: 'inherit' });
        console.log('签名文件已成功生成在 sign 文件夹中');
    } catch (error) {
        console.error('生成签名文件时出错:', error);
    }
}


module.exports = {
    generateSign
}