<template>
  <DynamicPage
    :filter="filterConfig"
    :table="tableConfig"
    :operation="operations"
    :batch-operation="batchOperations"
    auto-load
    row-key="_id"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import AppVersionApi from '@/api/AppVersion';

  // Filter 配置
  const filterConfig = ref({
    formSchema: {
      fields: [
        {
          name: 'name',
          label: 'name',
          type: 'text',
          placeholder: '请输入name',
        },
        {
          name: 'version',
          label: 'version',
          type: 'text',
          placeholder: '请输入version',
        },
        {
          name: 'appLink',
          label: 'appLink',
          type: 'text',
          placeholder: '请输入appLink',
        },
        {
          name: 'brand',
          label: 'brand',
          type: 'text',
          placeholder: '请输入brand',
        },
        {
          name: 'releaseDate',
          label: 'releaseDate',
          type: 'datePicker',
          placeholder: '请输入releaseDate',
        },
      ],
    },
  });

  // 数据加载方法
  async function loadData(filter, pagination) {
    return AppVersionApi.list({
      ...filter,
      ...pagination,
    });
  }

  // Table 配置
  const tableConfig = ref({
    columns: [
      {
        title: 'name',
        dataIndex: 'name',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'version',
        dataIndex: 'version',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'appLink',
        dataIndex: 'appLink',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'brand',
        dataIndex: 'brand',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'releaseDate',
        dataIndex: 'releaseDate',
        width: 150,
        ellipsis: true,
      },
      {
        title: '操作',
        dataIndex: 'operations',
        customRender: {
          type: 'operations',
          props: {
            operations: [
              {
                text: '编辑',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    'title': '编辑',
                    'esc-to-close': false,
                  },
                  contentType: 'form',
                  form: {
                    formSchema: {
                      fields: [
                        {
                          name: 'name',
                          label: 'name',
                          type: 'text',
                          placeholder: '请输入name',
                        },
                        {
                          name: 'version',
                          label: 'version',
                          type: 'text',
                          placeholder: '请输入version',
                        },
                        {
                          name: 'appLink',
                          label: 'appLink',
                          type: 'text',
                          placeholder: '请输入appLink',
                        },
                        {
                          name: 'brand',
                          label: 'brand',
                          type: 'text',
                          placeholder: '请输入brand',
                        },
                        {
                          name: 'releaseDate',
                          label: 'releaseDate',
                          type: 'datePicker',
                          placeholder: '请输入releaseDate',
                        },
                      ],
                    },
                  },
                  getDefaultValue(rowData) {
                    const { _id: id } = rowData;
                    return AppVersionApi.getById(id).then((res) => res.data);
                  },
                  action: async ({ formData, record, refreshTable }) => {
                    const { _id: id } = record;
                    await AppVersionApi.update(id, formData);
                    refreshTable();
                  },
                },
              },
              {
                text: '删除',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    title: '确认删除',
                  },
                  contentType: 'text',
                  text: '确认删除？',
                  action: async ({ record, refreshTable }) => {
                    const { _id: id } = record;
                    await AppVersionApi.delete(id);
                    refreshTable();
                  },
                },
              },
            ],
          },
        },
      },
    ],
    isPageable: true,
    pagination: {
      current: 1,
      pageSize: 10,
      showPageSize: true,
      showTotal: true,
      total: 0,
      showJumper: true,
      pageSizeOptions: [10, 20, 50],
    },
    load: {
      action: loadData,
    },
  });

  // 操作按钮配置
  const operations = ref([
    {
      text: '新增',
      props: {
        type: 'primary',
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '新增',
          width: 800,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                name: 'name',
                label: 'name',
                type: 'text',
                placeholder: '请输入name',
              },
              {
                name: 'version',
                label: 'version',
                type: 'text',
                placeholder: '请输入version',
              },
              {
                name: 'appLink',
                label: 'appLink',
                type: 'text',
                placeholder: '请输入appLink',
              },
              {
                name: 'brand',
                label: 'brand',
                type: 'text',
                placeholder: '请输入brand',
              },
              {
                name: 'releaseDate',
                label: 'releaseDate',
                type: 'datePicker',
                placeholder: '请输入releaseDate',
              },
            ],
          },
        },
        getDefaultValue: () => {
          return {
            name: '',
            version: '',
            appLink: '',
            brand: '',
            releaseDate: new Date(),
          };
        },
        close: (data) => {
          data.refreshTable();
        },
        action: async (data) => {
          await AppVersionApi.create(data.formData);
          Message.success('操作成功');
          data.refreshTable();
        },
      },
    },
    {
      text: '批量操作',
      props: {
        type: 'primary',
      },
      clickActionType: 'batch',
    },
  ]); // 单个操作按钮
  const batchOperations = ref([]); // 批量操作按钮
</script>
