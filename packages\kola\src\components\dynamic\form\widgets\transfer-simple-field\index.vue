<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
    :label-col-flex="field.labelWidth"
  >
    <a-transfer
      show-search
      :data="sourceData"
      class="remote-transfer"
      :class="{ searching: loading }"
      v-model="value"
      :simple="field.simple"
      :one-way="field.oneWay ?? true"
      :source-input-search-props="sourceSearchProps"
      :target-input-search-props="searchProps"
      @select="handleSelect"
    >
      <template #source-title="{ countSelected, countTotal, checked, indeterminate, onSelectAllChange }">
        <div :style="styleHeader">
          {{ getSourceTitle({ countSelected, countTotal }) }}
          <a-checkbox :model-value="checked" :indeterminate="indeterminate" @change="onSelectAllChange" />
        </div>
        <a-spin v-if="loading" class="spin"></a-spin>
      </template>
      <template #target-title="{ countSelected, countTotal, onClear }">
        <div :style="styleHeader">
          {{ getTargetTitle({ countSelected, countTotal }) }}
          <IconDelete @click="onClear" />
        </div>
      </template>
      <template #item="{ label, value }">
        <a-tooltip mini :content="label" :popup-visible="value === tooltip">
          <span @mouseenter="mouseenter($event, value)" @mouseleave="mouseleave">{{ label }}</span>
        </a-tooltip>
      </template>
    </a-transfer>
  </a-form-item>
</template>

<script setup lang="ts">
  import { inject, ref, watchEffect, computed, Ref } from 'vue';
  import { isFunction, debounce } from 'lodash';
  import { TransferSimpleField } from '../../../types/form';

  const props = defineProps<{
    field: TransferSimpleField;
    path: string;
  }>();

  const formData = inject<Ref<Record<string, any>>>('formData');
  const search = ref();
  const loading = ref(false);
  const tooltip = ref('');
  const debounceSearch = debounce(onSearch, 200);
  const styleHeader = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingRight: '8px',
  };
  const searchProps = {
    placeholder: '请输入',
    allowClear: false,
  };
  const sourceSearchProps = {
    ...searchProps,
    oninput: (evt) => {
      if (!props.field.remote) return;
      if (search.value === evt.target.value) return;
      search.value = evt.target.value;
      sourceData.value = [];
      loading.value = true;
      debounceSearch('source');
    },
  };
  const value = defineModel<any>({
    default: [],
    set(val) {
      props.field.onChange?.(val, formData);
      if (props.field.setter) {
        return props.field.setter(val);
      }
      return val;
    },
  });
  const sourceData = ref<{ value: string | number; label: string }[]>([]);

  async function getSourceData() {
    if (typeof props.field.source.data === 'function') {
      sourceData.value = await props.field.source.data(props.field, formData?.value as any, props.path, search.value);
      loading.value = false;
    } else {
      sourceData.value = props.field.source.data || [];
    }
  }

  function getSourceTitle({ countSelected, countTotal }) {
    if (typeof props.field.sourceTitle === 'function') {
      return props.field.sourceTitle({ countSelected, countTotal });
    }
    return props.field.sourceTitle || 'Source';
  }

  function getTargetTitle({ countSelected, countTotal }) {
    if (typeof props.field.targetTitle === 'function') {
      return props.field.targetTitle({ countSelected, countTotal });
    }
    return props.field.targetTitle || 'Target';
  }

  async function onSearch(type) {
    if (!props.field.search || type === 'target') return;
    props.field.search(search.value, formData?.value);
  }

  function mouseenter(evt, val) {
    const pNode = evt.target?.parentNode;
    if (pNode && pNode.scrollWidth > pNode.offsetWidth) {
      tooltip.value = val;
    }
  }

  function mouseleave() {
    tooltip.value = '';
  }

  function handleSelect(selects) {
    const newSelects = [...value.value, ...selects];
    value.value = [...new Set(newSelects)];
  }

  watchEffect(() => {
    getSourceData();
  });
</script>

<style lang="less" scoped>
  .remote-transfer {
    position: relative;
    width: 90%;

    min .spin {
      position: absolute;
      top: 50%;
      left: 88px;
    }
  }

  :deep(.arco-transfer-view-body) {
    .arco-transfer-list-item {
      height: auto;
      min-height: 36px;
      line-height: normal;
      padding: 5px 10px;

      .arco-transfer-list-item-checkbox {
        .arco-checkbox-label {
          word-break: break-all;
          overflow: visible;
          white-space: normal;
          text-overflow: clip;
        }
      }
    }
  }

  :deep(.searching) {
    .arco-transfer-view-empty {
      visibility: hidden;
    }
  }

  :deep(.arco-transfer-operations) {
    display: none;
  }

  :deep(.arco-transfer-view-target) {
    margin-left: 24px;
  }

  :deep(.arco-transfer-view) {
    width: 50%;
    //height: auto;
    height: 300px;
    overflow-y: auto;
  }
</style>
