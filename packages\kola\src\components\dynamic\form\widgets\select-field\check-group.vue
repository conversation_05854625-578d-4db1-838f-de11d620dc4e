<template>
  <div>
    <a-checkbox
      v-if="showAll"
      :model-value="selectCtx.checkedAll"
      :indeterminate="selectCtx.indeterminate"
      @change="selectCtx.handleChangeAll"
    >
      全选
    </a-checkbox>
    <a-checkbox-group
      v-model="value"
      @change="selectCtx.handleChange"
      :direction="direction"
      :class="{ 'bordered-group': bordered }"
    >
      <a-checkbox v-for="item in sourceData" :value="item[valueKey]" :key="item[valueKey]" :disabled="item.disabled">
        <component :is="item[labelKey]" v-if="isVNode(item[labelKey])" />
        <template v-else>{{ item[labelKey] }}</template>
      </a-checkbox>
    </a-checkbox-group>
  </div>
</template>

<script setup lang="ts">
  import { inject, h, isVNode } from 'vue';
  import { Option } from '../../../types/form';
  import { SelectContext, selectInjectionKey } from './context';

  defineProps<{
    sourceData: Option[];
    valueKey: string;
    labelKey: string;
    format: string;
    showAll: boolean;
    direction: 'vertical' | 'horizontal';
    bordered?: boolean;
  }>();
  const selectCtx = inject<Partial<SelectContext>>(selectInjectionKey, {});

  const value = defineModel<any>();
</script>

<style lang="less" scoped>
  .bordered-group {
    border: 1px solid var(--color-border-2);
    border-radius: 2px;

    :deep(.arco-checkbox) {
      &:not(:last-child) {
        border-bottom: 1px solid var(--color-border-2);
      }
    }
  }
</style>
