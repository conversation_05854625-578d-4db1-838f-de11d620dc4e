const express = require('express');
const router = express.Router();
const UserController = require('../controller/User');

// User相关路由
router.post('/Users', UserController.createUser); // 创建User
router.get('/Users', UserController.getAllUsers); // 获取User列表
router.get('/Users/<USER>', UserController.getUserById); // 获取单个User
router.put('/Users/<USER>', UserController.updateUser); // 更新User
router.delete('/Users/<USER>', UserController.deleteUser); // 删除User

module.exports = router;
