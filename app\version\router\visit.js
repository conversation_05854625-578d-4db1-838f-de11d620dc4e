const express = require('express');
const router = express.Router();
const VisitController = require('../controller/visit');

// Visit相关路由
router.post('/Visits', VisitController.createVisit); // 创建Visit
router.get('/Visits', VisitController.getAllVisits); // 获取Visit列表
router.get('/Visits/:id', VisitController.getVisitById); // 获取单个Visit
router.put('/Visits/:id', VisitController.updateVisit); // 更新Visit
router.delete('/Visits/:id', VisitController.deleteVisit); // 删除Visit

module.exports = router;
