#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const generateDynamicPage = require('./src/gene-template');
const {generateControllerFile, generateRouterFile} = require("./src/gen-server");
const {generateApiDoc} = require("./src/gen-api");

// 解析 Mongoose Schema
function parseSchema(schema) {
  const fields = [];
  for (const [key, value] of Object.entries(schema.paths)) {
    // eslint-disable-next-line no-continue
    if (key === '_id' || key === '__v') continue;
    fields.push({
      name: key,
      type: value.instance, // 字段类型，如 String, Number, Date 等
      required: value.isRequired, // 是否必填
      unique: value.options.unique, // 是否唯一
      default: value.options.default, // 默认值
    });
  }
  return fields;
}

/**
 * 查找包含 package.json 的根目录
 * @param {string} [dir=process.cwd()] - 起始目录，默认为当前工作目录
 * @returns {string} - 根目录路径
 */
function findRootDir(dir = process.cwd()) {
  // 检查当前目录是否有 package.json
  if (fs.existsSync(path.join(dir, 'package.json'))) {
    return dir;
  }

  // 获取上一级目录
  const parentDir = path.dirname(dir);

  // 如果已经到达文件系统的根目录，则停止查找
  if (parentDir === dir) {
    throw new Error('未找到包含 package.json 的根目录');
  }

  // 递归查找上一级目录
  return findRootDir(parentDir);
}

// CLI 工具主逻辑
async function main() {
  program
    .version('1.0.0')
    .description('根据 Mongoose 模型生成 Vue DPage 组件')
    .requiredOption('-m, --model <path>', 'Mongoose 模型文件路径')
    .parse(process.argv);

  const { model } = program.opts();

  // 动态加载 Mongoose 模型
  const modelPath = path.resolve(process.cwd(), model);
  // eslint-disable-next-line global-require
  const modelModule = require(modelPath);
  const { schema } = modelModule;

  // 解析 Schema
  const fields = parseSchema(schema);

  // 生成 DPage 组件
  const { modelName } = modelModule;
  const dpageCode = generateDynamicPage(modelName, fields);

  // 输出文件
  const outputPath = path.resolve(findRootDir(), `${modelName}Page.vue`);
  fs.writeFileSync(outputPath, dpageCode);

  console.log(`DPage 组件已生成: ${outputPath}`);

  // 生成控制器文件
  const controllerCode = generateControllerFile(modelName);
  console.log(process.cwd())
  const controllerPath = path.resolve(findRootDir(), `controller/${modelName}.js`);
  fs.writeFileSync(controllerPath, controllerCode);

  // 生成路由文件
  const routerCode = generateRouterFile(modelName);
  const routerPath = path.resolve(findRootDir(), `router/${modelName}.js`);
  fs.writeFileSync(routerPath, routerCode);

  console.log(`控制器文件已生成: ${controllerPath}`);
  console.log(`路由文件已生成: ${routerPath}`);

  // api 调用文档
  const apiDoc = generateApiDoc(modelName);
  const apiDocPath = path.resolve(path.resolve(findRootDir(), '../main/src/api'), `${modelName}.js`);
  fs.writeFileSync(apiDocPath, apiDoc);
  console.log(`API 调用文档已生成: ${apiDocPath}`);
}

main().catch((err) => {
  console.error('生成失败:', err);
});
