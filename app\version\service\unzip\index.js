/**
 * 根据下载地址下载到本地 -> 解压 -> 解混淆 -> 保存到磁盘
 */
const fs = require('fs');
const path = require('path');
const https = require('https');
const { v4: uuidv4 } = require('uuid');
const AdmZip = require('adm-zip');
const os = require('os');

const { deobfuscate } = require('obfuscator-io-deobfuscator');
const { processFiles } = require('../../../pack/src/utils/decode');
const ProcessPool = require('../../../pack/src/handler/process-pool');

const processPool = new ProcessPool(os.cpus().length - 2); // 创建最大 4 进程的进程池

function downloadRpk(url, fileName) {
  return new Promise((resolve, reject) => {
    // 生成临时文件名
    const tempDir = path.resolve(__dirname, '../../rpks/zip');
    const tempFileName = path.resolve(tempDir, `${fileName}.zip`);

    // 确保输出目录存在
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    // 创建可写流
    const fileStream = fs.createWriteStream(tempFileName);

    // 发起HTTPS请求下载文件
    https
      .get(url, (response) => {
        // 检查响应状态码
        if (response.statusCode !== 200) {
          reject(new Error(`下载失败，状态码：${response.statusCode}`));
          return;
        }

        // 将数据写入文件
        response.pipe(fileStream);

        // 监听完成事件
        fileStream.on('finish', () => {
          fileStream.close(() => {
            resolve(tempFileName);
          });
        });
      })
      .on('error', (err) => {
        // 删除临时文件
        fs.unlink(tempFileName, () => {});
        reject(err);
      });
  });
}

/**
 * 解压文件到指定目录并删除原文件
 * @param {string} filePath - 要解压的文件路径
 * @param {string} outputDir - 解压输出目录
 * @returns {Promise<string>} 返回解压后的目录路径
 */
function unzipFile(filePath, outputDir) {
  return new Promise((resolve, reject) => {
    try {
      /* // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      } */

      // 读取zip文件
      const zip = new AdmZip(filePath);

      // 解压到指定目录
      zip.extractAllTo(outputDir, true);

      resolve(outputDir);
    } catch (error) {
      // 如果出错，尝试删除临时文件
      reject(error);
    }
  });
}

function deobfuscateFile(filePath, outputDir) {
  // eslint-disable-next-line no-useless-catch
  try {
    // 确保输出目录存在
    /* if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    } */

    const fileList = [];
    processFiles(filePath, outputDir, deobfuscate, fileList);
    console.log(fileList);

    return Promise.all(
      fileList.map((data) =>
        processPool.runTask(data, path.resolve(__dirname, '../../../pack/src/handler/process-pool/decode-worker.js'))
      )
    );
  } catch (error) {
    throw error;
  }
}

function saveRpk(url, fileName) {
  return downloadRpk(url, fileName)
    .then((filePath) => {
      return unzipFile(filePath, path.resolve(__dirname, '../../rpks/unzip', fileName));
    })
    .then((outputDir) => {
      return deobfuscateFile(outputDir, path.join(__dirname, '../../rpks/decode', fileName));
    })
    .catch((e) => {
      console.log(e);
    });
}

module.exports = {
  saveRpk,
};
