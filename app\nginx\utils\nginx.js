const { exec } = require('child_process');
const { startCommand } = require('../config');
// 定义要执行的 Nginx 检查命令
const checkCommand = 'nginx -t';

// 执行命令

function checkNginx() {
  return new Promise((resolve, reject) => {
    exec(checkCommand, (error, stdout, stderr) => {
      console.log(error, stdout, stderr);
      if (error) {
        reject(error);
        return;
      }

      resolve(stderr);
    });
  });
}

function startNginx() {
  return new Promise((resolve, reject) => {
    exec(startCommand, (error, stdout, stderr) => {
      if (error) {
        reject(error);
        return;
      }

      resolve(stdout);
    });
  });
}

module.exports = {
  checkNginx,
  startNginx,
};
