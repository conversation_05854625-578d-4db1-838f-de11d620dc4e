<template>
  <DynamicPage
    :filter="filterConfig"
    :table="tableConfig"
    :operation="operations"
    :batch-operation="batchOperations"
    auto-load
    row-key="_id"
  />
</template>

<script setup>
import { ref } from 'vue';
import { Message } from '@arco-design/web-vue';
import SeedAppApi from '@/api/SeedApp';


// Filter 配置
const filterConfig = ref({
  formSchema: {
    fields: [
      {
        name: 'name',
        label: 'name',
        type: 'text',
        placeholder: '请输入name',
      },
          {
        name: 'packageName',
        label: 'packageName',
        type: 'text',
        placeholder: '请输入packageName',
      },
          {
        name: 'version',
        label: 'version',
        type: 'text',
        placeholder: '请输入version',
      },
          {
        name: 'icon',
        label: 'icon',
        type: 'text',
        placeholder: '请输入icon',
      },
          {
        name: 'tabs',
        label: 'tabs',
        type: 'text',
        placeholder: '请输入tabs',
      },
          {
        name: 'theme',
        label: 'theme',
        type: 'text',
        placeholder: '请输入theme',
      },
          {
        name: 'private',
        label: 'private',
        type: 'text',
        placeholder: '请输入private',
      },
          {
        name: 'agreement',
        label: 'agreement',
        type: 'text',
        placeholder: '请输入agreement',
      },
          {
        name: 'createdAt',
        label: 'createdAt',
        type: 'datePicker',
        placeholder: '请输入createdAt',
      },
          {
        name: 'updatedAt',
        label: 'updatedAt',
        type: 'datePicker',
        placeholder: '请输入updatedAt',
      }
    ],
  },
});

// 数据加载方法
async function loadData(filter, pagination) {
  return SeedAppApi.list({
    ...filter,
    ...pagination
  });
}
// Table 配置
const tableConfig = ref({
  columns: [
    {
      title: 'name',
      dataIndex: 'name',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'packageName',
      dataIndex: 'packageName',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'version',
      dataIndex: 'version',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'icon',
      dataIndex: 'icon',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'tabs',
      dataIndex: 'tabs',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'theme',
      dataIndex: 'theme',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'private',
      dataIndex: 'private',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'agreement',
      dataIndex: 'agreement',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'createdAt',
      dataIndex: 'createdAt',
      width: 150,
      ellipsis: true,
    },
          {
      title: 'updatedAt',
      dataIndex: 'updatedAt',
      width: 150,
      ellipsis: true,
    },
          {
    title: '操作',
    dataIndex: 'operations',
    customRender: {
      type: 'operations',
      props: {
        operations: [
          {
            text: '编辑',
            props: {
              type: 'text',
            },
            clickActionType: 'modal',
            modal: {
              props: {
                'title': '编辑',
                'esc-to-close': false,
              },
              contentType: 'form',
              form: {
                formSchema: {
                  fields: [
                    {
        name: 'name',
        label: 'name',
        type: 'text',
        placeholder: '请输入name',
      },
          {
        name: 'packageName',
        label: 'packageName',
        type: 'text',
        placeholder: '请输入packageName',
      },
          {
        name: 'version',
        label: 'version',
        type: 'text',
        placeholder: '请输入version',
      },
          {
        name: 'icon',
        label: 'icon',
        type: 'text',
        placeholder: '请输入icon',
      },
          {
        name: 'tabs',
        label: 'tabs',
        type: 'text',
        placeholder: '请输入tabs',
      },
          {
        name: 'theme',
        label: 'theme',
        type: 'text',
        placeholder: '请输入theme',
      },
          {
        name: 'private',
        label: 'private',
        type: 'text',
        placeholder: '请输入private',
      },
          {
        name: 'agreement',
        label: 'agreement',
        type: 'text',
        placeholder: '请输入agreement',
      },
          {
        name: 'createdAt',
        label: 'createdAt',
        type: 'datePicker',
        placeholder: '请输入createdAt',
      },
          {
        name: 'updatedAt',
        label: 'updatedAt',
        type: 'datePicker',
        placeholder: '请输入updatedAt',
      }
                  ],
                },
              },
              getDefaultValue(rowData) {
                const { _id: id } = rowData;
                return SeedAppApi.getById(id).then((res) => res.data);
              },
              action: async ({ formData, record, refreshTable }) => {
                const { _id: id } = record;
                await SeedAppApi.update(id, formData);
                refreshTable();
              },
            },
          },
          {
            text: '删除',
            props: {
              type: 'text',
            },
            clickActionType: 'modal',
            modal: {
              props: {
                title: '确认删除',
              },
              contentType: 'text',
              text: '确认删除？',
              action: async ({ record, refreshTable }) => {
                const { _id: id } = record;
                await SeedAppApi.delete(id);
                refreshTable();
              },
            },
          },
        ],
      },
    },
  }
  ],
  isPageable: true,
  pagination: {
    current: 1,
    pageSize: 10,
    showPageSize: true,
    showTotal: true,
    total: 0,
    showJumper: true,
    pageSizeOptions: [10, 20, 50],
  },
  load: {
    action: loadData,
  },
});

// 操作按钮配置
const operations = ref([
    {
      text: '新增',
      props: {
        type: 'primary',
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '新增',
          width: 800,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
        name: 'name',
        label: 'name',
        type: 'text',
        placeholder: '请输入name',
      },
          {
        name: 'packageName',
        label: 'packageName',
        type: 'text',
        placeholder: '请输入packageName',
      },
          {
        name: 'version',
        label: 'version',
        type: 'text',
        placeholder: '请输入version',
      },
          {
        name: 'icon',
        label: 'icon',
        type: 'text',
        placeholder: '请输入icon',
      },
          {
        name: 'tabs',
        label: 'tabs',
        type: 'text',
        placeholder: '请输入tabs',
      },
          {
        name: 'theme',
        label: 'theme',
        type: 'text',
        placeholder: '请输入theme',
      },
          {
        name: 'private',
        label: 'private',
        type: 'text',
        placeholder: '请输入private',
      },
          {
        name: 'agreement',
        label: 'agreement',
        type: 'text',
        placeholder: '请输入agreement',
      },
          {
        name: 'createdAt',
        label: 'createdAt',
        type: 'datePicker',
        placeholder: '请输入createdAt',
      },
          {
        name: 'updatedAt',
        label: 'updatedAt',
        type: 'datePicker',
        placeholder: '请输入updatedAt',
      }
            ],
          },
        },
        getDefaultValue: () => {
          return {
    name: '',
    packageName: '',
    version: '',
    icon: '',
    tabs: null,
    theme: null,
    private: '',
    agreement: '',
    createdAt: new Date(),
    updatedAt: new Date()
  }
        },
        close: (data) => {
          data.refreshTable();
        },
        action: async (data) => {
          await SeedAppApi.create(data.formData);
          Message.success('操作成功');
          data.refreshTable();
        },
      },
    },
    {
      text: '批量操作',
      props: {
        type: 'primary',
      },
      clickActionType: 'batch',
    },
  ]); // 单个操作按钮
const batchOperations = ref([]); // 批量操作按钮


</script>
