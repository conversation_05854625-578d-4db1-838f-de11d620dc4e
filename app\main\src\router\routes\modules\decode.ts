import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const DASHBOARD: AppRouteRecordRaw = {
  path: '/decode',
  name: 'decode',
  components: {
    default: DEFAULT_LAYOUT,
    content: () => import('@/views/decode/index.vue'),
  },
  meta: {
    name: '解混淆',
    requiresAuth: true,
    icon: 'icon-sun',
    order: 4,
    roles: ['admin','FRE'],
  },
};

export default DASHBOARD;
