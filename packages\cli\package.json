{"name": "@repo/cli", "version": "0.0.0", "private": true, "module": "index.js", "types": "index.d.ts", "main": "index.js", "scripts": {"kola-json": "node ./src/apps/kola-json-creator/index.ts"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/sdk": "workspace:*", "@repo/typescript-config": "workspace:*"}, "dependencies": {"commander": "^13.0.0", "inquirer": "^12.3.0", "mitt": "^3.0.1", "mongoose": "^8.9.2"}}