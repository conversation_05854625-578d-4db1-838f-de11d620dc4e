<template>
  <DynamicPage
    :filter="filterConfig"
    :table="tableConfig"
    :operation="operations"
    :batch-operation="batchOperations"
    auto-load
    row-key="_id"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import VisitApi from '@/api/visit';

  // Filter 配置
  const filterConfig = ref({
    formSchema: {
      fields: [
        {
          name: 'path',
          label: 'path',
          type: 'text',
          placeholder: '请输入path',
        },
        {
          name: 'userId',
          label: 'userId',
          type: 'text',
          placeholder: '请输入userId',
        },
        {
          name: 'ip',
          label: 'ip',
          type: 'text',
          placeholder: '请输入ip',
        },
        {
          name: 'userAgent',
          label: 'userAgent',
          type: 'text',
          placeholder: '请输入userAgent',
        },
        {
          name: 'timestamp',
          label: 'timestamp',
          type: 'datePicker',
          placeholder: '请输入timestamp',
        },
        {
          name: 'createdAt',
          label: 'createdAt',
          type: 'datePicker',
          placeholder: '请输入createdAt',
        },
        {
          name: 'updatedAt',
          label: 'updatedAt',
          type: 'datePicker',
          placeholder: '请输入updatedAt',
        },
      ],
    },
  });

  // 数据加载方法
  async function loadData(filter, pagination) {
    return VisitApi.list({
      ...filter,
      ...pagination,
    });
  }

  // Table 配置
  const tableConfig = ref({
    columns: [
      {
        title: 'path',
        dataIndex: 'path',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'userId',
        dataIndex: 'userId',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'ip',
        dataIndex: 'ip',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'userAgent',
        dataIndex: 'userAgent',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'timestamp',
        dataIndex: 'timestamp',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'createdAt',
        dataIndex: 'createdAt',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'updatedAt',
        dataIndex: 'updatedAt',
        width: 150,
        ellipsis: true,
      },
      {
        title: '操作',
        dataIndex: 'operations',
        customRender: {
          type: 'operations',
          props: {
            operations: [
              {
                text: '编辑',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    'title': '编辑',
                    'esc-to-close': false,
                  },
                  contentType: 'form',
                  form: {
                    formSchema: {
                      fields: [
                        {
                          name: 'path',
                          label: 'path',
                          type: 'text',
                          placeholder: '请输入path',
                        },
                        {
                          name: 'userId',
                          label: 'userId',
                          type: 'text',
                          placeholder: '请输入userId',
                        },
                        {
                          name: 'ip',
                          label: 'ip',
                          type: 'text',
                          placeholder: '请输入ip',
                        },
                        {
                          name: 'userAgent',
                          label: 'userAgent',
                          type: 'text',
                          placeholder: '请输入userAgent',
                        },
                        {
                          name: 'timestamp',
                          label: 'timestamp',
                          type: 'datePicker',
                          placeholder: '请输入timestamp',
                        },
                        {
                          name: 'createdAt',
                          label: 'createdAt',
                          type: 'datePicker',
                          placeholder: '请输入createdAt',
                        },
                        {
                          name: 'updatedAt',
                          label: 'updatedAt',
                          type: 'datePicker',
                          placeholder: '请输入updatedAt',
                        },
                      ],
                    },
                  },
                  getDefaultValue(rowData) {
                    const { _id: id } = rowData;
                    return VisitApi.getById(id).then((res) => res.data);
                  },
                  action: async ({ formData, record, refreshTable }) => {
                    const { _id: id } = record;
                    await VisitApi.update(id, formData);
                    refreshTable();
                  },
                },
              },
              {
                text: '删除',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    title: '确认删除',
                  },
                  contentType: 'text',
                  text: '确认删除？',
                  action: async ({ record, refreshTable }) => {
                    const { _id: id } = record;
                    await VisitApi.delete(id);
                    refreshTable();
                  },
                },
              },
            ],
          },
        },
      },
    ],
    isPageable: true,
    pagination: {
      current: 1,
      pageSize: 10,
      showPageSize: true,
      showTotal: true,
      total: 0,
      showJumper: true,
      pageSizeOptions: [10, 20, 50],
    },
    load: {
      action: loadData,
    },
  });

  // 操作按钮配置
  const operations = ref([
    {
      text: '新增',
      props: {
        type: 'primary',
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '新增',
          width: 800,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                name: 'path',
                label: 'path',
                type: 'text',
                placeholder: '请输入path',
              },
              {
                name: 'userId',
                label: 'userId',
                type: 'text',
                placeholder: '请输入userId',
              },
              {
                name: 'ip',
                label: 'ip',
                type: 'text',
                placeholder: '请输入ip',
              },
              {
                name: 'userAgent',
                label: 'userAgent',
                type: 'text',
                placeholder: '请输入userAgent',
              },
              {
                name: 'timestamp',
                label: 'timestamp',
                type: 'datePicker',
                placeholder: '请输入timestamp',
              },
              {
                name: 'createdAt',
                label: 'createdAt',
                type: 'datePicker',
                placeholder: '请输入createdAt',
              },
              {
                name: 'updatedAt',
                label: 'updatedAt',
                type: 'datePicker',
                placeholder: '请输入updatedAt',
              },
            ],
          },
        },
        getDefaultValue: () => {
          return {
            path: '',
            userId: null,
            ip: '',
            userAgent: '',
            timestamp: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        },
        close: (data) => {
          data.refreshTable();
        },
        action: async (data) => {
          await VisitApi.create(data.formData);
          Message.success('操作成功');
          data.refreshTable();
        },
      },
    },
    {
      text: '批量操作',
      props: {
        type: 'primary',
      },
      clickActionType: 'batch',
    },
  ]); // 单个操作按钮
  const batchOperations = ref([]); // 批量操作按钮
</script>
