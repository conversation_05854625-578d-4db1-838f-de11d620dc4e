const mongoose = require('mongoose');
const { salt, sha256 } = require('../utils/tools');
const userSchema = new mongoose.Schema(
  {
    // 用户名
    username: {
      type: String,
      required: true,
      unique: true, // 用户名唯一
    },
    id: {
      type: Number,
      // required: true,
      unique: true,
    },
    // 密码
    password: {
      type: String,
      required: true,
    },
    // 用户角色（例如：admin, user）
    role: {
      type: String,
      required: true,
      default: 'user', // 默认角色为 user
    },
    // 用户状态（例如：active, inactive）
    status: {
      type: String,
      required: true,
      default: 'active', // 默认状态为 active
    },
    // 最后登录时间
    lastLogin: {
      type: Date,
    },
    salt: {
      type: String,
    },
    hash: {
      type: String,
    },
  },
  {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
  }
);

userSchema.pre('save', async function (next) {
  if (!this.isNew) {
    return next(); // 如果不是新文档，跳过
  }

  try {
    const lastUser = await User.findOne().sort({ id: -1 }); // 查找 id 最大的文档
    this.id = lastUser && lastUser.id ? lastUser.id + 1 : 1; // 设置新文档的 id
    this.salt = salt();
    this.hash = sha256(this.password + this.salt);
    next();
  } catch (err) {
    next(err);
  }
});

// 创建用户模型
const User = mongoose.model('User', userSchema);

module.exports = User;
