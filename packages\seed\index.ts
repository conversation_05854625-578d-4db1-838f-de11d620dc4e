import type { App, Plugin } from 'vue';

import SText from './src/system-comp/s-text/index';

// 动态加载所有组件
const componentModules = import.meta.glob('./src/seed-comp/**/index.{vue,ts,js}');
const metaModules = import.meta.glob('./src/seed-comp/**/meta.json', { eager: true });

// 系统组件
const systemComponents: Plugin[] = [SText];

const install = async (app: App) => {
  for (const path in componentModules) {
    const module = await componentModules[path]();
    const componentName = path.split('/')[3]; // 提取组件名（如 battery-care）
    // app.component(componentName, module.default);
    app.use(module.default as Plugin);
  }

  for (const component of systemComponents) {
    app.use(component);
  }
};

const Seed = {
  install,
};

console.log('Object.entries(metaModules)', Object.entries(metaModules));
export const metaInfo = Object.entries(metaModules).map((item) => {
  return item[1].default;
});

export default Seed;
