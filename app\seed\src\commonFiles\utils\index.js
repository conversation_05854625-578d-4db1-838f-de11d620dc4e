/**
 * 您可以将常用的方法、或系统 API，统一封装，暴露全局，以便各页面、组件调用，而无需 require / import.
 */
const prompt = require('@system.prompt')
const storage = require('@system.storage')
const router = require('@system.router')
const fetch = require('@system.fetch')
const clipboard = require('@system.clipboard')

const hook2global = global.__proto__ || global
const clickEventNameData = {}

/**
 * 拼接 get url 和参数
 */
function queryString(url, query) {
  let str = []
  for (let key in query) {
    str.push(key + '=' + query[key])
  }
  let paramStr = str.join('&')
  return paramStr ? `${url}?${paramStr}` : url
}
/**
 * toast
 * @param {*} message
 * @param {*} duration
 */
function showToast(message = '', duration = 0) {
  if (!message) return
  prompt.showToast({
    message: message,
    duration
  })
}
/**
 *
 * @param {String} title 标题
 * @param {String} message 内容
 * @param {Array} buttons 按钮的数组，按钮结构：{text:'text',color:'#333333'}，color 可选：buttons 的第 1 项为 positive button；buttons 的第 2 项（如果有）为 negative button；buttons 的第 3 项（如果有）为 neutral button。最多支持 3 个 button
 * @param {Function} callback 回调函数
 */
function showDialog(title, message, buttons, callback) {
  prompt.showDialog({
    title,
    message,
    buttons,
    success: function (data) {
      callback(data)
    },
    cancel: function () {
      LOG('handling cancel')
    },
    fail: function (data, code) {
      LOG(`handling fail, code = ${code}`)
    }
  })
}
/**
 * 判断手机号码
 * @param {} data
 */
function phone(data) {
  var mobile_mode = /^1[34578]\d{9}$/
  if (!mobile_mode.test(data)) {
    return true
  }
}

/**
 * 设置storage
 * @param {*} key
 * @param {*} value
 */
function setStorage(key, value) {
  storage.set({
    key,
    value: value,
    fail(data, code) {
      LOG(`setStorage fail, code = ${code}`)
    }
  })
}
/**
 * 清空所有的缓存
 * @returns
 */
function clearStorage() {
  return new Promise((resolve, reject) => {
    storage.clear({
      success: function (data) {
        resolve()
      },
      fail: function (data, code) {
        reject()
      }
    })
  })
}
/**
 * 获取storage
 * @param {*} key
 */
function getStorage(key) {
  return new Promise((resolve, reject) => {
    storage.get({
      key,
      success(data) {
        if (data) {
          resolve(data)
        } else {
          resolve('')
        }
      },
      fail(data, code) {
        LOG(`getStorage fail, code = ${code}`)
        reject({ data, code })
      }
    })
  })
}

/**
 * 删除缓存信息
 */
function deleteStorage(key) {
  storage.delete({
    key,
    success: function (data) {
      LOG('handling success')
    },
    fail: function (data, code) {
      LOG(`handling fail, code = ${code}`)
    }
  })
}

/**
 * route  push页面挑转
 */
function routetheUrl(url, params, clear = false) {
  router.push({
    uri: url,
    params: { ...params, clearRouter: clear }
  })
}
/**
 * route  Replace  页面挑转
 * @param {} url
 * @param {*} params
 */
function routeReplacetheUrl(url, params) {
  router.replace({
    uri: url,
    params: params
  })
  router.clear()
}
/**
 * route  判断当前要跳转的路由是否为当前页面
 * @param {} url
 * @param {*} params
 */
function routeCheckUrl(url) {
  let routerList = router.getPages()
  LOG(`判断当前要跳转的路由是否为当前页面routeCheckUrl==>`, routerList)
  if (routerList[routerList.length - 1].path == url) {
    return true
  }
  return false
}
/**
 * route  检测当前页面数是否大于1
 */
function routeCheckPages() {
  let routerList = router.getPages()
  if (routerList.length > 1) {
    return true
  }
  return false
}
/**
 * 返回上一级
 */
function goBack() {
  router.back()
}
/**
 * 返回指定页面
 */
function goBackTo(path) {
  router.back({ path })
}
function clear() {
  router.clear()
}

/**
 *  //判断页面是否在栈内存里面存在，如果存在直接goback，否则跳转打开新路由
 */

function stacksRouter(path) {
  let routerList = []
  var stacks = router.getPages()
  stacks.forEach(item => {
    routerList.push(item.path)
  })
  //从数组中找到指定页面，没有则返回-1
  if (routerList.indexOf(path) != -1) {
    return true
  }
  return false
}

/**
 * 消息通知点击上报
 */
function pushMessageInfo(data) {
  $apis.example
    .pushMessage({
      pushId: data
    })
    .then(res => {})
    .catch(err => {})
}

/**
 * 手机返回键延迟2s
 * @param {number} startTimestamp  开始时间
 */
function backTime(startTimestamp, _this) {
  return new Date().getTime() - startTimestamp < _this.$app.$def.backTime
}

/**
 * 手机返回键延迟2s
 * @param {number} startTimestamp  开始时间
 * @param {Number} times  间隔差
 */
function backTimeCommon(startTimestamp, times) {
  return new Date().getTime() - startTimestamp < times
}

// 格式化倒计时 s->HH:mm:ss
function formatTime(time) {
  let hours = Math.floor(time / 3600)
  let minute = Math.floor(Math.floor(time % 3600) / 60)
  let second = time % 60
  hours = hours.toString().length === 1 ? `0${hours}` : hours
  minute = minute.toString().length === 1 ? `0${minute}` : minute
  second = second.toString().length === 1 ? `0${second}` : second
  return hours + ':' + minute + ':' + second
}

function dateToString() {
  let format = ''
  let nowDate = new Date()
  let Y = ''
  let M = ''
  let D = ''
  Y = `${nowDate.getFullYear()}`
  M = `0${nowDate.getMonth() + 1}`.slice(-2)
  D = `0${nowDate.getDate()}`.slice(-2)
  format = `${Y}年${M}月${D}日`
  return format
}
/*
 * 截取指定字节长度的字符串
 * 注：半角长度为1，全角长度为2
 * str:字符串
 * len:截取长度
 * return: 截取后的字符串及是否截取的标记（扩展用）code=1 字符串截断   code=0  字符串未截断
 */
function cutStrByte(str, len) {
  //校验参数
  if (!str || !len) {
    return { cutStr: '', code: 0 }
  }
  var code = '1', // 默认返回code值，已截断
    strLen = str.length, // 原字符串长度
    cutStr
  //如果字符串长度小于截取长度的一半,则返回全部字符串
  if (strLen <= len / 2) {
    cutStr = str
    code = '0'
  } else {
    //遍历字符串
    var strByteCount = 0
    for (var i = 0; i < strLen; i++) {
      //中文字符字节加2  否则加1
      strByteCount += getByteLen(str.charAt(i))
      //i从0开始 截断时大于len 只截断到第i个
      if (strByteCount > len) {
        cutStr = str.substring(0, i)
        break
      } else if (strByteCount == len) {
        cutStr = str.substring(0, i + 1)
        break
      }
    }
  }
  //cutstr为空，没有截断字符串
  if (!cutStr) {
    cutStr = str
    code = '0'
  }
  return { cutStr: cutStr, code: code }
}

/**
 * 获取字节长度，全角字符两个单位长度，半角字符1个单位长度
 */
function getByteLen(val) {
  var len = 0
  if (!val) {
    return len
  }
  for (var i = 0; i < val.length; i++) {
    if (!val[i]) {
      continue
    }
    // 全角
    if (val[i].match(/[^\x00-\xff]/gi) != null) {
      len += 2
    } else {
      len += 1
    }
  }
  return len
}

/**
 * 判断时间捡个是否大于30分钟
 * @param {number} startTimestamp  开始时间
 * @param {Number} times  间隔差
 */
function adFreeTimeDifference(startTime) {
  let curTime = Date.parse(new Date())
  let minute = Math.floor((curTime - startTime) / 60000)
  LOG(`距上次看视频免广告时间间隔======>${minute}`)
  return minute < 30 ? true : false
}

/**
 * 计算当前时间戳和传入时间戳的差值
 * @param {Number} timestamp 传入onShow记录的时间戳
 * @return 单位 s
 */
function pageTimeCompute(timestamp) {
  let seconds = 0
  let cruTiemstamp = new Date().getTime()
  if (cruTiemstamp > timestamp) {
    seconds = Math.ceil((cruTiemstamp - timestamp) / 1000)
  }
  return seconds
}

/**
 * 截取字符串
 * @param {String} str 截取的字符串
 * @param {Number} n 截取的长度
 */
function cutStr(str, n) {
  var strArr = []
  for (var i = 0, l = str.length; i < l / n; i++) {
    var a = str.slice(n * i, n * (i + 1))
    strArr.push(a)
  }
  return strArr
}

/**
 * 检测指定value 是否满足相应条件
 * @param {Number} curVal 设置的值
 * @param {Number} defalutVal 默认的值
 * @param {String} cond 条件 is 等于   not_is 不等于  less 小于 greater 大于
 * @returns {Boolean} res
 */
function checkKeyValueConditionSatisfied(curVal, defalutVal, cond) {
  if (cond != 'is' && cond != 'not_is' && cond != 'less' && cond != 'greater') {
    console.error('function checkKeyValueConditionSatisfied’s param(cond) not included is or not_is or less or greater')
    return false
  }
  let res = false
  switch (cond) {
    case 'is':
      res = curVal == defalutVal
      break
    case 'not_is':
      res = curVal != defalutVal
      break
    case 'less':
      res = curVal < defalutVal
      break
    case 'greater':
      res = curVal > defalutVal
      break
  }
  return typeof res == 'boolean' ? res : false
}
// 获取二级来源
function getSourceType() {
  LOG('getSourceType==========>', require('@system.app').getInfo())
  return require('@system.app').getInfo().source.type
}
// 获取三级来源
function getExtraScene() {
  let scene = require('@system.app').getInfo().source.extra.scene
  return scene ? scene : ''
}

function hexToRgb(val) {
  //HEX十六进制颜色值转换为RGB(A)颜色值
  // 16进制颜色值的正则
  var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
  // 把颜色值变成小写
  var color = val.toLowerCase()
  var result = ''
  if (reg.test(color)) {
    // 如果只有三位的值，需变成六位，如：#fff => #ffffff
    if (color.length === 4) {
      var colorNew = '#'
      for (var i = 1; i < 4; i += 1) {
        colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1))
      }
      color = colorNew
    }
    // 处理六位的颜色值，转为RGB
    var colorChange = []
    for (var i = 1; i < 7; i += 2) {
      colorChange.push(parseInt('0x' + color.slice(i, i + 2)))
    }
    result = 'rgb(' + colorChange.join(',') + ')'
    return { rgb: result, r: colorChange[0], g: colorChange[1], b: colorChange[2] }
  } else {
    result = '无效'
    return { rgb: result }
  }
}
/**
 * 计算剩余时长
 */
function countHour(battery, type) {
  if (battery && type) {
    return ((5000 * Number(battery)) / Number(type)).toFixed(1)
  }
}

/**
 * 将一维数组根据指定的长度拆分成二维数组
 * @param {Array} arr 原始数组
 * @param {Number} subGroupLength 需要拆分的长度
 * @returns 二维数组
 */
function array2group(arr, subGroupLength) {
  let index = 0
  let newArray = []
  while (index < arr.length) {
    newArray.push(arr.slice(index, (index += subGroupLength)))
  }
  return newArray
}

// 用于生成伪随机数的函数
function seededRandom(seed) {
  let x = Math.sin(seed) * 10000
  return x - Math.floor(x)
}

// 使用相同的种子来打乱数组
function shuffleArrayWithSeed(array, seed) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(seededRandom(seed) * (i + 1))
    ;[array[i], array[j]] = [array[j], array[i]]
  }
  return [...array]
}

/**
 * 按钮点击防抖
 * @param {String} event_name 组合事件名称
 * @param {Number} sleepTimes 休眠事件 单位 ms
 * @description 在元素点击时调用
 */
function dom_click_vali_shake(event_name, sleepTimes = 500) {
  if (!clickEventNameData[event_name]) {
    let _timestamp = new Date().getTime()
    clickEventNameData[event_name] = {
      timestamp: _timestamp,
      endTimestamp: _timestamp + sleepTimes
    }
    return true
  } else {
    let _timestamp = new Date().getTime()
    if (_timestamp <= clickEventNameData[event_name].endTimestamp) {
      return false
    } else {
      let _curData = {
        timestamp: _timestamp,
        endTimestamp: _timestamp + sleepTimes
      }
      clickEventNameData[event_name] = _curData
      return true
    }
  }
}

function filterBookList(list, publishIds) {
  const result = list.filter(item => publishIds.includes(String(item.id)))
  return result
}


/**
 * 设置剪切板内容
 * @param {*} text
 */
function setClipboard(text) {
  return new Promise(resolve => {
    clipboard.set({
      text: text,
      success: function (data) {
        console.log(`setClipboard success, data = ${data}`)
        resolve(true)
      },
      fail: function (data, code) {
        console.log(`setClipboard fail, code = ${code}`)
        resolve(false)
      }
    })
  })
}

/**
 * 修改数组顺序
 */
function shuffleArray(array) {
  // 创建一个数组的副本，以免修改原数组
  const shuffledArray = array.slice()

  for (let i = shuffledArray.length - 1; i > 0; i--) {
    // 随机选择一个索引
    const randomIndex = Math.floor(Math.random() * (i + 1))
    // 交换当前位置的元素与随机选择的元素
    ;[shuffledArray[i], shuffledArray[randomIndex]] = [shuffledArray[randomIndex], shuffledArray[i]]
  }
  return shuffledArray
}
export default {
  hexToRgb,
  showToast,
  queryString,
  phone,
  setStorage,
  getStorage,
  routetheUrl,
  routeReplacetheUrl,
  goBack,
  goBackTo,
  clear,
  deleteStorage,
  pushMessageInfo,
  backTime,
  backTimeCommon,
  routeCheckUrl,
  formatTime,
  routeCheckPages,
  clearStorage,
  showDialog,
  cutStrByte,
  getByteLen,
  adFreeTimeDifference,
  pageTimeCompute,
  cutStr,
  stacksRouter,
  checkKeyValueConditionSatisfied,
  getSourceType,
  getExtraScene,
  countHour,
  array2group,
  shuffleArrayWithSeed,
  dom_click_vali_shake,
  filterBookList,
  setClipboard,
  shuffleArray,
  setShelfList(list, storage = false) {
    hook2global.$shelfList = list
    if (storage) {
      setStorage('bookshelf', JSON.stringify(list))
    }
  },
  setCollectList(list, storage = false) {
    hook2global.$collectList = list
    if (storage) {
      setStorage('collect', JSON.stringify(list))
    }
  },
  async sleep(delay) {
    let timer
    return new Promise(resolve => {
      timer = setTimeout(() => {
        clearTimeout(timer)
        resolve()
      }, delay)
    })
  }
}
