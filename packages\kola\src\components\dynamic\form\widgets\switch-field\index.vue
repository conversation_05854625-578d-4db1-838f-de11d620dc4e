<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
  >
    <a-switch v-model="value" :checked-value="checkedValue" :unchecked-value="unCheckedValue" />
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject } from 'vue';
  import { SwitchField } from '../../../types/form';

  const props = defineProps<{
    field: SwitchField;
    path: string;
  }>();

  const checkedValue = computed(() => {
    return props.field.checkedValue ?? 1;
  });
  const unCheckedValue = computed(() => {
    return props.field.uncheckedValue ?? 0;
  });

  const formData = inject('formData');

  const value = defineModel<string | number | boolean>({
    default: '',
    set(val) {
      props.field.onChange?.(val, formData);
      if (props.field.setter) {
        return props.field.setter(val);
      }
      return val;
    },
  });
</script>
