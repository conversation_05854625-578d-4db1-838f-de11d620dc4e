const battery = {
  getStatus: (options) => {
    const { success } = options || {};
    
    // 固定返回75作为电量百分比
    const mockBatteryLevel = 75;
    
    // 固定返回充电状态为false（未充电）
    const mockIsCharging = false;
    
    setTimeout(() => {
      if (typeof success === 'function') {
        success({
          level: mockBatteryLevel, // 电池电量百分比 (0-100)
          isCharging: mockIsCharging // 是否正在充电
        });
      }
    }, 300);
  }
}
module.exports = battery