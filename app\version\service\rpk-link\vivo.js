const cryptoJS = require('crypto-js');
const axios = require('axios');
const AppVersion = require('../../model/version');
const { saveRpk } = require('../unzip');

function createSignParams(appName) {
  const params = {
    rpkPackage: appName,
    timestamp: Date.now(),
  };
  const keyS = new URLSearchParams(params); // 使用 URLSearchParams 简化参数拼接
  const crySign = cryptoJS.HmacSHA256(keyS.toString(), 'f3fc96e7021311eaa2b30235d2b38928dfgbea').toString();
  keyS.append('sign', crySign); // 转换为字符串
  return keyS;
}

/**
 * 获取应用详情
 * @param {string} appName - 应用名称
 * @returns {Promise<Object>} 应用详情
 */
const getAppDetails = async (appName) => {
  const url = `https://userapi.quickapp.cn/api/p/titlebar/queryRpkInfo`;
  const params = createSignParams(appName);
  try {
    const res = await axios.post(url, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'user-agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      },
    });
    if (res.data.code === 0) {
      return res.data.data;
    }
    return null;
  } catch (error) {
    console.log(error);
    return null;
  }
};



function createVivoLink(apps) {
  return Promise.all(
    apps.map(async (app) => {
      const appDetails = await getAppDetails(app.packageName);
      if (!appDetails) return;

      const matchedApp = apps.find((item) => item.packageName === appDetails.rpkPackage);
      if (!matchedApp) return;

      // 判断数据库是否已有此版本
      const existingVersion = await AppVersion.findOne({
        name: app.name,
        version: appDetails.versionName,
        brand: 'vivo',
      });
      if (existingVersion) return;
      // 没有再获取下载链接，入库
      const { rpkUrl, versionName } = appDetails;
      if (!versionName) return;
      // 创建新版本
      setTimeout(() => {
        saveRpk(rpkUrl, `${app.name}-${versionName}-vivo`);
      }, 1);
      // eslint-disable-next-line consistent-return
      return AppVersion.create({
        name: app.name,
        version: versionName,
        appLink: rpkUrl,
        brand: 'vivo',
      });
    })
  );
}

module.exports = {
  createVivoLink,
  getAppDetails,
};
