const path = require('path');
const { execSync } = require('child_process');


function getAppInfo(req, response) {
  console.log('getAppInfo');
  const appInfo = [];
  const targetDir = path.join(__dirname, '../../build', 'ad-interface');
  execSync('git pull', { cwd: targetDir, stdio: 'inherit' });
  const pkgInfoPath = path.join(targetDir, './bin/config/pkg-info.js')
  const pkgInfo = require(pkgInfoPath)
  delete require.cache[require.resolve(pkgInfoPath)]; // 清除缓存
  for (const category in pkgInfo) {
    const subCategory = pkgInfo[category];
    for (const key in subCategory) {
      appInfo.push({key: subCategory[key].pkg, pkgName: subCategory[key].name,company: subCategory[key].company})
    }
  }

  response.send({
    status: true,
    data: appInfo,
    ip: req.ip,
    timeStamp: Date.now(),
  });
}

module.exports = {
getAppInfo,
};
