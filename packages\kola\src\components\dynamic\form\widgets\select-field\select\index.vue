<template>
  <div class="container" ref="selectRef">
    <a-select
      v-model="value"
      :placeholder="field.placeholder || `请选择${field.label}`"
      :allow-search="field.select?.allowSearch"
      :allow-clear="field.select?.allowClear"
      :allow-create="field.select?.allowCreate"
      :limit="field.select?.limit"
      :max-tag-count="field.select?.maxTagCount"
      :style="field.style || field.select?.style"
      :loading="selectCtx.loading"
      :filter-option="field.select?.filterOption"
      :value-key="field.select?.valueKey ?? 'value'"
      :multiple="field.format === 'multipleSelect'"
      @popup-visible-change="selectCtx.onPopupVisibleChange"
      @change="selectChange"
      @search="handleSearch"
      @clear="handleClear"
      @remove="handleRemove"
      @exceed-limit="handleExceedLimit"
      :format-label="field.select?.formatLabel"
      :show-extra-options="field.select?.showExtraOptions"
      :virtual-list-props="virtualListProps"
      :options="sourceDataRef"
      :trigger-props="field.select?.updateAtScroll ? { updateAtScroll: true } : {}"
      @update:input-value="handleInputValue"
    >
      <template #option="{ data }">
        <a-tooltip :content="data.label" position="tl" v-if="field.select?.tooltip">
          <span>{{ data.label }}</span>
        </a-tooltip>
        <template v-else>
          <component :is="data.render" v-if="isVNode(data.render)" />
          <span v-else> {{ data.label }}</span>
        </template>
      </template>
      <template #header>
        <div style="padding: 6px 12px" v-if="showAll">
          <a-checkbox
            :model-value="selectCtx.checkedAll"
            :indeterminate="selectCtx.indeterminate"
            @change="selectCtx.handleChangeAll"
          >
            {{ showAllText ?? '全选' }}
          </a-checkbox>
        </div>
      </template>
      <template #search-icon></template>
    </a-select>
    <Tail v-if="field.select?.tail" :tail="field.select?.tail" />
  </div>
</template>

<script setup lang="ts">
  import { computed, inject, ref, Ref, isVNode, onMounted, nextTick, onBeforeUnmount } from 'vue';
  import { debounce } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import { FormData, Option, SelectField } from '../../../../types/form';
  import { SelectContext, selectInjectionKey } from '../context';
  import Tail from './tail.vue';

  const props = defineProps<{
    sourceData: Option[];
    valueKey: string;
    labelKey: string;
    format: string;
    showAll: boolean;
    showAllText?: string;
    field: SelectField;
  }>();
  const selectRef = ref();
  const selectCtx = inject<Partial<SelectContext>>(selectInjectionKey, {});
  const formData = inject<Ref<FormData>>('formData');
  const value = defineModel<any>();
  const searchText = ref<string>('');
  // searchText 是搜索回调的值，目前用于远程搜索，目前已知，在失去焦点时不清除，可能与组件内部 searchText 不一致
  const inputText = ref<string>('');
  // input text 和组件内部的 searchText 完全一致， 目前用于全选时候做筛选
  const selectChange = (selectValue: any) => {
    searchText.value = '';
    selectCtx.handleChange?.(selectValue);
  };
  const debouncedSearch = debounce(async (inputValue) => {
    if (inputValue === searchText.value) {
      return;
    }
    searchText.value = inputValue;
    selectCtx.updateSearchText?.(inputValue);
  }, 300);
  const virtualListProps = props.field.select?.openVirtual ? { threshold: 30 } : undefined;
  const sourceDataRef = computed(() => {
    try {
      return props.sourceData.map((item) => {
        return {
          ...item,
          label: item[props.labelKey],
          value: item[props.valueKey],
        };
      });
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log('e', e);
      // eslint-disable-next-line no-console
      console.log('name', props.field.name);
      return [];
    }
  });
  const handleSearch = (inputValue) => {
    if (props.field.select?.isAsyncSearch) {
      debouncedSearch(inputValue);
    }
  };

  const handleExceedLimit = () => {
    const limit = props.field.select?.limit;
    if (limit) {
      Message.error(`最多选择${limit}个`);
    }
  };
  const handleClear = () => {
    searchText.value = '';
    props.field.select?.onClear?.({
      value: value.value,
      formData,
    });
  };
  const handleRemove = (removed) => {
    props.field.select?.onRemove?.({
      value: value.value,
      formData,
      removed,
    });
  };

  const maxInputLength = props.field.select?.maxInputLength;

  function inputLen(type?: string) {
    const inputNode = selectRef.value?.querySelector('.arco-select-view-input');
    type === 'set' ? inputNode?.setAttribute('maxLength', maxInputLength) : inputNode?.removeAttribute('maxLength');
  }
  onMounted(() => {
    if (maxInputLength) {
      inputLen('set');
    }
  });
  onBeforeUnmount(() => {
    if (maxInputLength) {
      inputLen();
    }
  });
  function handleInputValue(inputVal) {
    inputText.value = inputVal;
  }

  defineExpose({
    getInputText: () => inputText.value,
  });
</script>

<style scoped lang="less">
  .container {
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
  }
</style>
