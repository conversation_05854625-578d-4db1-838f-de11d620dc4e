<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="field.rules"
    :hide-label="field.hideLabel"
    :disabled="field.disabled"
    :style="field.style"
  >
    <div>
      <SearchBar
        :all-option-map="optionMap"
        :placeholder="field.placeholder"
        :is-search-value="!!props.field.isShowValue"
      ></SearchBar>
      <div class="cascader-container">
        <div class="cascader-content">
          <card-item
            v-for="(item, index) in getItems"
            :card-info="getItems[index]"
            :key="item.title"
            :option="optionInfos"
            :level="index"
            :selected-path="selectedPath"
            :modal-config="item.modalConfig"
            :props-field="field"
            :get-items-length="getItems.length"
          ></card-item>
        </div>
        <div class="cascader-result">
          <div class="result-title">
            已选
            <a-button size="mini" type="text" style="padding: 0" @click="handleClear">清空 </a-button>
          </div>
          <SelectedCard :field="field" />
        </div>
      </div>
    </div>
  </a-form-item>
</template>

<script setup lang="ts">
  import { inject, ref, reactive, computed, watch, provide, ComputedRef, Ref, watchEffect, unref } from 'vue';
  import { useFormItem } from '@arco-design/web-vue';
  import { isFunction } from 'lodash';
  import { CascaderCardField, FormData } from '../../../types/form';
  import CardItem from './card.vue';
  import SelectedCard from './selected-card.vue';
  import SearchBar from './search-bar.vue';
  import type { CascaderOptionInfo, CascaderOption } from './interface';
  import {
    getOptionInfos,
    getValueKey,
    getLeafOptionKeys,
    getLeafOptionInfos,
    getFirstLevelParent,
    convertTreeData,
    valueToTree,
    getInitValueSelectPath,
  } from './utils';
  import { useSelectedPath } from './hooks/use-path';
  import { cascaderInjectionKey } from './context';

  const { eventHandlers } = useFormItem();

  const props = defineProps<{
    field: CascaderCardField;
    path: string;
  }>();

  const valueStruct = props.field.valueStruct || 'array';
  const formData = inject<Ref<FormData>>('formData');
  const value = defineModel<any>({
    default: '',
    set(val) {
      if (props.field.onChange) {
        setTimeout(() => {
          props.field.onChange?.(val, formData);
        }, 0);
      }
      return val;
    },
  });

  const optionInfos = ref<CascaderOptionInfo[]>([]);
  const totalLevel = ref(1);

  const optionMap = reactive(new Map<string, CascaderOptionInfo>());
  const leafOptionMap = reactive(new Map<string, CascaderOptionInfo>());
  const leafOptionValueMap = reactive(new Map<string, string>());
  const leafOptionSet = reactive(new Set<CascaderOptionInfo>());
  const valueKey = ref('value');
  const checkStrictly = ref(false);
  const loading = ref(false);
  const loadingLevel = ref(0);

  const lazyLoadOptions = reactive<Record<string, CascaderOption[]>>(defaultLazyLoadOptions());
  const customOptions = reactive<Record<string, CascaderOption[]>>({});

  const addLazyLoadOptions = (children: CascaderOption[], key: string) => {
    lazyLoadOptions[key] = children;
  };

  const addCustomOptions = (children: CascaderOption[], key: string) => {
    customOptions[key] = children;
  };

  function defaultLazyLoadOptions() {
    if (valueStruct === 'tree') {
      return value.value.reduce?.((pre, cur) => {
        pre[cur.value] = cur.children;
        return pre;
      }, {});
    }

    return {};
  }

  const DEFAULT_FIELD_NAMES = {
    value: 'value',
    label: 'label',
    disabled: 'disabled',
    children: 'children',
    tagProps: 'tagProps',
    render: 'render',
    isLeaf: 'isLeaf',
  };

  const computedValueMap: ComputedRef<Map<string, any>> = computed(() => {
    const values = valueStruct === 'tree' ? convertTreeData(value.value) : value.value || [];

    return new Map(
      values.map((v) => [
        getValueKey(v, {
          valueKey: 'value',
          leafOptionValueMap,
        }),
        v,
      ])
    );
  });

  const getItems = computed(() => {
    return isFunction(props.field.items) ? props.field.items(formData.value, props.path) : props.field.items || [];
  });

  const mergedFieldNames = computed(() => ({
    ...DEFAULT_FIELD_NAMES,
  }));
  const sourceData = ref<CascaderOption[]>([]);
  watch(
    [sourceData, lazyLoadOptions, mergedFieldNames, customOptions, getItems],
    ([_options, _lazyLoadOptions, _fieldNames, _customOptions]) => {
      optionMap.clear();
      leafOptionMap.clear();
      leafOptionValueMap.clear();
      leafOptionSet.clear();
      optionInfos.value = getOptionInfos(unref(_options) ?? [], {
        enabledLazyLoad: !!props.field.loadMore,
        lazyLoadOptions,
        optionMap,
        leafOptionSet,
        leafOptionMap,
        leafOptionValueMap,
        totalLevel,
        checkStrictly,
        valueKey,
        fieldNames: _fieldNames,
        items: getItems.value,
        customOptions,
      });
      // console.log('optionMap', optionMap);
      // console.log('leafOptionMap', leafOptionMap);
      // console.log('optionInfos', optionInfos);
    },
    {
      immediate: true,
      deep: true,
    }
  );

  async function getSourceData() {
    if (isFunction(props.field.option)) {
      loadingLevel.value = 0;
      loading.value = true;
      sourceData.value = await props.field.option(props.field, formData?.value as any, props.path);
      loading.value = false;
    } else {
      sourceData.value = props.field.option || [];
    }
  }

  const filteredLeafOptions = computed(() => {
    return Array.from(leafOptionSet);
  });

  const showSearchPanel = computed(() => {
    return false;
  });

  const expandChild = computed(() => {
    return false;
  });

  const updateValue = (values: any[] | any[][]) => {
    if (values.length === 0) {
      // setSelectedPath();
      // setActiveKey();
    }
    value.value =
      valueStruct === 'tree' ? valueToTree(values as string[], optionMap, props.field?.getMapField) : values;
    /* emit('update:modelValue', value);
emit('change', value); */
    eventHandlers.value?.onChange?.();
  };

  const handleClickOption = (option: CascaderOptionInfo, checked?: boolean) => {
    if (option.isMultiple) {
      selectMultiple(option, !!checked);
      return;
    }
    selectSingle(option, !!checked);
  };

  const selectSingle = (options: CascaderOptionInfo, checked: boolean) => {
    if (checked) {
      const leafOptionKeys = getLeafOptionKeys(options);
      updateValue([...leafOptionKeys]);
    } else {
      updateValue([]);
    }
  };

  const selectMultiple = (option: CascaderOptionInfo, checked: boolean) => {
    if (checked) {
      const leafOptionInfos = getLeafOptionInfos(option);
      const firstLevelNode = getFirstLevelParent(option);

      // 第一层是radio，需要过滤兄弟节点
      if (!firstLevelNode.isMultiple) {
        const leafOptionKeys = getLeafOptionKeys(firstLevelNode);
        const values: any[] = [];
        computedValueMap.value.forEach((v, key) => {
          if (leafOptionKeys.includes(key)) {
            values.push(v);
          }
        });
        updateValue([
          ...values,
          ...leafOptionInfos
            .filter((item) => !computedValueMap.value.has(item.key))
            .map((item) => {
              return item.key;
            }),
        ]);
        return;
      }

      // 第一层是checkbox，直接添加
      updateValue([
        ...computedValueMap.value.values(),
        ...leafOptionInfos
          .filter((item) => !computedValueMap.value.has(item.key))
          .map((item) => {
            return item.key;
          }),
      ]);
    } else {
      const leafOptionKeys = getLeafOptionKeys(option);
      const values: any[] = [];
      computedValueMap.value.forEach((v, key) => {
        if (!leafOptionKeys.includes(key)) {
          values.push(v);
        }
      });
      updateValue(values);
    }
  };

  function reverseSelect(options: CascaderOptionInfo[] | undefined) {
    if (!options) {
      return;
    }
    const curOption = options[0];
    const curOptionsKeys = options.map((v) => v.key);
    const parent = curOption.parent as CascaderOptionInfo;

    const leafOptionKeys = getLeafOptionKeys(parent);
    const newValues: string[] = leafOptionKeys.filter((v) => !options.find((item) => item.key === v));
    const values: any[] = [];
    computedValueMap.value.forEach((v, key) => {
      if (!curOptionsKeys.includes(key)) {
        values.push(v);
      }
    });

    updateValue([...values, ...newValues]);
  }
  function selectAll(options: CascaderOptionInfo[] | undefined, isSelectAll: boolean) {
    if (!options) {
      return;
    }

    const curOptionsKeys = options.map((v) => v.key); // 当前选项的所有Key数组

    if (isSelectAll) {
      const values: any[] = [];

      computedValueMap.value.forEach((v, key) => {
        values.push(v);
      });

      updateValue([...values, ...curOptionsKeys]);
    } else {
      curOptionsKeys.forEach((key) => {
        computedValueMap.value.delete(key);
      });

      const values: any[] = [];

      computedValueMap.value.forEach((v, key) => {
        if (curOptionsKeys.indexOf(key) === -1) {
          values.push(v);
        }
      });
      updateValue([...values]);
    }
  }

  const handlePathChange = (item: CascaderOptionInfo) => {
    if (isFunction(props.field.loadMore) && !item.isLeaf) {
      const { children, key } = item;

      if (!children) {
        loadingLevel.value = item.level + 1;
        loading.value = true;
      }

      props.field
        .loadMore?.(item.raw)
        .then((v?: any) => {
          loading.value = false;
          if (v) {
            addLazyLoadOptions?.(v, key);
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
    setSelectedPath?.(item.key);
  };

  const {
    activeKey,
    activeOption,
    selectedPath,
    displayColumns,
    setActiveKey,
    setSelectedPath,
    getNextActiveNode,
    getNextPanel,
  } = useSelectedPath(optionInfos, {
    optionMap,
    filteredLeafOptions,
    showSearchPanel,
    expandChild,
  });

  // 设置一个默认的选中值
  selectedPath.value = getInitValueSelectPath(value.value);

  provide(
    cascaderInjectionKey,
    reactive({
      onClickOption: handleClickOption,
      setActiveKey,
      setSelectedPath,
      addLazyLoadOptions,
      valueMap: computedValueMap,
      getNextPanel,
      optionMap,
      reverseSelect,
      loadMore: props.field.loadMore,
      loading,
      loadingLevel,
      handlePathChange,
      addCustomOptions,
      selectAll,
    })
  );

  const handleClear = () => {
    updateValue([]);
  };

  watchEffect(() => {
    getSourceData();
  });
</script>

<style scoped lang="less">
  @import './index.less';
</style>
