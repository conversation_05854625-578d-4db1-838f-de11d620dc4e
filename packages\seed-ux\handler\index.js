const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const tsTemplate = `import type { App } from 'vue';
import _Page from './index.vue';

const DynamicPage = Object.assign(_Page, {
  install: (app: App) => {
    app.component('S__component__', _Page);
  },
});

export type PageInstance = InstanceType<typeof _Page>;

export default DynamicPage;
`;

function mainHandler(filePath) {
  const componentName = filePath.split('/').pop();
  const inputFilePath = path.join(__dirname, '../', filePath);
  const outputFilePath = path.join(
    __dirname,
    '../../seed/src/seed-comp/',
    componentName
  );
  copyMeta(inputFilePath, outputFilePath);
  copyTs(outputFilePath, componentName);
  // registerComponent(componentName);
  convertUx(inputFilePath, outputFilePath);
}

function copyMeta(inputFilePath, outputFilePath) {
  console.log('正在复制meta.json')
  const metaPath = path.join(inputFilePath, 'meta.json');
  if (!fs.existsSync(metaPath)) {
    console.error(`File not found: ${metaPath}`);
    process.exit(1);
  }
  if (!fs.existsSync(outputFilePath)) {
    fs.mkdirSync(outputFilePath, { recursive: true });  // 这里才需要 recursive
  }
  fs.copyFileSync(metaPath, path.join(outputFilePath, 'meta.json'));
  console.log(`meta.json 复制成功`);
}


function copyTs(outputFilePath, componentName) {
  console.log('正在复制index.ts')
  const tsContent = tsTemplate.replace(/__component__/g, componentName);
  const tsFilePath = path.join(outputFilePath, 'index.ts');
  fs.writeFileSync(tsFilePath, tsContent);
  console.log('index.ts 复制成功')
}

function convertUx(inputFilePath, outputFilePath) {
  console.log('正在转换ux文件')
  const binPath = path.join(__dirname, '../../ux-vue');
  execSync(`npx tt -i ${inputFilePath} -o ${outputFilePath}`,{cwd:binPath})
  console.log('ux文件转换成功')
}

module.exports = mainHandler;
