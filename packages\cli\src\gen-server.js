// 生成控制器文件



function generateControllerFile(modelName, fields) {
  // 生成控制器文件
  const controllerCode = `const ${modelName} = require('../model/${modelName}.js');

// 创建${modelName}
const create${modelName} = async (req, res) => {
  try {
    const data = req.body;
    const newRecord = new ${modelName}(data);
    await newRecord.save();
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: newRecord
    });
  } catch (error) {
    res.status(400).json({ 
      code: 1,
      msg: error.message,
      data: null
    });
  }
};

// 获取${modelName}列表（支持分页和搜索）
const getAll${modelName}s = async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, ...filters } = req.query;
    const query = {};

    // 动态添加过滤条件
    Object.keys(filters).forEach((key) => {
      if (filters[key]) {
        query[key] = { $regex: filters[key], $options: 'i' };
      }
    });

    // 计算分页参数
    const skip = (pageNum - 1) * pageSize;

    // 查询总数
    const total = await ${modelName}.countDocuments(query);

    // 查询分页数据
    const records = await ${modelName}.find(query).skip(skip).limit(Number(pageSize)).sort({ createdAt: -1 });

    res.status(200).json({
      code: 0,
      msg: 'success',
      data: {
        total: total,
        list: records
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null
    });
  }
};
// 获取单个${modelName}
const get${modelName}ById = async (req, res) => {
  try {
    const record = await ${modelName}.findById(req.params.id);
    if (!record) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null
      });
    }
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: record
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null
    });
  }
};

// 更新${modelName}
const update${modelName} = async (req, res) => {
  try {
    const updatedRecord = await ${modelName}.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );
    if (!updatedRecord) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null
      });
    }
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: updatedRecord
    });
  } catch (error) {
    res.status(400).json({
      code: 1,
      msg: error.message,
      data: null
    });
  }
};

// 删除${modelName}
const delete${modelName} = async (req, res) => {
  try {
    const deletedRecord = await ${modelName}.findByIdAndDelete(req.params.id);
    if (!deletedRecord) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null
      });
    }
    res.status(200).json({
      code: 0,
      msg: '删除成功',
      data: null
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null
    });
  }
};

module.exports = {
  create${modelName},
  getAll${modelName}s,
  get${modelName}ById,
  update${modelName},
  delete${modelName},
};
`;

  return controllerCode;
}

// 生成路由文件
function generateRouterFile(modelName) {
  const routerCode = `const express = require('express');
const router = express.Router();
const ${modelName}Controller = require('../controller/${modelName}');

// ${modelName}相关路由
router.post('/${modelName}s', ${modelName}Controller.create${modelName}); // 创建${modelName}
router.get('/${modelName}s', ${modelName}Controller.getAll${modelName}s); // 获取${modelName}列表
router.get('/${modelName}s/:id', ${modelName}Controller.get${modelName}ById); // 获取单个${modelName}
router.put('/${modelName}s/:id', ${modelName}Controller.update${modelName}); // 更新${modelName}
router.delete('/${modelName}s/:id', ${modelName}Controller.delete${modelName}); // 删除${modelName}

module.exports = router;
`;

  return routerCode;
}

module.exports = {
  generateControllerFile,
  generateRouterFile,
};
