const fs = require('fs');
const path = require('path');
const buildTask = require('../model/build-task');
const user = require('../model/user');
const Ntask = require('../service/nTask');
const wsController = require('./ws');
const { removeEmptyProperties } = require('../utils/tools');
async function buildTaskList(req, res) {
  const { pageNum = 1, pageSize = 10, ...filters } = req.query;

  const query = removeEmptyProperties(filters);
  if (query.appName) {
    query.appName = { $regex: filters.appName, $options: 'i' };
  }
  if (query.sdkVersion) {
    query.sdkVersion = { $regex: filters.sdkVersion, $options: 'i' };
  }

  if (query.username) {
    query.username = { $regex: filters.username, $options: 'i' };
  }
  
  const skip = (pageNum - 1) * pageSize;
  const records = await buildTask.find(query).skip(skip).limit(Number(pageSize)).sort({ createdAt: -1 });
  const total = (await buildTask.find(query)).length; //await buildTask.find(query).length;

  res.status(200).json({
    code: 0,
    msg: 'success',
    data: {
      total: total,
      list: records,
    },
  });

  // const result = await buildTask.find().sort({createdAt: -1});
  // res.send({ status: true, data: {list:result,total:result.length} });
  // res.send({status:true,data:{list:fakeList,total:fakeList.length}})
}

async function createBuildTask(req, res) {
  if (!req.body || !Array.isArray(req.body) || !req.body.length) {
    return res.status(400).json({
      status: false,
      result: { message: '请提供有效的打包参数列表' },
    });
  }
  const role = await user.findOne({ id: req.user.userId }); //获取用户角色
  try {
    const { taskId, resultList } = await Ntask.addTaskList(req.body, role);
    // Start task processing
    Ntask.taskQueue.forEach((task) => {
      if (task.taskId === taskId) {
        buildTask.create(task).then((user) => {
          console.log('user', user._id);
          task.id = user._id;
        });
      }
    });
    Ntask.startTask(callback);

    return res.status(200).json({
      status: true,
      result: { taskId, resultList },
    });
  } catch (error) {
    console.log('error', error);
    return res.status(200).json({
      status: false,
      result: { resultList: error },
    });
  }
}

async function downloadAPK(req, res) {
  const { taskId, key, fileName } = req.query;
  const filePath = path.join(__dirname, '../../dist', taskId, key, fileName);

  if (!fs.existsSync(filePath)) {
    res.status(404).send({
      status: false,
      message: 'File not found',
    });
    return;
  }
  res.download(filePath);
}

function delTask(req, res) {
  const { ids } = req.body;
  ids.forEach((id) => {
    let taskId = '';
    let key = '';
    buildTask
      .findOne({ _id: id })
      .then((result) => {
        taskId = result.taskId;
        key = result.key;
        if (!taskId || !key) {
          console.error('删除查找失败:', err);
          return;
        }
        buildTask
          .deleteOne({ taskId, key })
          .then((result) => {
            console.log('删除成功:', result); // 返回删除操作的结果
            fs.rmSync(path.join(__dirname, '../../dist', taskId, key), { recursive: true, force: true });
          })
          .catch((err) => {
            console.error('删除失败:', err);
          });
      })
      .catch((err) => {
        console.error('删除查找失败:', err);
      });

    buildTask
      .deleteOne({ _id: id })
      .then((result) => {
        console.log(result); // 返回删除操作的结果
      })
      .catch((err) => {
        console.error('删除失败:', err);
      });
  });
  // buildTask.deleteOne({ taskId });
  res.send({ status: true, message: '删除成功' });
}

function handleDelTask(ids) {}

// 打包结束回调
function callback(task) {
  console.log('打包结束通知前端task:', task);
  buildTask.updateOne({ taskId: task.taskId, key: task.key }, { $set: task }).then((result) => {
    console.log('result', result);
  });
  wsController.sendMessage({
    type: 'taskComplete',
    taskId: task.taskId,
    key: task.key,
    brand: task.brand,
    sdkVersion: task.sdkVersion,
    version: task.version,
    data: task.result,
    timeCost: task.timeCost,
    appName: task.appName,
    timeStamp: task.timeStamp,
  });
}

module.exports = {
  buildTaskList,
  downloadAPK,
  createBuildTask,
  delTask,
};
