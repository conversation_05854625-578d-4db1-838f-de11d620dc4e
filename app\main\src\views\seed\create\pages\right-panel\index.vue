<template>
  <div class="right-panel">
    <h3>配置区</h3>
    <!-- 页面配置 -->
    <div v-if="currentPage" class="config-section">
      <h4>页面配置</h4>
      <ConfigForm v-model="pageConfig" :schema="pageConfigSchema" />
    </div>

    <!-- 组件配置 -->
    <div v-if="selectedComponent && Object.keys(formProps)?.length">
      <!-- 属性配置 -->
      <div class="config-section">
        <h4>组件配置</h4>
        <ConfigForm v-model="formProps" :schema="propSchema" />
      </div>
    </div>
    <div
      v-if="!currentPage && !selectedComponent"
      style="color: #999; text-align: center; padding: 20px"
    >
      请选择页面或组件进行配置
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import ConfigForm from '../../components/config-form.vue';

  const props = defineProps({
    selectedComponent: Object,
    currentPage: {
      type: Object,
      default: () => null,
    },
    isMainPage: {
      type: Boolean,
      default: false,
    },
    pagesConfig: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits([
    'update:props',
    'update:styles',
    'update:pageConfig',
  ]);

  const propSchema = ref({});
  const formProps = ref({});
  const styleSchema = ref({});
  const formStyles = ref({});
  const localStyles = ref(props.selectedComponent?.styles || {});


  // 页面配置
  const pageConfig = ref({
    title: '',
    path: '',
    icon: '',
    backgroundColor: '#ffffff',
    themeColor: '#1890FF',
  });

  // 页面配置schema
  const pageConfigSchema = {
    path: { label: '页面路由', type: 'text'},
    icon: { label: '底部图标', type: 'text'},
    backgroundColor: { label: '背景颜色', type: 'colorPicker'},
  };

  watch(
    () => props.currentPage,
    (val) => {
      if (val) {
        pageConfig.value = {
          title: val.title || '',
          path: val.path || '',
          icon: val.icon || '',
          backgroundColor: val.backgroundColor || '#ffffff',
          themeColor: val.themeColor || '#1890FF',
        };
      }
    }
  );

  watch(
    pageConfig,
    (val) => {
      // 同步所有页面配置属性到 pagesConfig
      const newConfig = {
        pageName: val.title, // 将 title 同步到 pageName
        pageId: props.currentPage?.id,
        themeColor: val.themeColor,
        backgroundColor: val.backgroundColor,
        path: val.path,
        components: props.currentPage?.components || [],
      };
      emit('update:pageConfig', newConfig);
    },
    { deep: true }
  );



  watch(
    () => props.selectedComponent,
    (val) => {
      console.log('val', val);
      if (val && val.props) {
        propSchema.value = val.props;
        formProps.value = Object.fromEntries(
          Object.entries(val.props).map(([k, v]) => [k, v.value])
        );

        // 设置样式 schema
        styleSchema.value = val.styles || {};
        // 设置样式值
        formStyles.value = Object.fromEntries(
          Object.entries(val.styles || {}).map(([k, v]) => [k, v.value])
        );
        localStyles.value = val.styles || {};

        console.log('propSchema', propSchema.value);
        console.log('styleSchema', styleSchema.value);
        console.log('formStyles', formStyles.value);
      }
    },
    { immediate: true }
  );

  watch(
    formProps,
    (val) => {
      // 组装回完整结构（带 type/options等），只更新 value 字段
      const newProps = {};
      Object.keys(propSchema.value).forEach((key) => {
        newProps[key] = { ...propSchema.value[key], value: val[key] };
      });
      emit('update:props', newProps);
    },
    { deep: true }
  );

  watch(
    formStyles,
    (val) => {
      // 组装回完整结构（带 type/options等），只更新 value 字段
      const newStyles = {};
      Object.keys(styleSchema.value).forEach((key) => {
        newStyles[key] = { ...styleSchema.value[key], value: val[key] };
      });
      emit('update:styles', newStyles);
    },
    { deep: true }
  );
</script>

<style scoped>
  .right-panel {
    width: 540px;
    padding: 16px;
    background: #f7f8fa;
    height: calc(100vh - 60px);
    border-left: 1px solid #e5e6eb;
    overflow-y: auto;
  }

  .config-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
  }

  .config-section h4 {
    font-size: 16px;
    font-weight: 500;
    color: #222;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e6eb;
  }
</style>
