const { getAppDownloadHref, searchAppVersion } = require('../service/rpk-link/xiaomi');
const { getApkInfo } = require('../service/rpk-link/oppo');
const { getAppDetails } = require('../service/rpk-link/vivo');
const { getHonorLink } = require('../service/rpk-link/honor');
const { getHwLink } = require('../service/rpk-link/hw');

const xmProxy = async (req, res) => {
  const { packageName } = req.body;
  const data = await searchAppVersion(packageName, 'xiaomi', true);
  res.send({
    code: 0,
    msg: 'success',
    data,
  });
};

const xmDownload = async (req, res) => {
  const { packageName } = req.body;
  const data = await getAppDownloadHref(packageName);
  res.send({
    code: 0,
    msg: 'success',
    data,
  });
};

const oppoProxy = async (req, res) => {
  const { packageName } = req.body;
  const data = await getApkInfo(packageName, true);
  res.send({
    code: 0,
    msg: 'success',
    data,
  });
};

const vivoProxy = async (req, res) => {
  const { packageName } = req.body;
  const data = await getAppDetails(packageName);
  res.send({
    code: 0,
    msg: 'success',
    data,
  });
};

const honorProxy = async (req, res) => {
  const { packageName } = req.body;
  const data = await getHonorLink(packageName);
  res.send({
    code: 0,
    msg: 'success',
    data,
  });
};

const hwProxy = async (req, res) => {
  const { packageName } = req.body;
  const data = await getHwLink(packageName);
  res.send({
    code: 0,
    msg: 'success',
    data,
  });
};

module.exports = {
  xmProxy,
  xmDownload,
  oppoProxy,
  vivoProxy,
  honorProxy,
  hwProxy,
};
