<template>
  <div class="wrapper">
    <list class="content">
      <list-item type="5" style="margin-top: 30px;flex-direction: column;padding-left:30px;">
        <div>
          <div class="item-1" @click="homeClick(1, '电池降温')">
            <div style="flex-direction:column;">
              <text class="item-text-1">电池降温</text>
              <text class="item-text-2">一键告别手机发烫</text>
            </div>
            <image src="https://img.sytangshiwl.top/qujieneng/images/<EMAIL>"></image>
          </div>
          <div style="width: 1px;height: 189px;background-color: #ededed;"></div>
          <div class="item-2" @click="homeClick(2, '充电监测')">
            <div style="flex-direction:column;">
              <text class="item-text-1">充电监测</text>
              <text class="item-text-2">一键排除充电风险</text>
            </div>
            <image src="https://img.sytangshiwl.top/qujieneng/images/<EMAIL>"></image>
          </div>
        </div>
        <div style="width: 100%;height: 1px;background-color: #ededed;"></div>
        <div>
          <div class="item-3" @click="homeClick(3, '耗电排行')">
            <div style="flex-direction:column;">
              <text class="item-text-1">耗电排行</text>
              <text class="item-text-2">一键查询耗电应用</text>
            </div>
            <image src="https://img.sytangshiwl.top/qujieneng/images/<EMAIL>"></image>
          </div>
          <div style="width: 1px;height: 189px;background-color: #ededed;"></div>
          <div class="item-4" @click="homeClick(4, '密码保险箱')">
            <div style="flex-direction:column;">
              <text class="item-text-1">密码保险箱</text>
              <text class="item-text-2">帮助记忆加密存储</text>
            </div>
            <image src="https://img.sytangshiwl.top/qujieneng/images/<EMAIL>"></image>
          </div>
        </div>
      </list-item>
    </list>
  </div>
</template>

<script>
export default {
  homeClick(index, title) {
    
  }
}
</script>

<style lang="less">
.wrapper{
  
  .content{
    .item-1, .item-2, .item-3, .item-4 {
      width: 750px;
      height: 189px;
      background-color: #ffffff;
      align-items: center;
      padding-left: 30px;

      > image {
        margin-left: 15px;
      }

      .item-text-1 {
        font-size: 35px;
        font-weight: bold;
        color: #333333;
        height: 32px;
      }

      .item-text-2 {
        font-size: 26px;
        color: #999999;
        height: 26px;
        margin-top: 13px;
      }
    }

    .item-1 {
      border-top-left-radius: 20px;
    }

    .item-2 {
      border-top-right-radius: 20px;
    }

    .item-3 {
      border-bottom-left-radius: 20px;
    }

    .item-4 {
      border-bottom-right-radius: 20px;
    }
  }
}
</style>