<template>
  <div class="content-wrapper">
    <div class="tree">
      <a-tree
        :data="data"
        :default-selected-keys="['app.js']"
        @select="onSelect"
      ></a-tree>
    </div>
    <div class="content">
      <Editor :code="content" :diff-code="diffContent"></Editor>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import versionApi from '@/api/AppVersion';
  import qs from 'query-string';
  import Editor from '@/views/version/components/editor.vue';
  import axios from 'axios';

  // eslint-disable-next-line vue/valid-define-props
  const props = defineProps<{
    record: Record<any, any>;
  }>();

  const data = ref([]);
  const content = ref('');
  const diffContent = ref('');

  console.log(props);

  versionApi.fileList(props.record).then((res) => {
    console.log(res);
    data.value = res.list;
  });

  function fetchPreContent(params: string) {
    fetch(`/api/pre-file-content?${params}`)
      .then((res) => {
        return res.blob();
      })
      .then(async (blob) => {
        diffContent.value = await blob.text();
      })
      .catch((e) => {
        console.log(e);
      });
  }

  

  function onSelect(selectedKeys: string[]) {
    console.log(selectedKeys);

    const params = qs.stringify({ ...props.record, key: selectedKeys[0] });
    console.log('params', params);
    fetch(`/api/version-file-content?${params}`)
      .then((res) => {
        return res.blob();
      })
      .then(async (blob) => {
        console.log(blob);
        content.value = await blob.text();
      })
      .catch((e) => {
        console.log(e);
      });
    fetchPreContent(params);
  }
  
  onSelect(['app.js']);
</script>

<style lang="less">
  .content-wrapper {
    display: flex;

    .tree {
      flex: 0 0 300px;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 8px;
    }

    .content {
      flex: 1 1 800px;
      border-radius: 8px;
      margin-left: 16px;
      overflow: hidden;

      code {
        overflow: auto;
      }
    }
  }
</style>
