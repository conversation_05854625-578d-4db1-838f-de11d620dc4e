const WebSocket = require('ws');
const http = require('http');
class WsController {
  constructor() {
    this.ws = null;
  }
  initWs(app) {
    const server = http.createServer(app);
    this.ws = new WebSocket.Server({ server });
    this.ws.on('connection', (ws) => {
      console.log('Client connected');
      ws.on('message', (message) => {
        const receivedMessage = Buffer.isBuffer(message) ? message.toString() : message;
      });
    });
    server.listen(3002, () => {
      console.log('Server is running on http://localhost:3002');
    });
  }
  sendMessage(message) {
    try {
      this.ws.clients.forEach((client) => {
        client.send(JSON.stringify(message));
      });
    } catch (error) {
      console.error('WebSocket send message error', error);
    }
  }
}

module.exports = new WsController();