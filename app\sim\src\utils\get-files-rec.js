const fs = require('fs');
const path = require('path');

function getFilesRecursive(dirPath, ext) {
  const files = fs.readdirSync(dirPath);
  let fileList = [];

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // 如果是目录，递归调用
      fileList = fileList.concat(getFilesRecursive(fullPath, ext));
    } else {
      if (ext) {
        if (fullPath.endsWith(ext)) {
          fileList.push(fullPath);
        }
      } else {
        fileList.push(fullPath);
      }
    }
  });

  return fileList;
}

module.exports = getFilesRecursive
