const express = require('express');
const { getAppVersionFileList, getFileContent, getPreVersionContent } = require('../controller/copare');
const proxy = require('../controller/proxy');
const ai = require('../controller/ai');

const router = express.Router();
const appRouter = require('./CompetitorsApp.js');
const AppVersion = require('./AppVersion.js');
const user = require('./User.js');
const visit = require('./visit.js');

router.get('/version-file-list', getAppVersionFileList);
router.get('/version-file-content', getFileContent);
router.get('/pre-file-content', getPreVersionContent);
router.post('/proxy/xm', proxy.xmProxy);
router.post('/proxy/download/xm', proxy.xmDownload);
router.post('/proxy/oppo', proxy.oppoProxy);
router.post('/proxy/vivo', proxy.vivoProxy);
router.post('/proxy/honor', proxy.honorProxy);
router.post('/proxy/hw', proxy.hwProxy);
router.get('/ai/v2', ai.getAnswerV2);
router.get('/ai', ai.getAnswer);

// 版本相关路由
// 这里可以添加versionController的相关路由
router.use(appRouter);
router.use(AppVersion);
router.use(user);
router.use(visit);
// 导出带有统一前缀的路由
module.exports = router;
