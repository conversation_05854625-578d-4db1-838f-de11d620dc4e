import { http } from '@/utils/http';

const UserApi = {
  // 创建User
  create(data) {
    return http.post('/api/Users', data);
  },

  // 获取User列表
  list(params) {
    return http.get('/api/Users', { params });
  },

  // 获取单个User
  getById(id) {
    return http.get(`/api/Users/<USER>
  },

  // 更新User
  update(id, data) {
    return http.put(`/api/Users/<USER>
  },

  // 删除User
  delete(id) {
    return http.delete(`/api/Users/<USER>
  },
  getMenuList() {
    return ''
  }
};

export default UserApi;
