const fs = require('fs');
const path = require('path');
const { OUT_PUT_DIR } = require('../constant.js');

const { generatePage } = require("./generatePage.js")

function generateTabs(appConfig) {
    const { meta, pages } = appConfig;
    const { tabs: pagesBrief } = meta;
    pagesBrief.forEach(page => {
        const pageConfig = pages.find(p => p.meta.pageName === page.pageName);
        const pageDir = path.join(OUT_PUT_DIR, '/pages/Main')
        if (!fs.existsSync(pageDir)) { // 如果目录不存在，则创建目录
            fs.mkdirSync(pageDir, { recursive: true }); // 使用 { recursive: true } 选项创建多级目录
        }
        const pagePath = path.join(pageDir, `${page.pageName}.ux`)
        const pageContent = generatePage(pageConfig)
        fs.writeFileSync(pagePath, pageContent, 'utf-8')
    })
}

module.exports = { generateTabs }