import { reactive } from 'vue';
import { Pagination } from '../../types/table';

const usePagination = (isPageable, defaultPagination = {}) => {
  const pagination: any = isPageable
    ? reactive<Pagination>({
        current: 1,
        pageSize: 10,
        showPageSize: true,
        showJumper: true,
        showTotal: true,
        total: 0,
        pageSizeOptions: [10, 20, 30, 40, 50, 100, 200, 300, 400, 500],
        ...defaultPagination,
      })
    : {};
  return {
    pagination,
  };
};

export default usePagination;
