const { parentPort } = require('worker_threads');
const path = require('path');
const fs = require('fs');
let WORKSPACE = '';
let originBranch = 'master-robot';
let targetBranch = 'master';
const {
  cdPath,
  updateAdInterface,
  autoUpgrade,
  gitCommit,
  gitCheckout,
  gitPullOrigin,
  checkouNewBranch,
} = require('../utils/instruction');

const { createMergeRequest } = require('../utils/tools');
const sendMsgToDingTalk = require('../utils/send-dd-msg');

parentPort.on('message', (taskList, role) => {
  init(taskList[0].branch);
  upgrade(taskList, role);
  // 将结果发送回主线程
});

const init = (item) => {
  if (item === 'master') {
    WORKSPACE = path.join(__dirname, '../../build/qkproject-upgrade');
    originBranch = 'master-robot';
    targetBranch = 'master';
  } else {
    WORKSPACE = path.join(__dirname, '../../build/qkproject-upgrade-develop');
    originBranch = 'develop-robot';
    targetBranch = 'develop';
  }
};

const upgrade = async (taskList, role) => {
  try {
    cdPath(WORKSPACE);
    gitCheckout(WORKSPACE, targetBranch);
    checkouNewBranch(WORKSPACE,originBranch);
    // gitPullOrigin(WORKSPACE, originBranch);
    updateAdInterface(WORKSPACE);
    console.log('taskList,', taskList);
    let commitMessage = 'feat:';
    const appList = taskList.map((item) => {
      const { appName, brand, key, operationType } = item;
      return {
        appName: appName,
        brand,
        key,
        operationType,
      };
    });
    console.log('appList', appList);

    appList.forEach((element) => {
      commitMessage += element.appName + ',';
      autoUpgrade(WORKSPACE, element.brand, element.appName);
    });
    commitMessage += `升级版本号，共${appList.length}个应用`;
    gitCommit(WORKSPACE, commitMessage);

    const mrLink = await createMergeRequest(originBranch, targetBranch, commitMessage, commitMessage);
      if (mrLink) {
        sendMsgToDingTalk({ mrLink });
      }
  } catch (e) {
    console.log('upgrade error', e);
  }
};
