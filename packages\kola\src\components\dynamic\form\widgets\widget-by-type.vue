<template>
  <div v-if="(field as any).viewType  === 'card'" style="margin-bottom: 20px">
    <p class="title">{{ field.label }}</p>
    <component :is="resolveComponent(field.type)" :field="formattedField" :path="path" v-model="value" />
  </div>
  <component v-else :is="resolveComponent(field.type)" :field="formattedField" :path="path" v-model="value" />
</template>

<script setup lang="ts">
  import { computed, inject, Ref } from 'vue';
  import { isFunction } from 'lodash';
  import { Field, FormData } from '../../types/form';
  import resolveComponent from './resolve-component';

  const props = defineProps<{
    field: Field;
    path: string;
  }>();

  const formData = inject<Ref<FormData>>('formData');
  const formattedField = computed(() => {
    const originRules = isFunction(props.field.rules)
      ? props.field.rules(props.field, formData?.value as any, props.path)
      : props.field.rules || [];

    if (props.field.required === true) {
      if (!originRules.find((r) => r.required === true)) {
        originRules.push({ required: true });
      }
    }

    const rules = originRules.map((r) => {
      const rule = { ...r };
      if (rule.required === true && !rule.message) {
        const { label } = props.field;
        rule.message = `${isFunction(label) ? label(props.field, formData?.value as any, props.path) : label}是必填项`;
      }

      return rule;
    });

    return {
      ...props.field,
      rules,
    };
  });

  const value = defineModel<any>();
</script>

<style scoped lang="less">
  .title {
    position: relative;
    font-weight: 500;
    margin: 20px 0;
    padding-left: 20px;
    font-size: 16px;
  }

  .title::before {
    position: absolute;
    content: '';
    height: 100%;
    border-left: 5px solid #2166ff;
    left: 0;
    right: 0;
  }
</style>
