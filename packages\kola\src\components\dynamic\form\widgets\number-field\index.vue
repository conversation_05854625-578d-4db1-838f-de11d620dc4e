<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="rules"
    :hide-label="field.hideLabel"
    :hide-asterisk="field.hideAsterisk"
  >
    <a-input-number
      v-model="value"
      :placeholder="placeholderString"
      :min="min"
      :max="max"
      :hide-button="!!field.suffix"
      :precision="field.precision"
      :disabled="disabled"
      :allow-clear="field.allowClear"
      model-event="input"
      :style="field?.style"
      @blur="onBlur"
    >
      <template #suffix v-if="field.suffix">
        {{ field.suffix }}
      </template>
      <template #prefix v-if="field.prefix">
        {{ field.prefix }}
      </template>
    </a-input-number>
    <template #extra v-if="field.extra">
      <component :is="field.extra"></component>
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, ref } from 'vue';
  import { isFunction, isNumber } from 'lodash';
  import { NumberField } from '../../../types/form';

  const value = defineModel<number | undefined>();

  const props = defineProps<{
    field: NumberField;
    path: string;
  }>();

  const formData = inject('formData') ?? ref({});

  const min = computed(() => {
    if (isNumber(props.field.min)) {
      return props.field.min;
    }

    if (isFunction(props.field.min)) {
      return props.field.min(value.value, (formData as any).value);
    }

    return 0;
  });

  const max = computed(() => {
    if (isNumber(props.field.max)) {
      return props.field.max;
    }

    if (isFunction(props.field.max)) {
      return props.field.max(value.value, (formData as any).value);
    }

    return undefined;
  });

  function onBlur() {
    props.field?.onBlur?.(value.value, formData?.value as any);
  }

  const disabled = computed(() => {
    return isFunction(props.field.disabled)
      ? props.field.disabled(props.field, formData?.value as any, props.path)
      : props.field.disabled;
  });

  const rules = computed(() => {
    // eslint-disable-next-line no-shadow
    const rules = [...(props.field.rules || [])];
    if (isNumber(max.value)) {
      rules.push({
        max: max.value,
        message: `${props.field.label}最大值为 ${max.value}`,
      });
    }

    if (isNumber(min.value)) {
      rules.push({
        min: min.value,
        message: `${props.field.label}最小值为 ${min.value}`,
      });
    }
    return rules;
  });
  const placeholderString = computed(() => {
    const { placeholder } = props.field;
    if (isFunction(placeholder)) {
      return placeholder(props.field, formData?.value as any, props.path);
    }
    return props.field.placeholder;
  });
</script>
