import { reactive } from 'vue';

export type IPagination = {
  current: number;
  pageSize: number;
  showPageSize: boolean;
  showTotal: boolean;
  total: number;
};

const usePagination = () => {
  const pagination = reactive<IPagination>({
    current: 1,
    pageSize: 10,
    showPageSize: true,
    showTotal: true,
    total: 0,
  });
  return {
    pagination,
  };
};

export default usePagination;
