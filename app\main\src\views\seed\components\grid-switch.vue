<template>
  <a-switch
    v-model="model"
    :checked-color="checkedColor"
    :unchecked-color="uncheckedColor"
    style="margin-left: 16px;"
  >
    <template #checked>
      <icon-grid />
      <span style="margin-left:4px;">{{ checkedText }}</span>
    </template>
    <template #unchecked>
      <icon-grid />
      <span style="margin-left:4px;">{{ uncheckedText }}</span>
    </template>
  </a-switch>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: Boolean,
  checkedColor: { type: String, default: '#4f8cff' },
  uncheckedColor: { type: String, default: '#d1d5db' },
  checkedText: { type: String, default: '网格' },
  uncheckedText: { type: String, default: '网格' },
});
const emit = defineEmits(['update:modelValue']);
const model = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
});
</script> 