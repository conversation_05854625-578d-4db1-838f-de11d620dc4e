const fs = require('fs');
const path = require('path');
const { OUT_PUT_DIR } = require('../constant.js');


function generateAppUx(meta) {
    const appUXTempDir = path.join(__dirname, '../template/app.ux');
    const appUXDir = path.join(OUT_PUT_DIR, 'app.ux');
    const appUXTemplate = fs.readFileSync(appUXTempDir, 'utf-8');
    const appUx = modifyAppUX(appUXTemplate, meta);
    fs.writeFileSync(appUXDir, appUx, 'utf-8');
}

function modifyAppUX(appUXTemplate, meta) {
    return appUXTemplate
}


module.exports = { generateAppUx }