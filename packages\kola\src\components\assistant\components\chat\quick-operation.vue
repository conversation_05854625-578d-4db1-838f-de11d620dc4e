<template>
  <div class="quick-operation">
    <a-button type="outline" size="mini" class="quick-operation-item" @click="handleClick('question')"
      >常见问题</a-button
    >
    <a-button type="outline" size="mini" class="quick-operation-item" @click="handleClick('help')">帮助手册</a-button>
    <a-button type="outline" size="mini" class="quick-operation-item" @click="handleClick('community')"
      >社区交流</a-button
    >
  </div>
</template>

<script setup lang="ts">
  import { inject } from 'vue';
  import useDictionary from '../../hooks/use-dictionary';

  const platformId: any = inject('platformId');
  const { getDictionary } = useDictionary();
  const configData = getDictionary();
  const emit = defineEmits(['question']);
  const handleClick = (type: string) => {
    if (type === 'question') {
      emit('question');
    }
    if (type === 'help') {
      window.open(configData.value.helpManual[platformId.value], '_blank');
    }
    if (type === 'community') {
      window.open(configData.value.knowledgeUrlMap[platformId.value], '_blank');
    }
  };
</script>

<style scoped>
  .quick-operation {
    display: flex;
    padding: 0 13px;

    .quick-operation-item {
      margin-left: 10px;
      color: rgb(75 75 75);
      border-color: rgb(233 233 233);
      border-radius: 4px;
    }

    .quick-operation-item:hover {
      color: rgb(var(--link-4));
      border-color: rgb(var(--primary-3));
    }
  }
</style>
