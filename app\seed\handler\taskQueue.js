class TaskQueue {
    constructor(){
        this.queue = [];
        this.taskRunning = false;
        this.maxTaskCount = 1; // 最大同时执行数量
    }
    addTask(task){
        this.queue.push(task);
        this.runTask();
    }
    runTask(){
        if(this.taskRunning || this.queue.length === 0){
            return;
        }
        this.taskRunning = true;
        const task = this.queue.shift();
        task().then(()=>{
            this.taskRunning = false;
            this.runTask();
        }).catch((error)=>{
            this.taskRunning = false;
            this.runTask();
        })
    }
}

module.exports = new TaskQueue();