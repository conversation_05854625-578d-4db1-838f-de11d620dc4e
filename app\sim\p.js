const fs = require('fs');
const path = require('path');
const { mean, maxBy, difference } = require('lodash');
const compareFiles = require('./src/compare');
const getFilesRecursive = require('./src/utils/get-files-rec');

const blockDir = path.resolve(__dirname, './cache/block');

let simList = [];
let rAvg = 0;

function main() {
  simList = [];
  const blockRPKList = fs.readdirSync(blockDir).filter(filter);

  const result = groupSimilarElements2(blockRPKList, getTowAppSim);

  return {
    avg: rAvg,
    data: simList,
  };
}

function getTowAppSim(app1, app2) {
  const textList1 = getFilesRecursive(path.resolve(blockDir, app1)).filter(filterSpecialFile);
  const textList2 = getFilesRecursive(path.resolve(blockDir, app2)).filter(filterSpecialFile);
  const sameList = textList1.map((f) => {
    const simily = maxBy(
      textList2.map((fn) => compareFiles(f, fn)),
      'similarity'
    );

    simList.push({
      ...simily,
      file1Path: simplePath(simily.file1Path),
      file2Path: simplePath(simily.file2Path),
      similarity: parseInt(simily.similarity),
    });
    return simily.similarity;
  });

  // const avg = Math.avg(...sameList)
  const avg = mean(sameList);

  rAvg = avg;
  // console.log(avg)

  return avg > 45;
}

function simpleFile(fPath) {
  return fPath.replace(path.resolve('./cache/block'), '');
}

function groupSimilarElements(arr, checkTwo) {
  const groups = [];

  let workList = arr.slice();

  let cur = workList.shift();

  while (workList.length) {
    const sameList = workList.filter((v, i) => {
      return checkTwo(cur, v);
    });

    groups.push([cur, ...sameList]);

    workList = difference(workList, sameList);

    // 一个就不操作了
    if (workList.length === 1) {
      groups.push(workList);
      break;
    }

    cur = workList.shift();
  }

  return groups;
}

function groupSimilarElements2(arr, checkTwo) {
  const groups = [];

  for (const element of arr) {
    let added = false;

    for (const group of groups) {
      // 检查当前元素是否与组内的任何元素相似
      if (group.every((item) => checkTwo(item, element))) {
        group.push(element);
        added = true;
        break;
      }
    }

    // 如果没有找到相似的组，则创建一个新组
    if (!added) {
      groups.push([element]);
    }
  }

  return groups;
}

function filter(v) {
  // return v === '动漫变脸换发型' || v === '火把看看'
  return true;
}

function filterSpecialFile(filePath) {
  return !filePath.includes('UnionAd') && !filePath.includes('YlhAds') && !filePath.includes('CardDemo');
}

function simplePath(filePath) {
  return filePath.replace(blockDir, '').replace('.txt', '.js').slice(1);
}

module.exports = main;
