<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
  >
    <a-range-picker
      :show-time="field.showTime"
      :time-picker-props="{
        defaultValue: ['00:00:00', '00:00:00'],
        ...field.timePickerProps,
      }"
      :style="field.style"
      :mode="field.mode"
      :format="field.format"
      :value-format="field.valueFormat"
      :allow-clear="field.allowClear"
      v-model="value"
      :disabled-date="
        (current) => {
          if (!field.disabledDate) return false;
          return field.disabledDate?.(current, selectedDate);
        }
      "
      :shortcuts="shortcuts"
      shortcuts-position="left"
      @select="
        (value, date, dateString) => {
          selectedDate = dateString ?? [];
        }
      "
    />
    <template #extra v-if="extra">
      <component :is="extra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { inject, ref, watch } from 'vue';
  import { DateRangeField } from '../../../types/form';

  const props = defineProps<{
    field: DateRangeField;
    path: string;
  }>();

  const extra = props.field.extra as unknown as string;
  const selectedDate = ref<(string | undefined)[]>([]);
  const formData = inject('formData');

  const value = defineModel<[string, string] | undefined>({
    default: undefined,
    set(val) {
      props.field.onChange?.(val, formData);
      return val;
    },
  });

  const shortcuts = [
    {
      label: '今天',
      value: getTimeDateVal(0, 0),
    },
    {
      label: '昨天',
      value: getTimeDateVal(1, 1),
    },
    {
      label: '近7天',
      value: getTimeDateVal(7, 1),
    },
    {
      label: '最近一个月',
      value: getTimeDateVal(30, 1),
    },
  ];

  function getTimeDateVal(startDay, endDay) {
    if (props.field.showTime) {
      return [
        dayjs().subtract(startDay, 'day').startOf('day').minute(0).second(0),
        dayjs().subtract(endDay, 'day').minute(0).second(0),
      ];
    }
    return [dayjs().subtract(startDay, 'day'), dayjs().subtract(endDay, 'day')];
  }

  watch(
    value,
    (newVal) => {
      if (!newVal) {
        selectedDate.value = [];
      }
    },
    { immediate: true }
  );
</script>
