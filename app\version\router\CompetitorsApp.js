const express = require('express');
const router = express.Router();
const CompetitorsAppController = require('../controller/CompetitorsApp');

// CompetitorsApp相关路由
router.post('/CompetitorsApps', CompetitorsAppController.createCompetitorsApp); // 创建CompetitorsApp
router.get('/CompetitorsApps', CompetitorsAppController.getAllCompetitorsApps); // 获取CompetitorsApp列表
router.get('/CompetitorsApps/:id', CompetitorsAppController.getCompetitorsAppById); // 获取单个CompetitorsApp
router.put('/CompetitorsApps/:id', CompetitorsAppController.updateCompetitorsApp); // 更新CompetitorsApp
router.delete('/CompetitorsApps/:id', CompetitorsAppController.deleteCompetitorsApp); // 删除CompetitorsApp

module.exports = router;
