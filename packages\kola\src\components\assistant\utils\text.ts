/**
 * 复制文本
 * @param { string} textValue
 * @returns { text }
 */
// eslint-disable-next-line consistent-return
export function copyText(textValue) {
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(textValue);
  }
  const textArea = document.createElement('textarea');
  textArea.value = textValue;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  textArea.remove();
}
export function stripHtmlTags(html) {
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || div.innerText || '';
}

export const highlightSearchText = (text: string, searchText: string) => {
  if (!searchText) return text;
  const regex = new RegExp(`(${searchText})`, 'gi');
  return text.replace(regex, '<span class="highlight">$1</span>');
};
export default copyText;
