import { ComputedRef, InjectionKey } from 'vue';
import { CascaderOption, CascaderOptionInfo } from './interface';

export interface CascaderContext {
  onClickOption: (option: CascaderOptionInfo, checked?: boolean) => void;
  setActiveKey: (key?: string) => void;
  setSelectedPath: (key?: string) => void;
  loadMore?: (option: CascaderOption, done: (children?: CascaderOption[]) => void) => void;
  addLazyLoadOptions?: (children: CascaderOption[], key: string) => void;
  addCustomOptions?: (children: CascaderOption[], key: string) => void;
  formatLabel?: (options: CascaderOption[]) => string;
  valueMap: Map<string, any>;
  getNextPanel: (level: number) => CascaderOptionInfo[];
  optionMap: Map<string, CascaderOptionInfo>;
  reverseSelect: (options: CascaderOptionInfo[] | undefined) => void;
  loading: boolean;
  loadingLevel: number;
  handlePathChange: (item: CascaderOptionInfo) => void;
  selectAll: (options: CascaderOptionInfo[] | undefined, isSelectAll: boolean) => void;
}

export const cascaderInjectionKey: InjectionKey<CascaderContext> = Symbol('ArcoCascader');
