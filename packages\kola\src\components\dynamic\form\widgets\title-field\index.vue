<template>
  <p class="title">{{ field.label }}</p>
  <div v-if="field.extra">
    <component :is="field.extra"></component>
  </div>
</template>

<script setup lang="ts">
  import { BaseField } from '../../../types/form';

  defineProps<{
    field: BaseField;
  }>();
</script>

<style scoped lang="less">
  .title {
    position: relative;
    font-weight: 500;
    margin: 20px 0;
    padding-left: 20px;
    font-size: 16px;
  }

  .title::before {
    position: absolute;
    content: '';
    height: 100%;
    border-left: 5px solid #2166ff;
    left: 0;
    right: 0;
  }
</style>
