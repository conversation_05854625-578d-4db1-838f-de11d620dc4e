const AppVersion = require('../model/version');
const { saveVersion } = require('../service/version');
// 创建AppVersion
const createAppVersion = async (req, res) => {
  try {
    // 获取所有app
    await saveVersion();
    res.status(200).json({
      code: 0,
      msg: '版本信息同步成功',
      data: null,
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 获取AppVersion列表（支持分页和搜索）
const getAllAppVersions = async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, ...filters } = req.query;
    const query = {};

    // 动态添加过滤条件
    Object.keys(filters).forEach((key) => {
      if (filters[key]) {
        query[key] = { $regex: filters[key], $options: 'i' };
      }
    });

    // 计算分页参数
    const skip = (pageNum - 1) * pageSize;

    // 查询总数
    const total = await AppVersion.countDocuments(query);

    // 查询分页数据
    const records = await AppVersion.find(query).skip(skip).limit(Number(pageSize)).sort({ createdAt: -1 });

    res.status(200).json({
      code: 0,
      msg: 'success',
      data: {
        total: total,
        list: records,
      },
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};
// 获取单个AppVersion
const getAppVersionById = async (req, res) => {
  try {
    const record = await AppVersion.findById(req.params.id);
    if (!record) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: record,
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 更新AppVersion
const updateAppVersion = async (req, res) => {
  try {
    const updatedRecord = await AppVersion.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updatedRecord) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: 'success',
      data: updatedRecord,
    });
  } catch (error) {
    res.status(400).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

// 删除AppVersion
const deleteAppVersion = async (req, res) => {
  try {
    const deletedRecord = await AppVersion.findByIdAndDelete(req.params.id);
    if (!deletedRecord) {
      return res.status(404).json({
        code: 1,
        msg: '未找到该记录',
        data: null,
      });
    }
    res.status(200).json({
      code: 0,
      msg: '删除成功',
      data: null,
    });
  } catch (error) {
    res.status(500).json({
      code: 1,
      msg: error.message,
      data: null,
    });
  }
};

module.exports = {
  createAppVersion,
  getAllAppVersions,
  getAppVersionById,
  updateAppVersion,
  deleteAppVersion,
};
