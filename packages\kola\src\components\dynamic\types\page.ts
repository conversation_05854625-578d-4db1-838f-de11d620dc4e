import { DForm } from './form';
import { DButton } from './button';
import { DTable } from './table';

export type DPage = {
  operation: DButton[];
  batchOperation?: DButton[];
  hideSelectedTip?: boolean;
  filter?: DForm;
  table: DTable;
  title?: string;
  autoLoad?: boolean;
  rowKey?: string;
  getCustomParams?: () => Record<string, any>;
  isMultipleSelect?: boolean;
  isSingleSelect?: boolean;
  hideResetColumnsWidth?: boolean;
};
