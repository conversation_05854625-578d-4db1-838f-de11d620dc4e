<template>
  <div>
    <div
      style="display: flex; align-items: center; justify-content: space-between"
      :style="{ marginTop: marginTop }"
    >
      <h3 style="margin: 0">{{ title }}</h3>
      <a-button
        type="primary"
        size="mini"
        @click="$emit('add')"
        style="margin-left: 8px"
        shape="circle"
      >
        <template #icon>
          <icon-plus />
        </template>
      </a-button>
    </div>
    <ul class="mock-page-list">
      <li
        v-for="page in list"
        :key="page.id"
        class="mock-page-item"
        :class="{ active: currentPageId === page.id }"
        @click="$emit('select', page.id)"
      >
        {{ page.name }}
        <a-button
          type="text"
          size="mini"
          class="delete-btn"
          @click.stop="$emit('delete', page)"
        >
          <template #icon>
            <icon-delete />
          </template>
        </a-button>
      </li>
    </ul>
  </div>
</template>

<script setup>
  import { defineProps, defineEmits } from 'vue';
  import { IconPlus, IconDelete } from '@arco-design/web-vue/es/icon';

  defineProps({
    title: String,
    list: Array,
    currentPageId: String,
    marginTop: {
      type: String,
      default: '',
    },
  });

  defineEmits(['add', 'select', 'delete']);
</script>

<style lang="less" scoped>
  // 颜色变量
  @delete-btn-hover-color: red;
  @delete-btn-active-color: white;

  .mock-page-list {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .mock-page-item {
    padding: 6px 12px;
    margin-bottom: 6px;
    background: #f4f6fa;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .mock-page-item:hover {
    background: #e3f2fd;

    .delete-btn {
      color: @delete-btn-hover-color;
    }
  }

  .mock-page-item.active {
    background: #1976d2;
    color: #fff;
    font-weight: bold;

    .delete-btn {
      color: @delete-btn-active-color;
    }
  }
</style>
