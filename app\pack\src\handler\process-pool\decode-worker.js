const { deobfuscate } = require('obfuscator-io-deobfuscator');
const fs = require('fs');

const decodeConfig = {
  silent: true,
  objectSimplification: {
    isEnabled: true,
    unsafeReplace: true,
  },
  objectPacking: {
    isEnabled: true,
  },
  proxyFunctionInlining: {
    isEnabled: true,
  },
  stringRevealing: {
    isEnabled: true,
  },
  expressionSimplification: {
    isEnabled: true,
  },
  constantPropagation: {
    isEnabled: true,
  },
  reassignmentRemoval: {
    isEnabled: true,
  },
  sequenceSplitting: {
    isEnabled: true,
  },
  controlFlowRecovery: {
    isEnabled: true,
  },
  deadBranchRemoval: {
    isEnabled: true,
  },
  unusedVariableRemoval: {
    isEnabled: true,
  },
  propertySimplification: {
    isEnabled: true,
  },
};

process.on('message', (workerData) => {
  const { fullPath, outputFilePath } = workerData;
  try {
    console.log('解析文件:', fullPath);
    const pre = new Date().getTime();
    const content = fs.readFileSync(fullPath, 'utf-8');
    // 将代码解析为 AST
    const parsedCode = deobfuscate(content, decodeConfig);

    fs.writeFileSync(outputFilePath, parsedCode, 'utf-8');
    console.log('解析成功:', outputFilePath, new Date().getTime() - pre);
    // 返回结果
    process.send('ok');
  } catch (err) {
    process.send({ error: err.message });
  } finally {
    process.exit(); // 任务完成后退出子进程
  }
});
