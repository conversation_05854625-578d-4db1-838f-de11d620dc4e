const { parentPort } = require('worker_threads');
const path = require('path');
const fs = require('fs');
const {
  runCodeExtractApp,
  runDevBuild,
  runReleaseLogBuild,
  runReleaseBuild,
  cdPath,
  updateCode,
  clearDist,
  updateAdInterface,
  clearCaches,
  replaceBeforeBuild,
  installHap
} = require('../utils/instruction');
const { extractVersion, getBuildError, getWorkSpace, getOneCommit } = require('../utils/tools');

parentPort.on('message', async (task) => {
  const { devPkg, releaseLogPkg, releasePkg,  timeCost, version, status,sdkCommit, appManifestRes, appDefinePluginRes } = await packApp(task);
  parentPort.postMessage({ type: 'success', devPkg, releaseLogPkg, releasePkg, timeCost, version, status,sdkCommit,appManifestRes, appDefinePluginRes });
  // 将结果发送回主线程
});

function _generateUrl(task) {
  const targetDir = path.join(__dirname, '../../dist', String(task.taskId), task.key);

  if (!fs.existsSync(targetDir)) {
    return { devPkg: '', releaseLogPkg: '', releasePkg: '', version: '' };
  }
  let devPkg = ''
  let releaseLogPkg = ''
  let releasePkg = ''
  const files = fs.readdirSync(targetDir);
  files.forEach((file) => {
    if (file.includes('release-log')) {
      releaseLogPkg = `/api/download/?taskId=${task.taskId}&key=${task.key}&fileName=${file}`
    } else if (file.includes('release')) {
      releasePkg = `/api/download/?taskId=${task.taskId}&key=${task.key}&fileName=${file}`
    } else if (file.includes('debug')) {
      devPkg = `/api/download/?taskId=${task.taskId}&key=${task.key}&fileName=${file}`
    }
  })
  const version = extractVersion(files[0]);
  return { devPkg, releaseLogPkg, releasePkg , version };
}

async function packApp(task) {
  let timeCost = 0;
  let appManifestRes = '';
  let appDefinePluginRes = '';

  const targetDir = getWorkSpace(task.brand, task.sdkVersion)
  console.log('Processing task:', task);
  // 清除dist目录
  try {
    clearDist(targetDir);
    clearCaches(targetDir);
  } catch (error) {
    parentPort.postMessage({ type: 'error', message: '删除dist失败' });
    return { status: 'error', message: '删除dist失败' };
  }

  const startTime = Date.now();
  // 切换目录
  cdPath(targetDir);
  // Define build variants
  const buildTypes = [
    { name: 'dev', buildFn: runDevBuild, isReleaseLog: false },
    { name: 'release-log', buildFn: runReleaseLogBuild, isReleaseLog: true },
    { name: 'release', buildFn: runReleaseBuild, isReleaseLog: false },
  ];
  try {
    // 更新代码
    updateCode(targetDir);
    updateAdInterface(targetDir);
  } catch (error) {
    parentPort.postMessage({ type: 'error', message: '更新代码失败' });
    return { status: 'error', message: '业务代码拉取失败' };
  }
  // Extract code

  try {
   const {appDefinePlugin,appManifest} = runCodeExtractApp(targetDir, task.brand, task.appName, task.sdkVersion);
   appDefinePluginRes = appDefinePlugin
   appManifestRes = appManifest
  } catch (error) {
    console.log('error', error.status, typeof error.status);
    switch (error.status) {
      case 9:
        parentPort.postMessage({
          type: 'error',
          message: '对接层版本不一致，请更新对接层',
          errorCode: error.status,
        });
        return {
          status: 'error',
          message: '对接层版本不一致，请更新对接层',
          errorCode: error.status,
        };
      default:
        parentPort.postMessage({
          type: 'error',
          message: '提取代码失败',
          errorCode: error.status,
        });
        return {
          status: 'error',
          message: '提取代码失败',
          errorCode: error.status,
        };
    }
  }

  const newbuildTypes =
    task.pkgType.indexOf('all') !== -1 ? buildTypes : buildTypes.filter((b) => task.pkgType.indexOf(b.name) !== -1);
    console.log('newbuildTypes',newbuildTypes)
    // installHap()  // 安装hap-toolkit
  for (const { buildFn, isReleaseLog, name } of newbuildTypes) {
  try {
  replaceBeforeBuild(targetDir,name)
  buildFn(targetDir);
  } catch (error) {
  const errorMessage = getBuildError(error?.output?.toString());
  parentPort.postMessage({
  type: 'error',
  message: `打包失败:${errorMessage}`,
  });
  console.log('打包失败:', errorMessage);
  return { status: 'error', message: `打包失败:${errorMessage}` };
  }
const resultDir = `${String(task.taskId)}/${task.key}`;
  try {
  _cpRpk(targetDir, resultDir, isReleaseLog);
  } catch (error) {
  parentPort.postMessage({ type: 'error', message: '复制rpk文件失败' });
  console.log('复制rpk文件失败:', error);
  return { status: 'error', message: '复制rpk文件失败' };
  }
}

  // Cleanup and return results
  timeCost = (Date.now() - startTime) / 1000;
  console.log(`Total build time: ${timeCost}s`);
  const { devPkg, releaseLogPkg, releasePkg, version } = _generateUrl(task);
  const sdkCommit = await getOneCommit('ad-sdk', task.sdkVersion)
  return {
    devPkg,
    releaseLogPkg,
    releasePkg,
    timeCost,
    version,
    status: 'success',
    sdkCommit,
    appManifestRes,
    appDefinePluginRes
  };
}

function _cpRpk(targetDir, dirName, isReleaseLog = false) {
  const destinationDir = path.join(__dirname, '../../dist', dirName); // Specify your output directory
  const sourceDir = path.join(targetDir, 'dist'); // Specify your output directory
  // Ensure the output directory exists
  if (!fs.existsSync(destinationDir)) {
    fs.mkdirSync(destinationDir, { recursive: true });
  }
  console.log(`Cleared existing files in ${destinationDir}`);
  // Copy .rpk files to the destination directory
  const files = fs.readdirSync(sourceDir);
  files.forEach((file) => {
    if (file.endsWith('.rpk')) {
      const newFile = isReleaseLog ? file.replace('release', 'release-log') : file;
      const sourceFile = path.join(sourceDir, file);
      const destFile = path.join(destinationDir, newFile);
      fs.copyFileSync(sourceFile, destFile);
      console.log(`Copied ${sourceFile} to ${destFile}`);
    }
  });

  // 删除工作区
  // fs.rmSync(targetDir, { recursive: true, force: true })
}
