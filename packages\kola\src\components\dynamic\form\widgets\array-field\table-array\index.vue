<template>
  <a-button @click="handleAdd" type="primary" class="add-btn" :disabled="!canAdd">
    <template #icon>
      <icon-plus />
    </template>
    <template #default>添加</template>
  </a-button>
  <a-table class="array-table" :data="value" :pagination="false" :bordered="{ wrapper: true, cell: true }">
    <template #columns>
      <template v-for="f in (field.item as ObjectField).fields" :key="f.name">
        <a-table-column
          :title="f.label"
          :width="f.style?.width"
          :data-index="f.name"
          :header-cell-class="f.required || f.rules?.some((rule) => rule.required) ? 'table-array-column-required' : ''"
          v-if="!f.visibleOn || f.visibleOn(value, formData)"
        >
          <template #cell="{ record, rowIndex }">
            <WidgetByType :field="f" :path="`${path}[${rowIndex}]`" v-model="record[f.name]" :hide-label="true" />
          </template>
        </a-table-column>
      </template>

      <a-table-column title="操作" data-index="action">
        <template #cell="{ rowIndex }">
          <a-button @click="handleRemove(rowIndex)" type="text" :disabled="!canRemove">删除</a-button>
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<script setup lang="ts">
  import { inject } from 'vue';
  import { ArrayField, ObjectField } from '../../../../types/form';
  import WidgetByType from '../../widget-by-type.vue';

  const value = defineModel<any[]>({ default: [] });

  const formData = inject<Record<string, any>>('formData') ?? {};

  defineProps<{
    field: ArrayField;
    path: string;
    canAdd: boolean;
    canRemove: boolean;
  }>();

  const emits = defineEmits<{
    add: [index: number];
    remove: [index: number];
  }>();

  const handleAdd = () => {
    emits('add', value.value.length);
  };

  const handleRemove = (index: number) => {
    emits('remove', index);
  };
</script>

<style lang="less" scoped>
  .add-btn {
    margin-bottom: 8px;
  }

  .array-table {
    .arco-form-item {
      margin-bottom: 0;
    }
  }

  :global(.table-array-column-required .arco-table-th-title::after) {
    content: ' *';
    color: rgb(var(--danger-6));
  }
</style>
