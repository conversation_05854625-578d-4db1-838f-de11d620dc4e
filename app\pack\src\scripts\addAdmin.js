const mongoose = require('mongoose');
const {salt,sha256} = require('../utils/tools');
// 定义管理员模型
const adminSchema = new mongoose.Schema({
    username: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    role: { type: String, default: 'admin' }, // 角色字段，默认为 'admin'
    createdAt: { type: Date, default: Date.now },
    salt: { type: String, required: true },
    hash: { type: String, required: true },
    id: { type: Number, required: true },
});

const Admin = mongoose.model('users', adminSchema);

// 插入第一个管理员账户的函数
async function createFirstAdmin() {
    try {
        // 连接到 MongoDB 数据库
        await mongoose.connect('mongodb://localhost:27017/fe');
        console.log('数据库连接成功');

        
        const existingAdmin = await Admin.findOne({ username: 'admin' });
        
        const username = 'admin';
        const password = 'admin123';
        const salts = salt();
        const hash = sha256(password + salts);
        
        // 创建第一个管理员账户
        const records = {
            username,
            password, // 实际项目中应使用加密后的密码
            role: 'admin',
            id: 4,
            salt:salts,
            hash
        }
       
        // 检查是否已存在管理员账户
        if (existingAdmin) {
            console.log('管理员账户已存在,更新记录');
           await Admin.updateOne({ username: 'admin' }, records)
            return;
        }

        // 保存到数据库
        const admin = new Admin(records);
        await admin.save();
        console.log('第一个管理员账户创建成功:', admin);
    } catch (error) {
        console.error('发生错误:', error);
    } finally {
        // 关闭数据库连接
        await mongoose.disconnect();
        console.log('数据库连接已关闭');
    }
}

// 执行脚本
createFirstAdmin();