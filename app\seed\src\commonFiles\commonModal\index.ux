<template>
    <div class="common-modal">
      <div class="common-modal-content">
        <text class="title">{{ title }}</text>
        <text class="content" if="content">{{ content }}</text>
        <slot name="default"></slot>
        <div class="common-modal-btns">
          <div
            class="modal-btn"
            style="{{confirmBtnStyle}}"
            @click="confirmFun"
            if="{{showDif}}"
          >
            <text style="{{confirmTextStyle}}">{{ confirmBtnText }}</text>
          </div>
          <div class="modal-btn" @click="cancelFun" style="{{cancelBtnStyle}}">
            <text style="{{cancelTextStyle}}">{{ cancelBtnText }}</text>
          </div>
          <div
            class="modal-btn"
            style="{{confirmBtnStyle}}"
            @click="confirmFun"
            if="{{!showDif}}"
          >
            <text style="{{confirmTextStyle}}">{{ confirmBtnText }}</text>
          </div>
        </div>
      </div>
    </div>
</template>
<script>
export default {
  props: {
    showDif: {
      type: Boolean,
      default: false
    },
    showModal: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    cancelBtnText: {
      type: String,
      default: ''
    },
    confirmBtnText: {
      type: String,
      default: ''
    },
    confirmBtnStyle: {
      type: String,
      default: ''
    },
    cancelBtnStyle: {
      type: String,
      default: ''
    },
    confirmTextStyle: {
      type: String,
      default: ''
    },
    cancelTextStyle: {
      type: String,
      default: ''
    },
    showRichText: {
      type: Boolean,
      default: true
    },
  },
  cancelFun() {
    this.$emit('cancelFun', {})
  },
  confirmFun() {
    this.$emit('confirmFun', {})
  },
}
</script>

<style lang="less">
text {
  font-family: PingFang SC, PingFang SC-Medium;
}
.common-modal {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
  align-items: center;
  justify-content: center;
  .common-modal-content {
    width: 600px;
    background-color: #ffffff;
    border-radius: 48px;
    padding: 48px 40px 40px 40px;
    flex-direction: column;
    align-items: center;
    .title {
      font-size: 36px;
      font-weight: 700;
      color: #222222;
      text-align: center;
      margin-bottom: 44px;
    }
    .content {
      width: 500px;
      font-size: 32px;
      font-weight: 400;
      color: #888888;
      line-height: 48px;
      text-align: center;
    }
    .common-modal-btns {
      justify-content: space-between;
      margin-top: 60px;
      width: 100%;
      .modal-btn {
        width: 240px;
        height: 80px;
        background-color: rgba(153, 153, 153, 0.1);
        border-radius: 40px;
        align-items: center;
        justify-content: center;
        text {
          color: #404040;
          font-weight: 500;
          font-size: 32px;
        }
      }
    }
  }
}
</style>
