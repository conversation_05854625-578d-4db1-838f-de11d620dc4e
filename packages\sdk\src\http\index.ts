import axios, { AxiosInstance, AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { Message } from '@arco-design/web-vue';
import { RESULT_ENUM } from '../constants/http-enum';
import checkStatus from './helper/check-status';
import { getLoginUrl } from '../auth/utils';
import { getToken } from '@/utils/auth';
function showMessage(response) {
  if (response?.data?.message || response?.data?.msg) {
    Message.error(`${response.data.message || response.data.msg}`);
  } else {
    checkStatus(response?.status);
  }
}

class RequestHttp {
  service: AxiosInstance;

  // eslint-disable-next-line no-shadow
  public constructor(config: AxiosRequestConfig) {
    // instantiation
    this.service = axios.create(config);

    /**
     * @description 请求拦截器
     * 客户端发送请求 -> [请求拦截器] -> 服务器
     */
    this.service.interceptors.request.use(
      // eslint-disable-next-line no-shadow
      (config: any) => {
        if (!config.headers) {
          config.headers = {};
        }
        const token = getToken();
        if (token) {
          if (!config.headers) {
            config.headers = {};
          }
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error: AxiosError) => {
        // @ts-ignore
        return Promise.reject(error);
      }
    );

    /**
     * @description 响应拦截器
     *  服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
     */
    this.service.interceptors.response.use(
      (response: AxiosResponse) => {
        const { data } = response;

        console.log('http', data)
        // 全局错误信息拦截（防止下载文件的时候返回数据流，没有 code 直接报错）
        // 此处兼容了两种code  javaApi code  是0  goApi  code 是1
        if (data.code && data.code !== RESULT_ENUM.SUCCESS) {
          Message.error(`失败：${data.message ?? data.msg}`);
          // @ts-ignore
          return Promise.reject(data);
        }
        // 成功请求（在页面上除非特殊情况，否则不用处理失败逻辑）
        return data;
      },
      async (error: AxiosError) => {
        const { response } = error;

        // 登录失效
        if (response?.status === RESULT_ENUM.OVERDUE) {
          showMessage(response);
          setTimeout(() => {
            window.location.href = getLoginUrl();
          }, 1500);
          return;
        }

        // 请求超时 && 网络错误单独判断，没有 response
        if (error.message.indexOf('timeout') !== -1) Message.error('请求超时！请您稍后重试');
        if (error.message.indexOf('Network Error') !== -1) Message.error('网络错误！请您稍后重试');
        // 根据服务器响应的错误状态码，做不同的处理
        if (response) {
          showMessage(response);
        }
        // 服务器结果都没有返回(可能服务器错误可能客户端断网)，断网处理:可以跳转到断网页面
        // if (!window.navigator.onLine) router.replace('/500');
        // @ts-ignore
        // eslint-disable-next-line consistent-return
        return Promise.reject(error);
      }
    );
  }

  /**
   * @description 常用请求方法封装
   */
  get(url: string, params?: object, _object = {}): Promise<any> {
    return this.service.get(url, params);
  }

  post(url: string, params?: object | string, _object = {}): Promise<any> {
    return this.service.post(url, params, _object);
  }

  put(url: string, params?: object | string, _object = {}): Promise<any> {
    return this.service.put(url, params, _object);
  }

  delete(url: string, params?: object | string, _object = {}): Promise<any> {
    return this.service.delete(url);
  }

  download(url: string, params?: object, _object = {}): Promise<any> {
    return this.service.post(url, params, { ..._object, responseType: 'blob' });
  }
}

export const RequestHttpConfig = {
  baseURL: '/',
  // 设置超时时间
  timeout: RESULT_ENUM.TIMEOUT,
  // 跨域时候允许携带凭证
  withCredentials: true,
};

// @ts-ignore
export const http = new RequestHttp(RequestHttpConfig);

// @ts-ignore
export default RequestHttp;
