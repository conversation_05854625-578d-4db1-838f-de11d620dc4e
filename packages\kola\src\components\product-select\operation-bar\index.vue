<template>
  <div class="operations">
    <a-space>
      <template v-for="item in operation" :key="item.text">
        <DynamicButton :config="item"></DynamicButton>
      </template>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  import { defineOptions, defineProps } from 'vue';
  import type { DButton } from '../../types/button';

  defineProps<{
    operation: DButton[];
  }>();

  defineOptions({ name: 'OperationBar' });
</script>

<style scoped lang="less">
  .operations {
    margin-bottom: 16px;
  }
</style>
