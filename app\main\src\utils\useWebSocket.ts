// useWebSocket.ts
import { ref, onMounted, onBeforeUnmount, type Ref } from 'vue'

interface WebSocketOptions {
  reconnectInterval?: number // 重连间隔时间（毫秒）
  maxReconnectAttempts?: number // 最大重连次数
}

interface UseWebSocket {
  messages: Ref<string[]> // 接收到的消息
  isConnected: Ref<boolean> // 连接状态
  sendMessage: (message: string) => void // 发送消息的方法
}

export default function useWebSocket(url: string, options: WebSocketOptions = {}): UseWebSocket {
  const ws = ref<WebSocket | null>(null)
  const messages = ref<any[any]>([])
  const isConnected = ref<boolean>(false)
  const reconnectInterval = options.reconnectInterval || 3000
  const maxReconnectAttempts = options.maxReconnectAttempts || 5
  const timer = ref<any>(null)

  let reconnectAttempts = 0 // 当前重连次数

  const sendMessage = (message: string) => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      ws.value.send(message)
    } else {
      console.error('WebSocket 未连接，无法发送消息')
    }
  }
  const connectWebSocket = () => {
    ws.value = new WebSocket(url)
    const keepAlive = ()=>{
      timer.value = setInterval(()=>{
        sendMessage('keepAlive')
      },1000*30)
    }
    

  const attemptReconnect = () => {
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.warn('达到最大重连次数，停止重连')
      return
    }
    setTimeout(() => {
      reconnectAttempts += 1
      console.log(`尝试重连...（第 ${reconnectAttempts} 次）`)
      connectWebSocket()
    }, reconnectInterval)
  }

  

  ws.value.onopen = () => {
    console.log('WebSocket 连接已建立')
    isConnected.value = true
    reconnectAttempts = 0 // 重置重连次数
    keepAlive()
  }

  ws.value.onmessage = (event: MessageEvent) => {
    console.log('接收到消息:', JSON.parse(event.data))
    messages.value = [...messages.value, JSON.parse(event.data)]
  }

  ws.value.onclose = () => {
    console.log('WebSocket 连接已关闭')
    isConnected.value = false
    clearInterval(timer.value)
  }

  ws.value.onerror = (error: Event) => {
    console.error('WebSocket 错误:', error)
    ws.value?.close() // 发生错误时关闭连接，触发 onclose
    attemptReconnect() // 尝试重连
  }
}


  onMounted(() => {
    connectWebSocket()
  })

  onBeforeUnmount(() => {
    ws.value?.close()
  })

  return {
    messages,
    isConnected,
    sendMessage,
  }
}
