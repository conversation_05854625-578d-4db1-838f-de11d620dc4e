// ? 全局默认配置项

// 首页地址（默认）
import { extractDomainFromUrl } from '../auth/utils';

// 登录页地址（默认）
// @ts-ignore
import { isDev, isTest, isProd } from '../auth/env';

export const HOME_URL = '/home';

const domain = extractDomainFromUrl(window.location.origin) ?? '';

const testPassport = domain.includes('chengyouyun') ? 'https://test-workspace' : 'https://test-passport';
const prodPassport = domain.includes('chengyouyun') ? 'https://workspace' : 'https://passport';

let loginUrl = '';
const baseLoginUrl = '/login';
loginUrl = baseLoginUrl;
// if (isDev) {
//   loginUrl = baseLoginUrl;
// } else if (isTest) {
//   loginUrl = `${testPassport}${domain}${baseLoginUrl}`;
// } else if (isProd) {
//   loginUrl = `${prodPassport}${domain}${baseLoginUrl}`;
// } else {
//   loginUrl = baseLoginUrl;
// }

export const LOGIN_URL = loginUrl;
