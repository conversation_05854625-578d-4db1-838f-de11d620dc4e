<template>
  <div class="page">
    <h1>Nginx 配置</h1>
    <DynamicForm
      v-model="formData"
      :form-schema="formSchema"
      :default-form-data="defaultValue"
    ></DynamicForm>
    <DynamicButton :config="buttonConfig"></DynamicButton>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { getNginxConfig, putNginxConfig } from '@/api/nginx';

  const formData = ref({ data: '' });
  const defaultValue = ref({
    data: '',
  });

  const formSchema = ref({
    fields: [
      {
        label: '',
        name: 'data',
        type: 'textarea',
        autoSize: true,
        hideLabel: true,
      },
    ],
  });

  async function submit() {
    const data = await putNginxConfig(formData.value);
    console.log('data', data);
  }

  const buttonConfig = {
    text: '更新',
    props: {
      type: 'primary',
    },
    clickActionType: 'action',
    action: () => {
      submit();
    },
  };

  async function getData() {
    const data = await getNginxConfig();
    formData.value = { data };
  }

  getData();
</script>

<style lang="less" scoped>
  .page {
    padding: 20px;
    background: #fff;
  }
</style>
