<template>
  <div style="padding: 16px; background-color: #f5f5f5; height: 100%">
    <DynamicPage
    :filter="filterConfig"
    :table="tableConfig"
    :operation="operations"
    :batch-operation="batchOperations"
    auto-load
    row-key="_id"
  />
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import UserApi from '@/api/Users';
  import { ROLE_LIST } from './constants';
  // Filter 配置
  const filterConfig = ref({
    formSchema: {
      fields: [
        {
          name: 'username',
          label: 'username',
          type: 'text',
          placeholder: '请输入username',
        },
        {
          name: 'role',
          label: 'role',
          type: 'select',
          format: 'singleSelect',
          source: {
            data: ROLE_LIST,
          },
        },
      ],
    },
  });

  // 数据加载方法
  async function loadData(filter, pagination) {
    return UserApi.list({
      ...filter,
      ...pagination,
    });
  }
  // Table 配置
  const tableConfig = ref({
    columns: [
      {
        title: 'username',
        dataIndex: 'username',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'role',
        dataIndex: 'role',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'status',
        dataIndex: 'status',
        width: 150,
        ellipsis: true,
      },
      {
        title: 'lastLogin',
        dataIndex: 'lastLogin',
        width: 150,
        ellipsis: true,
      },


      {
        title: '操作',
        dataIndex: 'operations',
        customRender: {
          type: 'operations',
          props: {
            operations: [
              {
                text: '编辑',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    'title': '编辑',
                    'esc-to-close': false,
                  },
                  contentType: 'form',
                  form: {
                    formSchema: {
                      fields: [
                        {
                          name: 'username',
                          label: 'username',
                          type: 'text',
                          placeholder: '请输入username',
                        },
                        {
                          name: 'password',
                          label: 'password',
                          type: 'text',
                          placeholder: '请输入password',
                        },
                        {
                          name: 'role',
                          label: 'role',
                          type: 'select',
                          format: 'singleSelect',
                          source: {
                            data: ROLE_LIST,
                          },
                          placeholder: '请输入role',
                        },
                        {
                          name: 'status',
                          label: 'status',
                          type: 'text',
                          placeholder: '请输入status',
                        },
                      ],
                    },
                  },
                  getDefaultValue(rowData) {
                    const { _id: id } = rowData;
                    return UserApi.getById(id).then((res) => res.data);
                  },
                  action: async ({ formData, record, refreshTable }) => {
                    const { _id: id } = record;
                    await UserApi.update(id, formData);
                    refreshTable();
                  },
                },
              },
              {
                text: '删除',
                props: {
                  type: 'text',
                },
                clickActionType: 'modal',
                modal: {
                  props: {
                    title: '确认删除',
                  },
                  contentType: 'text',
                  text: '确认删除？',
                  action: async ({ record, refreshTable }) => {
                    const { _id: id } = record;
                    await UserApi.delete(id);
                    refreshTable();
                  },
                },
              },
            ],
          },
        },
      },
    ],
    isPageable: true,
    pagination: {
      current: 1,
      pageSize: 10,
      showPageSize: true,
      showTotal: true,
      total: 0,
      showJumper: true,
      pageSizeOptions: [10, 20, 50],
    },
    load: {
      action: loadData,
    },
  });

  // 操作按钮配置
  const operations = ref([
    {
      text: '新增',
      props: {
        type: 'primary',
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '新增',
          width: 800,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                name: 'username',
                label: 'username',
                type: 'text',
                placeholder: '请输入username',
              },
              {
                name: 'password',
                label: 'password',
                type: 'text',
                placeholder: '请输入password',
              },
              {
                name: 'role',
                label: 'role',
                type: 'select',
                format: 'singleSelect',
                source: {
                  data: ROLE_LIST
                },
                placeholder: '请输入role',
              },
              {
                name: 'status',
                label: 'status',
                type: 'text',
                placeholder: '请输入status',
              }
            ],
          },
        },
        getDefaultValue: () => {
          return {
            username: '',
            password: '',
            role: '',
            status: 'active',
            lastLogin: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        },
        close: (data) => {
          data.refreshTable();
        },
        action: async (data) => {
          await UserApi.create(data.formData);
          Message.success('操作成功');
          data.refreshTable();
        },
      },
    },
    {
      text: '批量操作',
      props: {
        type: 'primary',
      },
      clickActionType: 'batch',
    },
  ]); // 单个操作按钮
  const batchOperations = ref([]); // 批量操作按钮
</script>
