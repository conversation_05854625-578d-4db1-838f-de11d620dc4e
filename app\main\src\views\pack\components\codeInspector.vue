<template>
  <a-tabs>
    <a-tab-pane key="1" title="manifest">
      <vue-code-highlight
        :codeValue="record.appManifestRes || '-'"
        lang="javascript"
        height="300"
        width="100%"
        :scrollStyleBool="true"
      >
      </vue-code-highlight>
    </a-tab-pane>
    <a-tab-pane key="2" title="defineConfig">
      <vue-code-highlight
        :codeValue="record.appDefinePluginRes || '-'"
        lang="javascript"
        height="300"
        width="100%"
        :scrollStyleBool="true"
      >
      </vue-code-highlight>
    </a-tab-pane>
  </a-tabs>
</template>

<script lang="ts" setup>
  import 'vue-highlight-code/dist/style.css';

  const props = defineProps<{
    record: any;
  }>();
</script>
