const bluetooth = {
  openAdapter: (obj) => {
    if (obj.success) {
      obj.success()
    }
  },
  closeAdapter: (obj) => {
    if (obj.success) {
      obj.success()
    }
  },
  getAdapterState: (obj) => {
    // 模拟获取适配器状态成功
    const mockData = {
      available: true,
      discovering: false
    };
    
    if (obj.success) {
      obj.success(mockData);
    }
    if (obj.complete) {
      obj.complete();
    }
  },
  onAdapterStateChange: (callback) => {
    console.log('Adapter state change listener registered');
    // 模拟状态变化（仅示例，实际不会主动触发）
    setTimeout(() => {
      if (typeof callback === 'function') {
        callback({ available: false });
      }
    }, 5000);
  },
  startDevicesDiscovery: (obj) => {
    const { services = [] } = obj;
    console.log(`开始发现蓝牙设备，筛选服务: ${services.join(', ')}`);
    
    if (obj.success) {
      obj.success();
    }
    // 可以添加失败情况的模拟
    // if (obj.fail) {
    //   obj.fail({ errorMsg: '发现设备失败' }, 10003);
    // }
    if (obj.complete) {
      obj.complete();
    }
  },
  stopDevicesDiscovery: (obj) => {
    console.log('停止蓝牙设备发现');
    if (obj.success) {
      obj.success();
    }
    if (obj.complete) {
      obj.complete();
    }
  },
  getDevices: (obj) => {
    // 模拟获取已发现的蓝牙设备列表
    const mockDevices = [
      {
        deviceId: 'device-001',
        name: 'SensorTag',
        RSSI: -60,
        advertisData: new ArrayBuffer(4),
        serviceData: {
          'FEE7': new ArrayBuffer(8)
        }
      },
      {
        deviceId: 'device-002',
        name: 'SmartWatch',
        RSSI: -75,
        advertisData: new ArrayBuffer(6),
        serviceData: {
          '180D': new ArrayBuffer(4),
          '180F': new ArrayBuffer(4)
        }
      }
    ];
    // 为模拟数据填充一些随机值
    mockDevices.forEach(device => {
      // 填充advertisData
      const advData = new Uint8Array(device.advertisData);
      for (let i = 0; i < advData.length; i++) {
        advData[i] = Math.floor(Math.random() * 256);
      }
      // 填充serviceData
      for (const uuid in device.serviceData) {
        const serviceData = new Uint8Array(device.serviceData[uuid]);
        for (let i = 0; i < serviceData.length; i++) {
          serviceData[i] = Math.floor(Math.random() * 256);
        }
      }
    });
    const mockData = {
      devices: mockDevices
    };
    console.log('获取蓝牙设备列表成功');
    if (obj.success) {
      obj.success(mockData);
    }
    // 可以添加失败情况的模拟
    // if (obj.fail) {
    //   obj.fail({ errorMsg: '获取设备列表失败' }, 10005);
    // }
    if (obj.complete) {
      obj.complete();
    }
  },
  onDeviceFound: (callback) => {
    // 实际环境中，这应该监听系统事件并在发现新设备时调用回调
    // 这里仅作为示例保留接口定义
    console.log('Device discovery listener registered');
    
    // 模拟发现新设备（仅示例，实际不会主动触发）
    const mockNewDevice = {
      deviceId: 'device-003',
      name: 'HeartRateMonitor',
      RSSI: -70,
      advertisData: new ArrayBuffer(8),
      serviceData: {
        '180D': new ArrayBuffer(6)
      }
    };
    
    // 为模拟数据填充随机值
    const advData = new Uint8Array(mockNewDevice.advertisData);
    for (let i = 0; i < advData.length; i++) {
      advData[i] = Math.floor(Math.random() * 256);
    }
    
    for (const uuid in mockNewDevice.serviceData) {
      const serviceData = new Uint8Array(mockNewDevice.serviceData[uuid]);
      for (let i = 0; i < serviceData.length; i++) {
        serviceData[i] = Math.floor(Math.random() * 256);
      }
    }
    
    // 模拟3秒后发现新设备
    setTimeout(() => {
      if (typeof callback === 'function') {
        callback({ devices: [mockNewDevice] });
      }
    }, 3000);
  },
  getConnectedDevices: (obj) => {
    // 模拟获取已连接的蓝牙设备
    const mockConnectedDevices = [
      {
        deviceId: 'connected-001',
        name: 'ConnectedHeadphones',
        RSSI: -55,
        services: ['180A', '180D', '180F']
      }
    ];
    
    const mockData = {
      devices: mockConnectedDevices
    };
    
    console.log('获取已连接的蓝牙设备成功');
    
    if (obj.success) {
      obj.success(mockData);
    }
    
    // 可以添加失败情况的模拟
    // if (obj.fail) {
    //   obj.fail({ errorMsg: '获取已连接设备失败' }, 10006);
    // }
    
    if (obj.complete) {
      obj.complete();
    }
  },
  createBLEConnection: (obj) => {
    // 提取传入的参数
    const { deviceId } = obj;
    
    // 模拟连接过程
    console.log(`正在连接到蓝牙设备: ${deviceId}`);
    
    // 随机决定连接是否成功
    const isSuccess = Math.random() > 0.3;
    
    setTimeout(() => {
      if (isSuccess) {
        console.log(`成功连接到设备: ${deviceId}`);
        if (obj.success) {
          obj.success();
        }
      } else {
        const errorCode = 10007;
        const errorMsg = '连接失败，设备可能不可用或已配对';
        console.log(`连接失败，错误码: ${errorCode}`);
        if (obj.fail) {
          obj.fail({ errorMsg }, errorCode);
        }
      }
      
      if (obj.complete) {
        obj.complete();
      }
    }, 1000);
  },
  closeBLEConnection: (obj) => {
    // 提取传入的参数
    const { deviceId } = obj;

    // 模拟断开连接过程
    console.log(`正在断开与蓝牙设备的连接: ${deviceId}`);
    
    // 随机决定断开是否成功
    const isSuccess = Math.random() > 0.2;
    
    setTimeout(() => {
      if (isSuccess) {
        console.log(`成功断开与设备的连接: ${deviceId}`);
        if (obj.success) {
          obj.success();
        }
      } else {
        const errorCode = 10008;
        const errorMsg = '断开连接失败，设备可能已断开或不存在';
        console.log(`断开连接失败，错误码: ${errorCode}`);
        if (obj.fail) {
          obj.fail({ errorMsg }, errorCode);
        }
      }
      
      if (obj.complete) {
        obj.complete();
      }
    }, 800);
  },
  getBLEDeviceServices: (obj) => {
    // 提取传入的参数
    const { deviceId } = obj;
    
    // 模拟获取设备服务
    console.log(`正在获取设备 ${deviceId} 的服务列表`);
    
    // 模拟常见的蓝牙服务列表
    const mockServices = [
      {
        uuid: '1800',
        isPrimary: true,
        characteristics: ['2A00', '2A01']
      },
      {
        uuid: '1801',
        isPrimary: true,
        characteristics: ['2A05']
      },
      {
        uuid: '180D',
        isPrimary: true,
        characteristics: ['2A37', '2A38']
      },
      {
        uuid: '180F',
        isPrimary: true,
        characteristics: ['2A19']
      }
    ];
    
    const mockData = {
      services: mockServices
    };
    
    // 随机决定操作是否成功
    const isSuccess = Math.random() > 0.2;
    
    setTimeout(() => {
      if (isSuccess) {
        console.log(`成功获取设备 ${deviceId} 的服务列表`);
        if (obj.success) {
          obj.success(mockData);
        }
      } else {
        const errorCode = 10009;
        const errorMsg = '获取服务列表失败，设备可能未连接或不支持此操作';
        console.log(`获取服务列表失败，错误码: ${errorCode}`);
        if (obj.fail) {
          obj.fail({ errorMsg }, errorCode);
        }
      }
      
      if (obj.complete) {
        obj.complete();
      }
    }, 1000);
  },
  getBLEDeviceCharacteristics: (obj) => {
    // 提取传入的参数
    const { deviceId, serviceId } = obj;
    
    // 模拟获取设备特征值
    console.log(`正在获取设备 ${deviceId} 服务 ${serviceId} 的特征值列表`);
    
    // 根据不同服务ID返回不同的特征值列表
    const mockCharacteristics = (() => {
      switch (serviceId) {
        case '1800': // 通用访问服务
          return [
            {
              uuid: '2A00',
              properties: {
                read: true,
                write: false,
                notify: false
              }
            },
            {
              uuid: '2A01',
              properties: {
                read: true,
                write: false,
                notify: false
              }
            }
          ];
        case '1801': // 通用属性服务
          return [
            {
              uuid: '2A05',
              properties: {
                read: true,
                notify: true
              }
            }
          ];
        case '180D': // 心率服务
          return [
            {
              uuid: '2A37',
              properties: {
                read: false,
                notify: true
              }
            },
            {
              uuid: '2A38',
              properties: {
                read: true,
                write: false,
                notify: false
              }
            }
          ];
        case '180F': // 电池服务
          return [
            {
              uuid: '2A19',
              properties: {
                read: true,
                notify: true
              }
            }
          ];
        default: // 未知服务返回通用特征值
          return [
            {
              uuid: '2AXX',
              properties: {
                read: true,
                write: true,
                notify: true
              }
            }
          ];
      }
    })();
    
    const mockData = {
      characteristics: mockCharacteristics
    };
    
    // 随机决定操作是否成功
    const isSuccess = Math.random() > 0.25;
    
    setTimeout(() => {
      if (isSuccess) {
        console.log(`成功获取设备 ${deviceId} 服务 ${serviceId} 的特征值列表`);
        if (obj.success) {
          obj.success(mockData);
        }
      } else {
        const errorCode = 10010;
        const errorMsg = '获取特征值列表失败，设备可能未连接或服务不存在';
        console.log(`获取特征值列表失败，错误码: ${errorCode}`);
        if (obj.fail) {
          obj.fail({ errorMsg }, errorCode);
        }
      }
      
      if (obj.complete) {
        obj.complete();
      }
    }, 1200);
  },
  // 新增：读取蓝牙特征值
  readBLECharacteristicValue: (obj) => {
    // 提取传入的参数
    const { deviceId, serviceId, characteristicId } = obj;
    
    // 模拟读取特征值
    console.log(`正在读取设备 ${deviceId} 服务 ${serviceId} 特征值 ${characteristicId}`);
    
    // 根据不同特征值ID生成模拟数据
    const generateMockValue = () => {
      const buffer = new ArrayBuffer(4);
      const view = new DataView(buffer);
      
      switch (characteristicId) {
        case '2A00': // 设备名称
          return new TextEncoder().encode('MyBluetoothDevice');
        case '2A19': // 电池电量
          view.setUint8(0, Math.floor(Math.random() * 100)); // 0-100随机值
          return buffer;
        case '2A37': // 心率测量
          view.setUint8(0, 0x06); // 标志位
          view.setUint8(1, Math.floor(Math.random() * 50) + 60); // 60-110随机值
          return buffer;
        default: // 通用情况
          for (let i = 0; i < 4; i++) {
            view.setUint8(i, Math.floor(Math.random() * 256));
          }
          return buffer;
      }
    };
    
    const mockValue = generateMockValue();
    
    // 随机决定操作是否成功
    const isSuccess = Math.random() > 0.3;
    
    setTimeout(() => {
      if (isSuccess) {
        console.log(`成功发起读取设备 ${deviceId} 服务 ${serviceId} 特征值 ${characteristicId}`);
        
        // 模拟特征值变化事件
        if (typeof bluetooth.onBLECharacteristicValueChange === 'function') {
          bluetooth.onBLECharacteristicValueChange({
            deviceId,
            serviceId,
            characteristicId,
            value: mockValue
          });
        }
        
        if (obj.success) {
          obj.success();
        }
      } else {
        const errorCode = 10011;
        const errorMsg = '读取特征值失败，特征值可能不支持读取或设备断开连接';
        console.log(`读取特征值失败，错误码: ${errorCode}`);
        if (obj.fail) {
          obj.fail({ errorMsg }, errorCode);
        }
      }
      
      if (obj.complete) {
        obj.complete();
      }
    }, 1000);
  },
  
  // 新增：特征值变化事件监听函数
  onBLECharacteristicValueChange: null,
  
  // 设置特征值变化回调
  setCharacteristicValueChangeCallback: (callback) => {
    bluetooth.onBLECharacteristicValueChange = callback;
    console.log('特征值变化回调已设置');
  },
  writeBLECharacteristicValue: (obj) => {
    // 提取传入的参数
    const { deviceId, serviceId, characteristicId, value } = obj;
    
    // 校验value是否为ArrayBuffer类型
    if (!(value instanceof ArrayBuffer)) {
      const errorCode = 10012;
      const errorMsg = '写入失败，value必须是ArrayBuffer类型';
      console.error(errorMsg);
      if (obj.fail) obj.fail({ errorMsg }, errorCode);
      if (obj.complete) obj.complete();
      return;
    }
    
    // 模拟写入特征值
    console.log(`正在向设备 ${deviceId} 服务 ${serviceId} 特征值 ${characteristicId} 写入数据`);
    console.log(`写入数据长度: ${value.byteLength} 字节`);
    
    // 模拟解析写入的十六进制数据（仅日志展示）
    const hexData = bluetooth.ab2hex(value);
    console.log(`写入的十六进制数据: ${hexData}`);
    
    // 随机决定操作是否成功（模拟80%成功率）
    const isSuccess = Math.random() > 0.2;
    
    setTimeout(() => {
      if (isSuccess) {
        console.log(`成功向设备 ${deviceId} 写入特征值 ${characteristicId}`);
        if (obj.success) obj.success();
      } else {
        const errorCode = 10013;
        const errorMsg = '写入特征值失败，特征值可能不支持写入或设备断开连接';
        console.log(`写入特征值失败，错误码: ${errorCode}`);
        if (obj.fail) obj.fail({ errorMsg }, errorCode);
      }
      
      if (obj.complete) {
        obj.complete();
      }
    }, 1200);
  },
  
  // 新增：启用/禁用特征值通知
  notifyBLECharacteristicValueChange: (obj) => {
    const { deviceId, serviceId, characteristicId, state } = obj;
    
    console.log(`${state ? '启用' : '禁用'} 设备 ${deviceId} 服务 ${serviceId} 特征值 ${characteristicId} 的通知`);
    
    // 随机决定操作是否成功（模拟90%成功率）
    const isSuccess = Math.random() > 0.1;
    
    setTimeout(() => {
      if (isSuccess) {
        console.log(`${state ? '启用' : '禁用'} 通知成功`);
        if (obj.success) obj.success();
      } else {
        const errorCode = 10014;
        const errorMsg = `${state ? '启用' : '禁用'} 通知失败，特征值可能不支持通知或设备断开连接`;
        console.log(`操作失败，错误码: ${errorCode}`);
        if (obj.fail) obj.fail({ errorMsg }, errorCode);
      }
      
      if (obj.complete) {
        obj.complete();
      }
    }, 1000);
  },
  onBLECharacteristicValueChange: (callback) => {
    if (typeof callback !== 'function') {
      console.error('onBLECharacteristicValueChange: callback必须是函数');
      return;
    }
    
    // 封装ab2hex工具方法
    const ab2hex = (buffer) => {
      const hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        (bit) => ('00' + bit.toString(16)).slice(-2)
      );
      return hexArr.join('');
    };
    
    // 创建私有回调数组
    const callbacks = [];
    
    // 包装原始回调，添加ab2hex方法
    const wrappedCallback = (data) => {
      // 为数据添加hexValue属性
      const enrichedData = {
        ...data,
        hexValue: ab2hex(data.value)
      };
      
      callback(enrichedData);
    };
    
    // 添加到回调数组
    callbacks.push(wrappedCallback);
    console.log('特征值变化回调已注册');
    
    // 返回一个取消注册的函数
    return () => {
      const index = callbacks.indexOf(wrappedCallback);
      if (index !== -1) {
        callbacks.splice(index, 1);
        console.log('特征值变化回调已取消注册');
      }
    };
  },
  
  // 模拟特征值变化（仅用于测试）
  _simulateCharacteristicValueChange: (deviceId, serviceId, characteristicId, value) => {
    const data = {
      deviceId,
      serviceId,
      characteristicId,
      value
    };
    
    console.log(`模拟特征值变化: ${characteristicId} = ${bluetooth.ab2hex?.(value) || '[unknown]'}`);
    
    // 调用所有注册的回调
    if (typeof bluetooth.onBLECharacteristicValueChange.callbacks !== 'undefined') {
      bluetooth.onBLECharacteristicValueChange.callbacks.forEach(cb => {
        cb(data);
      });
    }
  },
  onBLEConnectionStateChange: (callback) => {
    if (typeof callback !== 'function') {
      console.error('onBLEConnectionStateChange: callback必须是函数');
      return;
    }
    
    // 创建私有回调数组
    const callbacks = [];
    
    // 添加到回调数组
    callbacks.push(callback);
    console.log('连接状态变化回调已注册');
    
    // 返回一个取消注册的函数
    return () => {
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
        console.log('连接状态变化回调已取消注册');
      }
    };
  },
  
  // 模拟连接状态变化（仅用于测试）
  _simulateConnectionStateChange: (deviceId, connected) => {
    const data = {
      deviceId,
      connected
    };
    
    console.log(`模拟连接状态变化: ${deviceId} 连接状态=${connected}`);
    
    // 注意：由于回调数组已封装在内部，这里无法直接访问
    // 实际应用中需要调整设计或使用其他方式触发事件
    console.log('警告：无法触发连接状态变化事件，回调数组已封装在内部');
  }
}

module.exports = bluetooth