<template>
  <transition name="fade">
    <div v-if="visible" class="message" :class="type">
      {{ message }}
    </div>
  </transition>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';

  const visible = ref(false);
  const message = ref('');
  const type = ref('');

  const show = (msg: string, msgType = 'info', time = 3000) => {
    message.value = msg;
    type.value = msgType;
    visible.value = true;
    setTimeout(() => {
      visible.value = false;
    }, time);
  };
  defineExpose({
    show,
  });
</script>

<style scoped>
  .message {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 4px 8px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 1000;
  }

  .message.info {
    background-color: #2196f3;
  }

  .message.success {
    background-color: #4caf50;
  }

  .message.warning {
    background-color: #ff9800;
  }

  .message.error {
    background-color: #f44336;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s;
  }

  .fade-enter,
  .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
    opacity: 0;
  }
</style>
