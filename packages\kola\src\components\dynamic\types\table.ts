import { Component } from 'vue';

export type Filter = Record<string, any>;

export type Pagination = {
  current: number;
  pageSize: number;
  showPageSize: boolean;
  showTotal: boolean;
  total: number;
  showJumper: boolean;
  pageSizeOptions: number[];
};

export type requestPagination = {
  pageNum: number;
  pageSize: number;
};

export type Column = {
  title: string;
  dataIndex: string;
  width?: number;
  minWidth?: number;
  fixed?: 'left' | 'right';
  headerToolTip?: string;
  ellipsis?: boolean;
  tooltip?: boolean | object;
  render?: ({ column, record, rowIndex }) => any;
  customRender?: {
    type: string | Component;
    props?: Record<string, any>;
  };
  sortable?: {
    sortDirections?: ('ascend' | 'descend')[];
    sorter?: boolean;
    sortOrder?: 'ascend' | 'descend' | '';
    defaultSortOrder?: 'ascend' | 'descend' | '';
  };
};

export type DTable = {
  columns: Column[];
  props?: Record<string, any>; // table props 扩展字段
  isPageable?: boolean;
  virtualHeight?: number;
  pagination?: Partial<Pagination>;
  loadMore?: (record, done) => void;
  checkStrictly?: boolean;
  showCheckedAll?: boolean;
  topComponent?: Component;
  load: {
    action: (
      filter: Filter,
      pagination: requestPagination,
      getCustomParams?: () => any
    ) => Promise<{
      data: {
        totalCount: number;
        pageSize: number;
        pageNo: number;
        list: Record<string, any>[];
      };
    }>;
  };
};

export type RefreshTable = (params?: { resetPagination: boolean; resetPageSize?: boolean }) => void;

export type Sorter = {
  sortBy: string;
  sortDirection: string;
};
