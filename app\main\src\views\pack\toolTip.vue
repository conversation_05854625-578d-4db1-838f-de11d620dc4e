<template>
  <div>
    <div v-if="record.status === 'pending'" class="packing">
      <icon-sync class="rotate-element" />
      打包中
    </div>
    <div v-else-if="record.status === 'success'" class="pack-success">
      <icon-face-smile-fill />
      成功
    </div>
    <div v-else class="pack-failure">
      <a-tooltip :content="record.failedResult || '-'">
        <span>
          <icon-face-frown-fill />
        失败
        </span>
      </a-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps<{
    content: string;
    record: any;
  }>();
</script>

<style lang="less" scoped>
  .packing {
    color: rgb(var(--arcoblue-5));
  }

  .pack-success {
    color: rgb(var(--green-6));
  }

  .pack-failure {
    color: rgb(var(--red-6));
  }

  .rotate-element {
    animation: rotate 2s linear infinite; /* 2秒完成一次旋转，无限循环 */
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg); /* 起始角度 */
    }
    to {
      transform: rotate(360deg); /* 结束角度 */
    }
  }
</style>
