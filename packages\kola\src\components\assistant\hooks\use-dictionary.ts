import { isEmpty } from 'lodash';
import { ref } from 'vue';
import websocketApi from '../apis';
import { generateRequestId } from '../utils/uuid';

const dictionary = ref<any>({});
const isRequestSent = ref(false);

const useDictionary = () => {
  const queryDictionary = () => {
    if (!isRequestSent.value) {
      isRequestSent.value = true;
      websocketApi.send({
        messageType: 'onDictionary',
        requestId: generateRequestId(),
        data: {},
      });
      websocketApi.on('onDictionary', (res) => {
        const { code, data } = res;
        if (code === 0) {
          dictionary.value = data;
          isRequestSent.value = false;
        }
      });
    }
  };

  const getDictionary = () => {
    if (isEmpty(dictionary.value) && !isRequestSent.value) {
      queryDictionary();
    }
    return dictionary;
  };

  return {
    getDictionary,
  };
};

export default useDictionary;
