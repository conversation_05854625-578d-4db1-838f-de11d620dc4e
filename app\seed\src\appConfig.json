{"meta": {"name": "壁纸大全", "package": "com.njnd.wallpaper", "logoUrl": "https://gitlab.ghfkj.cn/uploads/-/system/user/avatar/45/avatar.png", "tabs": [{"name": "首页", "pageName": "Home", "icon": "https://fre.ghfkj.cn/quickapp/app/bzdq/icon-0.png", "activeIcon": "https://fre.ghfkj.cn/quickapp/app/bzdq/icon-0-selected.png"}, {"name": "清理", "pageName": "Clear", "icon": "https://fre.ghfkj.cn/quickapp/app/bzdq/icon-1.png", "activeIcon": "https://fre.ghfkj.cn/quickapp/app/bzdq/icon-1-selected.png"}, {"name": "我的", "pageName": "User", "icon": "https://fre.ghfkj.cn/quickapp/app/bzdq/icon-3.png", "activeIcon": "https://fre.ghfkj.cn/quickapp/app/bzdq/icon-3-selected.png"}], "pages": [{"name": "设置页", "pageName": "Setting", "path": "pages/Setting"}, {"name": "详情页", "pageName": "Detail", "path": "pages/Detail"}, {"name": "图片页", "pageName": "UserImg", "path": "pages/UserImg"}, {"name": "清理详情页", "pageName": "ClearDetail", "path": "pages/ClearDetail"}], "agreementUrl": "https://fre.ghfkj.cn/quickapp/app/meihaobizhi/UserAgreement.html", "privacyUrl": "https://fre.ghfkj.cn/quickapp/app/meihaobizhi/privacy.html"}, "pages": [{"meta": {"name": "首页", "pageName": "Home", "path": "pages/Main?activeTab=0"}, "components": [{"name": "bannerSwiper", "props": {"autoPlay": true, "imgList": ["https://devbar.tianyaoguan.cn/bzdq/banner.png?x-oss-process=image/resize,w_691,h_250", "https://devbar.tianyaoguan.cn/bzdq/banner.png?x-oss-process=image/resize,w_691,h_250", "https://devbar.tianyaoguan.cn/bzdq/banner.png?x-oss-process=image/resize,w_691,h_250"]}}]}, {"meta": {"name": "清理", "pageName": "Clear", "path": "pages/Main?activeTab=1"}, "components": []}, {"meta": {"name": "我的", "pageName": "User", "path": "pages/Main?activeTab=2"}, "components": []}, {"meta": {"name": "设置页", "pageName": "Setting", "path": "pages/Setting"}, "components": []}, {"meta": {"name": "详情页", "pageName": "Detail", "path": "pages/Detail"}, "components": []}, {"meta": {"name": "图片页", "pageName": "UserImg", "path": "pages/UserImg"}, "components": []}, {"meta": {"name": "清理详情页", "pageName": "ClearDetail", "path": "pages/ClearDetail"}, "components": []}]}