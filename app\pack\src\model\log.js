const mongoose = require('mongoose');
const logSchema = new mongoose.Schema(
  {
    // 用户名
    username: {
      type: String,
      required: true,
    },
    // 用户角色（例如：admin, user）
    role: {
      type: String,
      required: true,
      default: 'user', // 默认角色为 user
    },
    requestApi:{
      type: String,
    },
    requestParams: {
      type: String,
    },
    // 最后登录时间
    lastLogin: {
      type: Date,
    },
  },
  {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
  }
);


// 创建日志模型
const Log = mongoose.model('Log', logSchema);

module.exports = Log;