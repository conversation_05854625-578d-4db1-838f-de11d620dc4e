const cheerio = require('cheerio');
const forHandler = require('./tasks/for');
const ifHandler = require('./tasks/if');
const bindHandler = require('./tasks/bind');
const styleHandler = require('./tasks/style');
const textHandler = require('./tasks/text');

function templateRevert(templateContent) {
  const $ = cheerio.load(templateContent, { xmlMode: true, decodeEntities: false });

  forHandler($);
  ifHandler($);
  bindHandler($);
  styleHandler($, 'style');
  styleHandler($, 'class');

  // 组件替换
  textHandler($);

  // 生成转换后的内容
  let convertedContent = $.html();
  convertedContent = convertedContent.replace(/&#x24;/g, '$');
  convertedContent = convertedContent.replace(/&gt;/g, '>');
  convertedContent = convertedContent.replace(/v-else=""/g, 'v-else');

  return convertedContent;
}

function templateRevertV2(templateContent) {
  return `<template>
  ${templateRevert(templateContent)}
</template>`;
}

module.exports = {
  templateRevert,
  templateRevertV2,
};
