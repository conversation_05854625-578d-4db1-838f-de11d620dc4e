const { fork } = require('child_process');

class ProcessPool {
  constructor(maxProcesses) {
    this.maxProcesses = maxProcesses; // 最大进程数
    this.taskQueue = []; // 待处理任务队列
    this.activeProcesses = []; // 当前活跃的进程
  }

  runTask(workerData, workerFile) {
    return new Promise((resolve, reject) => {
      const task = { workerData, workerFile, resolve, reject };
      this.taskQueue.push(task);
      this.scheduleTask();
    });
  }

  scheduleTask() {
    if (this.activeProcesses.length < this.maxProcesses && this.taskQueue.length > 0) {
      const { workerData, workerFile, resolve, reject } = this.taskQueue.shift();
      const child = fork(workerFile);

      this.activeProcesses.push(child);

      child.send(workerData);

      child.on('message', (result) => {
        resolve(result);
        this.removeProcess(child);
      });

      child.on('error', (err) => {
        reject(err);
        this.removeProcess(child);
      });

      child.on('exit', (code) => {
        if (code !== 0) {
          reject(new Error(`Process exited with code ${code}`));
        }
      });
    }
  }

  removeProcess(child) {
    this.activeProcesses = this.activeProcesses.filter((proc) => proc !== child);
    this.scheduleTask();
  }
}

module.exports = ProcessPool;
