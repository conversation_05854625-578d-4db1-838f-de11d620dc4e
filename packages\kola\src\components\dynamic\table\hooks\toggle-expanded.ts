import { ref } from 'vue';
import { flatMap } from 'lodash';

function getIdsFromTree(tree) {
  return flatMap(tree, (node) => {
    // 添加当前节点的 id 到结果数组
    const isChildren = Array.isArray(node.children) && node.children.length > 0;
    const ids = isChildren ? [node.id] : [];

    // 如果节点有子节点，递归调用 getIdsFromTree
    if (isChildren) {
      ids.push(...getIdsFromTree(node.children));
    }

    return ids;
  });
}

const useToggleExpanded = (tableData) => {
  const expandedKeys = ref<number[]>([]);

  function changeTableExpanded({ isExpanded }) {
    expandedKeys.value = isExpanded ? getIdsFromTree(tableData.value) : [];
  }

  return {
    expandedKeys,
    changeTableExpanded,
  };
};

export default useToggleExpanded;
