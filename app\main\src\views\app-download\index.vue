<template>
  <div class="page">
    <div class="wrapper">
      <a-typography>
        <a-typography-title class="h1"> APP 下载</a-typography-title>
        <a-typography-paragraph>
          <div class="search">
            <div style="margin-right: 24px; flex: none">应用名/包名</div>
            <div class="input-wrapper">
              <a-input
                v-model="keyword"
                class="search-input"
                @press-enter="handleSearch"
                @focus="
                  () => {
                    visible = true;
                  }
                "
                @blur="handleBlur"
              >
              </a-input>
              <div
                v-if="visible"
                class="history-list"
                @mousedown.stop="tagClicked = true"
              >
                <a-tag
                  v-for="(item, index) in historyList"
                  :key="index"
                  :closable="true"
                  class="history-tag"
                  size="large"
                  @click="handleSelectHistory(item)"
                  @close="handleTagClose(index)"
                  >{{ item.name }}
                  <template v-if="item.icon" #icon>
                    <div style="display: flex; align-items: center">
                      <img
                        style="
                          width: 14px;
                          height: 14px;
                          vertical-align: baseline;
                          border-radius: 2px;
                        "
                        :src="item.icon"
                      />
                    </div>
                  </template>
                </a-tag>
              </div>
            </div>

            <a-button
              style="margin-left: 24px; flex: 0"
              type="primary"
              @click="handleSearch"
              >搜索
            </a-button>
          </div>
          <div class="title">精准(包名)匹配</div>
          <div class="content">
            <div class="card-wrapper">
              <a-card
                class="download-card"
                title="VIVO"
                :bordered="false"
                :body-style="{ padding: 0 }"
              >
                <div v-if="vivoList.length === 0">
                  <a-empty />
                </div>
                <div v-else>
                  <a-list :data="vivoList" :bordered="false">
                    <template #item="{ item }">
                      <a-list-item>
                        <template #actions>
                          <div class="action-list">
                            <a-button
                              type="text"
                              size="small"
                              @click="handleShowModal(item)"
                              >详情
                            </a-button>
                            <a-button
                              type="text"
                              size="small"
                              @click="handleDownloadVivo(item)"
                              >下载
                            </a-button>
                          </div>
                        </template>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar shape="square">
                              <img alt="avatar" :src="item.icon" />
                            </a-avatar>
                          </template>
                          <template #description>
                            <div class="des" style="font-weight: 700">
                              {{ item.rpkName }}
                            </div>
                            <div class="des">
                              版本号：{{ item.versionName }}
                            </div>
                            <div class="des"> 包名：{{ item.rpkPackage }}</div>
                            <div class="des line" :title="item.description">
                              描述：{{ item.description }}
                            </div>
                          </template>
                        </a-list-item-meta>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>
              </a-card>
              <a-card
                class="download-card"
                title="荣耀"
                :bordered="false"
                :body-style="{ padding: 0 }"
              >
                <div v-if="honorList.length === 0">
                  <a-empty />
                </div>
                <div v-else>
                  <a-list :data="honorList" :bordered="false">
                    <template #item="{ item }">
                      <a-list-item>
                        <template #actions>
                          <div class="action-list">
                            <a-button
                              type="text"
                              size="small"
                              @click="handleShowModal(item)"
                              >详情
                            </a-button>
                            <a-button
                              type="text"
                              size="small"
                              @click="handleDownloadHonor(item)"
                              >下载
                            </a-button>
                          </div>
                        </template>
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar shape="square">
                              <img alt="avatar" :src="item.iconAddr" />
                            </a-avatar>
                          </template>
                          <template #description>
                            <div class="des" style="font-weight: 700">
                              {{ item.qkAppName }}
                            </div>
                            <div class="des"> 版本号：{{ item.verName }}</div>
                            <div class="des"> 包名：{{ item.pkgName }}</div>
                            <div class="des line" :title="item.briefIntro">
                              描述：{{ item.briefIntro }}
                            </div>
                          </template>
                        </a-list-item-meta>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>
              </a-card>
              <div class="download-card"></div>
            </div>
          </div>
          <div class="title">模糊匹配</div>
          <div class="content">
            <div id="sss" ref="cardWrapperEl" style="overflow: hidden">
              <div ref="cardEl" class="card-wrapper">
                <a-card
                  class="download-card"
                  title="小米"
                  :bordered="false"
                  :body-style="{ padding: 0 }"
                >
                  <div v-if="xmList.length === 0">
                    <a-empty />
                  </div>
                  <div v-else>
                    <a-list :data="xmList" :bordered="false">
                      <template #item="{ item }">
                        <a-list-item>
                          <template #actions>
                            <div class="action-list">
                              <a-button
                                type="text"
                                size="small"
                                @click="handleShowModal(item)"
                                >详情
                              </a-button>
                              <a-button
                                type="text"
                                size="small"
                                @click="handleDownloadXm(item.packageName)"
                                >下载
                              </a-button>
                            </div>
                          </template>
                          <a-list-item-meta>
                            <template #avatar>
                              <a-avatar shape="square">
                                <img alt="avatar" :src="item.icon" />
                              </a-avatar>
                            </template>
                            <template #description>
                              <div class="des" style="font-weight: 700">
                                {{ item.appName }}
                              </div>
                              <div class="des">
                                版本号：{{ item.appVersionName }}
                              </div>
                              <div class="des">
                                包名：{{ item.packageName }}
                              </div>
                              <div class="des line" :title="item.description">
                                描述：{{ item.description }}
                              </div>
                            </template>
                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </a-card>
                <a-card
                  class="download-card"
                  title="OPPO"
                  :bordered="false"
                  :body-style="{ padding: 0 }"
                >
                  <div v-if="oppoList.length === 0">
                    <a-empty />
                  </div>
                  <div v-else>
                    <a-list :data="oppoList" :bordered="false">
                      <template #item="{ item }">
                        <a-list-item>
                          <template #actions>
                            <div class="action-list">
                              <a-button
                                type="text"
                                size="small"
                                @click="handleShowModal(item)"
                                >详情
                              </a-button>
                              <a-button
                                type="text"
                                size="small"
                                @click="handleDownloadOppo(item)"
                                >下载
                              </a-button>
                            </div>
                          </template>
                          <a-list-item-meta>
                            <template #avatar>
                              <a-avatar shape="square">
                                <img alt="avatar" :src="item.iconUrl" />
                              </a-avatar>
                            </template>
                            <template #description>
                              <div class="des" style="font-weight: 700">
                                {{ item.name }}
                              </div>
                              <div class="des"> 版本号：{{ item.vName }}</div>
                              <div class="des"> 包名：{{ item.pkgName }}</div>
                              <div class="des line" :title="item.description">
                                描述：{{ item.description }}
                              </div>
                            </template>
                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </a-card>
                <a-card
                  class="download-card"
                  title="华为"
                  :bordered="false"
                  :body-style="{ padding: 0 }"
                >
                  <div v-if="hwList.length === 0">
                    <a-empty />
                  </div>
                  <div v-else>
                    <a-list :data="hwList" :bordered="false">
                      <template #item="{ item }">
                        <a-list-item>
                          <template #actions>
                            <div class="action-list">
                              <a-button
                                type="text"
                                size="small"
                                @click="handleShowModal(item)"
                                >详情
                              </a-button>
                              <a-button
                                type="text"
                                size="small"
                                @click="handleDownloadHw(item)"
                                >下载
                              </a-button>
                            </div>
                          </template>
                          <a-list-item-meta>
                            <template #avatar>
                              <a-avatar shape="square">
                                <img alt="avatar" :src="item.icon" />
                              </a-avatar>
                            </template>
                            <template #description>
                              <div class="des" style="font-weight: 700">
                                {{ item.name }}
                              </div>
                              <div class="des">
                                版本号：{{ item.appVersionName }}
                              </div>
                              <div class="des"> 包名：{{ item.package }}</div>
                              <div class="des line" :title="item.memo">
                                描述：{{ item.memo }}
                              </div>
                            </template>
                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </a-card>
              </div>
            </div>
          </div>
        </a-typography-paragraph>
      </a-typography>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, h, onMounted } from 'vue';
  import downloadAPi from '@/api/download';
  import { Modal, Descriptions } from '@arco-design/web-vue';

  // 修改historyList为对象数组，包含name和icon属性
  interface HistoryItem {
    name: string;
    icon?: string;
  }

  const historyList = ref<HistoryItem[]>([]);
  const visible = ref(false);
  const tagClicked = ref(false);
  const keyword = ref('');
  const xmList = ref<any[]>([]);
  const oppoList = ref<any[]>([]);
  const vivoList = ref<any[]>([]);
  const honorList = ref<any[]>([]);
  const hwList = ref<any[]>([]);
  const cardEl = ref<any>(null);
  const cardWrapperEl = ref<any>(null);

  // 全局常量，用于localStorage键
  const HISTORY_KEY = 'inputHistoryV2';

  // 从 localStorage 加载历史记录
  onMounted(() => {
    const savedHistory = localStorage.getItem(HISTORY_KEY);
    if (savedHistory) {
      try {
        const parsed = JSON.parse(savedHistory);
        // 兼容旧版本的字符串数组格式
        if (Array.isArray(parsed) && parsed.length > 0) {
          if (typeof parsed[0] === 'string') {
            // 转换旧格式为新格式
            historyList.value = parsed.map((item) => ({ name: item }));
          } else {
            historyList.value = parsed;
          }
        }
      } catch (e) {
        console.error('解析历史记录失败', e);
        historyList.value = [];
      }
    }
  });

  // 保存历史记录到 localStorage
  const saveHistory = () => {
    if (!keyword.value.trim()) return;

    // 检查是否已存在相同name的记录
    const existingIndex = historyList.value.findIndex(
      (item) => item.name === keyword.value
    );

    // 创建新的历史记录项
    const newItem: HistoryItem = { name: keyword.value };

    let updatedList: HistoryItem[];
    if (existingIndex > -1) {
      // 如果已存在，保留原有icon
      if (historyList.value[existingIndex].icon) {
        newItem.icon = historyList.value[existingIndex].icon;
      }
      // 移除旧项并在开头添加新项
      updatedList = [
        newItem,
        ...historyList.value
          .filter((item) => item.name !== keyword.value)
          .slice(0, 9),
      ];
    } else {
      // 如果不存在，直接添加到开头
      updatedList = [newItem, ...historyList.value.slice(0, 9)];
    }

    localStorage.setItem(HISTORY_KEY, JSON.stringify(updatedList));
    historyList.value = updatedList;
  };

  // 处理弹窗显隐变化
  const handleVisibleChange = (newVisible: boolean) => {
    visible.value = newVisible;
  };

  /**
   * @description: 更新历史记录中的图标
   * @param name 应用名称
   * @param iconUrl 图标URL
   */
  function updateHistoryIcon(name: string, iconUrl: string) {
    if (!name || !iconUrl) return;

    // 查找历史记录中是否有匹配的项
    const index = historyList.value.findIndex((item) => item.name === name);
    if (index > -1) {
      // 更新图标
      historyList.value[index].icon = iconUrl;
      // 保存到localStorage
      localStorage.setItem(HISTORY_KEY, JSON.stringify(historyList.value));
    }
  }

  /**
   * @description: vivo下载
   * @param searchKey
   */
  function handleVIVOSearch(searchKey: string) {
    downloadAPi.vivoDownload({ packageName: searchKey }).then((res) => {
      vivoList.value = res.data ? [res.data] : [];

      // 如果搜索成功且有数据，检查是否需要更新图标
      if (vivoList.value.length > 0 && vivoList.value[0].icon) {
        const item = vivoList.value[0];
        // 如果当前搜索的关键词与应用名称匹配，更新图标
        if (item.rpkName === keyword.value) {
          updateHistoryIcon(keyword.value, item.icon);
        }
      }
    });
  }

  /**
   * @description: honor下载
   * @param searchKey
   */
  function handleHonorSearch(searchKey: string) {
    downloadAPi.honorDownload({ packageName: searchKey }).then((res) => {
      honorList.value = res.data ? [res.data] : [];

      // 如果搜索成功且有数据，检查是否需要更新图标
      if (honorList.value.length > 0 && honorList.value[0].iconAddr) {
        const item = honorList.value[0];
        // 如果当前搜索的关键词与应用名称匹配，更新图标
        if (item.qkAppName === keyword.value) {
          updateHistoryIcon(keyword.value, item.iconAddr);
        }
      }
    });
  }

  /**
   * @description: 根据关键字获取包名
   * @param appList
   * @param packageKeyName
   * @param appKeyName
   */
  function packageNameTake(
    appList: any[],
    packageKeyName: string,
    appKeyName: string
  ) {
    const item = appList.find((v: any) => {
      return v[appKeyName] === keyword.value;
    });
    if (item) {
      return item[packageKeyName];
    }
    return '';
  }

  /**
   * @description: 处理下载事件
   */
  function handleSearch() {
    if (!keyword.value) {
      return;
    }

    saveHistory();

    let packageSearched = false;

    if (keyword.value.indexOf('.') > -1) {
      handleVIVOSearch(keyword.value);
      handleHonorSearch(keyword.value);
      return;
    }

    // 小米搜索
    downloadAPi.xmSearch({ packageName: keyword.value }).then((res) => {
      xmList.value = res.data || [];

      // 精准匹配，更新图标
      const exactMatch = xmList.value.find(
        (item) => item.appName === keyword.value
      );
      if (exactMatch && exactMatch.icon) {
        updateHistoryIcon(keyword.value, exactMatch.icon);
      }

      const packageName = packageNameTake(
        xmList.value,
        'packageName',
        'appName'
      );
      if (packageName && !packageSearched) {
        packageSearched = true;
        handleVIVOSearch(packageName);
        handleHonorSearch(packageName);
      }
    });

    // OPPO搜索
    downloadAPi.oppoDownload({ packageName: keyword.value }).then((res) => {
      oppoList.value = res.data || [];

      // 精准匹配，更新图标
      const exactMatch = oppoList.value.find(
        (item) => item.name === keyword.value
      );
      if (exactMatch && exactMatch.iconUrl) {
        updateHistoryIcon(keyword.value, exactMatch.iconUrl);
      }

      const packageName = packageNameTake(oppoList.value, 'pkgName', 'name');
      if (packageName && !packageSearched) {
        packageSearched = true;
        handleVIVOSearch(packageName);
        handleHonorSearch(packageName);
      }
    });

    // 华为搜索
    downloadAPi.hwDownload({ packageName: keyword.value }).then((res) => {
      hwList.value = res.data || [];

      // 精准匹配，更新图标
      const exactMatch = hwList.value.find(
        (item) => item.name === keyword.value
      );
      if (exactMatch && exactMatch.icon) {
        updateHistoryIcon(keyword.value, exactMatch.icon);
      }

      const packageName = packageNameTake(hwList.value, 'package', 'name');
      if (packageName && !packageSearched) {
        handleVIVOSearch(packageName);
        handleHonorSearch(packageName);
      }
    });
  }

  function downloadLink(link: string, filename: string) {
    fetch(link)
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      })
      .catch((error) => {
        console.error('下载失败:', error);
      });
  }

  function handleDownloadXm(packageNames: string) {
    downloadAPi.xmDownload({ packageName: packageNames }).then((res) => {
      const { link, packageName, version } = res.data;
      if (link) {
        window.open(link, '_blank');
      }
    });
  }

  function handleDownloadOppo(appInfo: any) {
    const { url, pkgName, vName } = appInfo;
    window.open(url, '_blank');
  }

  function handleDownloadVivo(appInfo: any) {
    const { rpkUrl, versionName, rpkName } = appInfo;

    window.open(rpkUrl, '_blank');
  }

  function handleDownloadHonor(appInfo: any) {
    const { rpkAddr } = appInfo;

    window.open(rpkAddr, '_blank');
  }

  function handleDownloadHw(appInfo: any) {
    const { downurl } = appInfo;

    window.open(downurl, '_blank');
  }

  function handleShowModal(info: Record<string, string>) {
    const labelValueArray = Object.entries(info).map(([key, value]) => ({
      label: key,
      value,
    }));
    Modal.info({
      title: '详细信息',
      width: 800,
      content: () => h(Descriptions, { data: labelValueArray, column: 1 }),
    });
  }

  function handleBtnClick(type: string) {
    switch (type) {
      case 'left': {
        cardEl.value.style.left = 0;
        break;
      }
      default: {
        cardEl.value.style.left = `${
          cardWrapperEl.value.clientWidth -
          (document.querySelector('.arco-card')!.clientWidth * 5 + 20 * 4)
        }px`;
      }
    }
  }

  function handleBlur() {
    setTimeout(() => {
      visible.value = false;
    }, 100);
  }

  function handleTagClose(index: number) {
    historyList.value.splice(index, 1);
    tagClicked.value = true;
    // 更新历史记录列表并保存到localStorage
    localStorage.setItem(HISTORY_KEY, JSON.stringify(historyList.value));
  }

  // 处理选择历史记录
  const handleSelectHistory = (item: HistoryItem) => {
    keyword.value = item.name;
    visible.value = false;
    handleSearch();
  };
</script>

<style scoped lang="less">
  .page {
    padding: 16px;
    background-color: rgb(245, 245, 245);
    height: 100%;
  }

  .wrapper {
    background: #fff;
  }

  .search {
    display: flex;
    width: 50%;
    margin: 0 auto;
    align-items: center;
    justify-content: center;
  }

  .h1 {
    margin: 0 auto;
    text-align: center;
    padding: 24px 0 24px;
  }

  .card-wrapper {
    display: flex;
    gap: 20px;
    flex-wrap: nowrap;
    position: relative;
    transition: all 0.3s ease-in-out;
  }

  .content {
    padding: 12px 32px 32px;
    position: relative;

    .download-card {
      width: 30%;
      flex: 1 0 auto;
    }

    .des {
      font-size: 12px;
    }

    .line {
      white-space: nowrap; /* 强制文本在一行显示 */
      overflow: hidden; /* 隐藏超出容器的内容 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }

    :deep(.arco-list-item-main) {
      overflow: hidden;
    }
  }

  .title {
    position: relative;
    font-weight: 500;
    margin: 20px 5px 20px 32px;
    padding-left: 20px;
    font-size: 16px;
    display: inline-block;

    &:before {
      position: absolute;
      content: '';
      height: 100%;
      border-left: 5px solid #2166ff;
      left: 0;
      right: 0;
    }
  }

  .action-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    justify-content: center;
  }

  .input-wrapper {
    position: relative;
    width: 100%;
  }

  .history-list {
    z-index: 100;
    position: absolute;
    top: 100%; /* 定位到父元素底部 */
    margin-top: 8px; /* 再向下移动8px */
    left: 0;
    right: 0;
    min-height: 80px;
    box-sizing: border-box;
    padding: 8px;
    background-color: var(--color-bg-popup);
    border: 1px solid var(--color-fill-3);
    border-radius: var(--border-radius-medium);
    box-shadow: 0 4px 10px #0000001a;
  }

  .history-tag {
    cursor: pointer;
    margin-bottom: 8px;
    margin-right: 8px;
  }
</style>
