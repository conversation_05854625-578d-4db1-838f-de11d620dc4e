<template>
  <a-radio-group v-model="value">
    <a-radio v-for="item in sourceData" :value="item[valueKey]" :key="item[valueKey]" :disabled="!!item.disabled">
      <template #radio="{ checked }">
        <div
          class="custom-radio-card"
          :style="{ width: card?.width }"
          :class="{
            'custom-radio-card-checked': checked,
            'custom-radio-card-disabled': item.disabled,
          }"
        >
          <template v-if="item.icon">
            <div class="custom-radio-card-icon">
              <div class="custom-radio-card-title">
                <component :is="item.icon" style="margin-right: 16px" />
                <div>
                  {{ item[labelKey] }}
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="custom-radio-card-title">
              {{ item[labelKey] }}
            </div>
            <a-typography-text type="secondary" class="custom-radio-card-subtitle">
              {{ item[subKey] }}
            </a-typography-text>
          </template>
        </div>
      </template>
      {{ item[labelKey] }}
    </a-radio>
  </a-radio-group>
</template>

<script setup lang="ts">
  import { Option } from '../../../types/form';

  defineProps<{
    sourceData: Option[];
    valueKey: string;
    labelKey: string;
    format: string;
    subKey: string;
    card?: Record<string, any>;
  }>();

  const value = defineModel<any>();
</script>

<style lang="less" scoped>
  :deep(.arco-radio-group) {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }

  .custom-radio-card {
    padding: 10px 16px;
    border: 1px solid var(--color-border-2);
    border-radius: 4px;
    width: 200px;
    height: 100px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .custom-radio-card-icon {
    display: flex;
    font-size: 16px;
    align-items: center;
    justify-content: center;
    height: 100px;

    .custom-radio-card-title {
      font-size: 18px;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .custom-radio-card-mask {
    height: 14px;
    width: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    border: 1px solid var(--color-border-2);
    box-sizing: border-box;
  }

  .custom-radio-card-mask-dot {
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }

  .custom-radio-card-title {
    color: var(--color-text-1);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
    flex: none;
  }

  .custom-radio-card:not(.custom-radio-card-disabled):hover,
  .custom-radio-card-checked:not(.custom-radio-card-disabled),
  .custom-radio-card:not(.custom-radio-card-disabled):hover .custom-radio-card-mask,
  .custom-radio-card-checked:not(.custom-radio-card-disabled) .custom-radio-card-mask {
    border-color: rgb(var(--primary-6));
  }

  .custom-radio-card-checked:not(.custom-radio-card-disabled) {
    background-color: var(--color-primary-light-1);
  }

  .custom-radio-card:not(.custom-radio-card-disabled):hover .custom-radio-card-title,
  .custom-radio-card-checked:not(.custom-radio-card-disabled) .custom-radio-card-title {
    color: rgb(var(--primary-6));
  }

  .custom-radio-card-disabled .custom-radio-card-title,
  .custom-radio-card-disabled .custom-radio-card-subtitle {
    color: var(--color-text-4);
  }
</style>
