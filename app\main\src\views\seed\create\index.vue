<template>
  <div>
    <Header :toolbar-buttons="toolbarButtons"></Header>
    <div class="lowcode-container">
      <!-- 左侧：页面区 + 组件区 -->
      <div class="left-panel">
        <div class="page-list">
          <!-- 页面区 -->
          <PageListPanel
            title="一级页面"
            :list="pageList"
            :current-page-id="currentPageId"
            @add="showAddPageModal = true"
            @select="handlePageChange"
            @delete="(page) => deletePage(page, 'main')"
          />
          <PageListPanel
            title="二级页面"
            :list="subPageList"
            :current-page-id="currentPageId"
            margin-top="24px"
            @add="showAddSubPageModal = true"
            @select="handlePageChange"
            @delete="(page) => deletePage(page, 'sub')"
          />
        </div>
        <!-- 组件区 -->
        <MaterialPanel
          :tabs="materialTabs"
          :clone-component="cloneComponent"
          :is-match="isMatch"
        />
      </div>

      <!-- 中间：渲染区 -->
      <CenterPanel
        :render-list="renderList"
        :selected-component-id="selectedComponentId"
        :page-list="pageList"
        :current-page-id="currentPageId"
        :current-page="currentPage"
        @select-component="selectComponent"
        @delete-component="deleteComponent"
        @update:render-list="handleRenderListUpdate"
        @update:current-page-id="handlePageChange"
      />

      <!-- 右侧：配置区 -->
      <RightPanel
        :selected-component="selectedComponent"
        :current-page="currentPage"
        :is-main-page="isMainPage"
        :pages-config="pagesConfig"
        @update:props="handlePropsChange"
        @update:styles="handleStyleChange"
        @update:page-config="handlePageConfigChange"
      />
      <!-- 公共弹窗 -->
      <CommonModal
        v-model:visible="showAddPageModal"
        title="新增一级页面"
        @ok="handleAddPage"
        @cancel="showAddPageModal = false"
      >
        <a-input v-model="newPageName" placeholder="请输入页面名称" />
      </CommonModal>
      <CommonModal
        v-model:visible="showClearConfirm"
        title="确认清空"
        @ok="
          renderList = [];
          showClearConfirm = false;
        "
        @cancel="showClearConfirm = false"
      >
        确认清空当前页面画布吗？
      </CommonModal>
      <CommonModal
        v-model:visible="showDeleteConfirm"
        title="确认删除"
        @ok="confirmDelete"
        @cancel="
          showDeleteConfirm = false;
          componentToDelete = null;
        "
      >
        确认删除组件 "{{ componentToDelete?.label }}" 吗？
      </CommonModal>
      <CommonModal
        v-model:visible="showModal"
        title="最终产物"
        :footer="false"
        width="600px"
        @after-open="handleEditorLayout"
      >
        <Editor
          ref="jsonEditor"
          :model-value="pagesConfigSnapshot"
          read-only
          height="400px"
        />
      </CommonModal>
      <CommonModal
        v-model:visible="showAddSubPageModal"
        title="新增二级页面"
        @ok="handleAddSubPage"
        @cancel="showAddSubPageModal = false"
      >
        <a-input v-model="newSubPageName" placeholder="请输入页面名称" />
      </CommonModal>
      <CommonModal
        v-model:visible="showSaveConfirm"
        title="确认保存"
        @ok="handleSave"
        @cancel="showSaveConfirm = false"
      >
        确认保存当前配置吗？
      </CommonModal>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch, nextTick } from 'vue';
  import { useRoute } from 'vue-router';
  import { IconDelete, IconCode, IconSave, IconDownload } from '@arco-design/web-vue/es/icon';
  import { Message, Input as AInput } from '@arco-design/web-vue';
  // api
  import SeedAppApi from '@/api/SeedApp';
  // custom components
  import Editor from '../components/editor.vue';
  import { cloneComponent, isMatch, generateTabId, transformAppData } from './utils';
  import { pageList as defaultPageList, materialTabs } from './config';
  import PageListPanel from './pages/left-panel/page-list-panel.vue';
  import MaterialPanel from './pages/material-panel/index.vue';
  import CenterPanel from './pages/center-panel/index.vue';
  import RightPanel from './pages/right-panel/index.vue';
  import CommonModal from '../components/common-modal.vue';
  import Header from './pages/header/index.vue';
  import { PagesConfig, Component } from './types';

  // pagesConfig 结构拆分为 main 和 subPages
  const pagesConfig = ref<PagesConfig>({
    name: '',
    packageName: '',
    icon: '',
    privacyUrl: '',
    agreementUrl: '',
    // 一级页面
    main: [],
    // 二级页面
    subPages: [],
  });

  // pageList
  const pageList = ref(defaultPageList);
  // subPageList
  const subPageList = ref<Array<{ id: string; name: string }>>([]);
  // global state
  const componentToDelete = ref<any>(null);
  // 当前选中的页面ID
  const currentPageId = ref<string | null>(null);
  // 当前选中的组件ID
  const selectedComponentId = ref<string | null>(null);
  // 渲染列表
  const renderList = ref<Component[]>([]);
  // modal state
  const showAddPageModal = ref(false);
  const newPageName = ref('');
  const showAddSubPageModal = ref(false);
  const newSubPageName = ref('');
  //  Editor readonly snapshot
  const pagesConfigSnapshot = ref({});
  const jsonEditor = ref(null);
  const showModal = ref(false);
  const showClearConfirm = ref(false);
  const showDeleteConfirm = ref(false);
  const showSaveConfirm = ref(false);

  watch(showModal, (val) => {
    if (val) {
      // 弹窗打开时，快照当前数据
      pagesConfigSnapshot.value = JSON.parse(JSON.stringify(pagesConfig.value));
      console.log('showModal', pagesConfig.value);
    }
  });

  const route = useRoute();
  const { _id } = route.params;

  function isMainPage(pageId: string) {
    return pageList.value.some((page) => page.id === pageId);
  }

  function isSubPage(pageId: string) {
    return subPageList.value.some((page) => page.id === pageId);
  }

  function syncRenderListToPagesConfig() {
    if (currentPageId.value) {
      if (isMainPage(currentPageId.value)) {
        const pageIndex = pagesConfig.value.main.findIndex(
          (p) => p.pageId === currentPageId.value
        );
        const pageConfig = {
          pageName:
            pageList.value.find((p) => p.id === currentPageId.value)?.name ||
            '',
          pageId: currentPageId.value,
          themeColor:
            pageIndex !== -1
              ? pagesConfig.value.main[pageIndex].themeColor
              : '#1890FF',
          backgroundColor:
            pageIndex !== -1
              ? pagesConfig.value.main[pageIndex].backgroundColor
              : '#FFFFFF',
          path: pageIndex !== -1 ? pagesConfig.value.main[pageIndex].path : '',
          components: JSON.parse(JSON.stringify(renderList.value)),
        };
        if (pageIndex !== -1) {
          pagesConfig.value.main[pageIndex] = pageConfig;
        } else {
          pagesConfig.value.main.push(pageConfig);
        }
      } else if (isSubPage(currentPageId.value)) {
        const pageIndex = pagesConfig.value.subPages.findIndex(
          (p) => p.pageId === currentPageId.value
        );
        const pageConfig = {
          pageName:
            subPageList.value.find((p) => p.id === currentPageId.value)?.name ||
            '',
          pageId: currentPageId.value,
          themeColor:
            pageIndex !== -1
              ? pagesConfig.value.subPages[pageIndex].themeColor
              : '#1890FF',
          backgroundColor:
            pageIndex !== -1
              ? pagesConfig.value.subPages[pageIndex].backgroundColor
              : '#FFFFFF',
          path:
            pageIndex !== -1 ? pagesConfig.value.subPages[pageIndex].path : '',
          components: JSON.parse(JSON.stringify(renderList.value)),
        };
        if (pageIndex !== -1) {
          pagesConfig.value.subPages[pageIndex] = pageConfig;
        } else {
          pagesConfig.value.subPages.push(pageConfig);
        }
      }
    }
  }


  // 初始化时设置 renderList 和 pagesConfig
  onMounted(async () => {
    try {
      const { data } = await SeedAppApi.getById(_id);
      console.log('原始数据:', data);
      
      // 初始化应用基本信息
      pagesConfig.value = {
        name: data.name || '',
        packageName: data.packageName || '',
        icon: data.icon || '',
        privacyUrl: data.privacyUrl || '',
        agreementUrl: data.agreementUrl || '',
        main: [],
        subPages: [],
      };

      // 初始化页面配置
      if (data?.content?.main?.length) {
        pagesConfig.value.main = data.content.main.map((page: any, index: number) => ({
          pageName: page.pageName || `页面${index + 1}`,
          pageId: page.pageId || `page_${index}`,
          themeColor: page.themeColor || '#1890FF',
          backgroundColor: page.backgroundColor || '#FFFFFF',
          path: page.path || '',
          components: page.components || [],
        }));
        console.log('主页面配置:', pagesConfig.value.main);
      }

      if (data?.content?.subPages?.length) {
        pagesConfig.value.subPages = data.content.subPages.map((page: any, index: number) => ({
          pageName: page.pageName || `子页面${index + 1}`,
          pageId: page.pageId || `subpage_${index}`,
          themeColor: page.themeColor || '#1890FF',
          backgroundColor: page.backgroundColor || '#FFFFFF',
          path: page.path || '',
          components: page.components || [],
        }));
        console.log('子页面配置:', pagesConfig.value.subPages);
      }

      // 初始化页面列表
      pageList.value = pagesConfig.value.main.map((page: any) => ({
        id: page.pageId,
        name: page.pageName,
      }));

      subPageList.value = pagesConfig.value.subPages.map((page: any) => ({
        id: page.pageId,
        name: page.pageName,
      }));

      console.log('页面列表:', pageList.value);
      console.log('子页面列表:', subPageList.value);

      // 设置当前页面为第一个页面
      if (pageList.value.length > 0) {
        currentPageId.value = pageList.value[0].id;
        const currentPage = pagesConfig.value.main.find(
          (p) => p.pageId === currentPageId.value
        );
        console.log('当前主页面:', currentPage);
        renderList.value = currentPage?.components
          ? JSON.parse(JSON.stringify(currentPage.components))
          : [];
        console.log('渲染列表:', renderList.value);
      } else if (subPageList.value.length > 0) {
        currentPageId.value = subPageList.value[0].id;
        const currentPage = pagesConfig.value.subPages.find(
          (p) => p.pageId === currentPageId.value
        );
        console.log('当前子页面:', currentPage);
        renderList.value = currentPage?.components
          ? JSON.parse(JSON.stringify(currentPage.components))
          : [];
        console.log('渲染列表:', renderList.value);
      }
    } catch (error) {
      console.error('初始化数据失败:', error);
      Message.error('初始化数据失败');
    }
  });

  // 选中的组件
  const selectedComponent = computed(() => {
    return renderList.value.find(
      (item) => item.id === selectedComponentId.value
    );
  });

  // 当前页面信息
  const currentPage = computed(() => {
    if (!currentPageId.value) return null;
    const page =
      pageList.value.find((p) => p.id === currentPageId.value) ||
      subPageList.value.find((p) => p.id === currentPageId.value);
    return page || null;
  });

  // 选择组件
  function selectComponent(component) {
    selectedComponentId.value = component.id;
  }

  // 删除组件
  function deleteComponent(component) {
    componentToDelete.value = component;
    showDeleteConfirm.value = true;
  }


  // 确认删除
  function confirmDelete() {
    if (!componentToDelete.value) return;
    
    const index = renderList.value.findIndex(
      (item) => item.id === componentToDelete.value.id
    );
    if (index !== -1) {
      renderList.value = renderList.value.filter(
        (item) => item.id !== componentToDelete.value.id
      );
      if (selectedComponentId.value === componentToDelete.value.id) {
        selectedComponentId.value = null;
      }
    }
    showDeleteConfirm.value = false;
    componentToDelete.value = null;
    syncRenderListToPagesConfig();
  }

  // 处理页面配置变更
  function handlePageConfigChange(value: any) {
    console.log('handlePageConfigChange value:', value);
    if (currentPage.value) {
      // 更新当前页面配置
      Object.assign(currentPage.value, value);

      // 同步到pagesConfig
      if (isMainPage(currentPage.value.id)) {
        const mainPageIndex = pagesConfig.value.main.findIndex(
          (page) => page.pageId === currentPage.value.id
        );
        if (mainPageIndex !== -1) {
          pagesConfig.value.main[mainPageIndex] = {
            ...pagesConfig.value.main[mainPageIndex],
            ...value,
          };
        }
      } else if (isSubPage(currentPage.value.id)) {
        const subPageIndex = pagesConfig.value.subPages.findIndex(
          (page) => page.pageId === currentPage.value.id
        );
        if (subPageIndex !== -1) {
          pagesConfig.value.subPages[subPageIndex] = {
            ...pagesConfig.value.subPages[subPageIndex],
            ...value,
          };
        }
      }
      // 同步组件列表
      syncRenderListToPagesConfig();
    }
  }

  // 处理样式变更
  function handleStyleChange(value) {
    if (selectedComponent.value) {
      selectedComponent.value.styles = value;
      syncRenderListToPagesConfig();
    }
  }

  function handlePropsChange(value) {
    if (selectedComponent.value) {
      selectedComponent.value.props = value;
      syncRenderListToPagesConfig();
    }
  }

  // 工具栏按钮配置
  const toolbarButtons = [
    {
      key: 'preview',
      type: 'primary',
      title: '预览',
      icon: IconCode,
      disabled: false,
      handler: () => {
        showModal.value = true;
      },
    },
    {
      key: 'build',
      type: 'primary',
      title: '构建',
      icon: IconDownload,
      disabled: false,
      handler: async () => {
        try {
          // 确保当前页面数据已同步
          SeedAppApi.runSeedApp(_id)
          Message.success('构建成功')
        } catch (err: any) {
          console.error('构建失败:', err);
          Message.error(`构建失败: ${err.message || JSON.stringify(err)}`);
        }
      },
    },
    {
      key: 'clear',
      type: 'primary',
      title: '清空',
      icon: IconDelete,
      disabled: false,
      handler: () => {
        showClearConfirm.value = true;
      },
    },
    {
      key: 'save',
      type: 'primary',
      title: '保存',
      icon: IconSave,
      disabled: false,
      handler: () => {
        showSaveConfirm.value = true;
      },
    },
  ];

  function handlePageChange(pageId) {
    // 保存当前页面的组件数据
    if (currentPageId.value) {
      if (isMainPage(currentPageId.value)) {
        const pageIndex = pagesConfig.value.main.findIndex(
          (p) => p.pageId === currentPageId.value
        );
        if (pageIndex !== -1) {
          pagesConfig.value.main[pageIndex].components = JSON.parse(
            JSON.stringify(renderList.value)
          );
        }
      } else if (isSubPage(currentPageId.value)) {
        const pageIndex = pagesConfig.value.subPages.findIndex(
          (p) => p.pageId === currentPageId.value
        );
        if (pageIndex !== -1) {
          pagesConfig.value.subPages[pageIndex].components = JSON.parse(
            JSON.stringify(renderList.value)
          );
        }
      }
    }
    // 切换页面
    currentPageId.value = pageId;
    // 恢复新页面的组件数据
    if (isMainPage(pageId)) {
      const targetPage = pagesConfig.value.main.find(
        (p) => p.pageId === pageId
      );
      renderList.value = targetPage?.components
        ? JSON.parse(JSON.stringify(targetPage.components))
        : [];
    } else if (isSubPage(pageId)) {
      const targetPage = pagesConfig.value.subPages.find(
        (p) => p.pageId === pageId
      );
      renderList.value = targetPage?.components
        ? JSON.parse(JSON.stringify(targetPage.components))
        : [];
    }
    selectedComponentId.value = null;
  }

  // 在所有需要同步的地方调用 syncRenderListToPagesConfig()
  function handleRenderListUpdate(val) {
    renderList.value = val;
    syncRenderListToPagesConfig();
  }

  function handleEditorLayout() {
    nextTick(() => {
      if (jsonEditor.value?.layout) jsonEditor.value.layout();
    });
  }

  // 新增页面
  function handleAddPage() {
    if (!newPageName.value) {
      Message.warning('请输入页面名称');
      return;
    }
    const tabId = generateTabId();
    const newPage = {
      id: tabId,
      name: newPageName.value,
    };
    // 根据当前选中的页面类型决定添加到哪个列表
    if (currentPageId.value) {
      if (isMainPage(currentPageId.value, pageList.value)) {
        pageList.value.push(newPage);
        // 初始化新页面的配置
        pagesConfig.value.main.push({
          pageName: newPageName.value,
          pageId: tabId,
          themeColor: '#1890FF',
          backgroundColor: '#FFFFFF',
          path: `/${newPageName.value.toLowerCase()}`,
          components: [],
        });
      } else {
        subPageList.value.push(newPage);
        // 初始化新页面的配置
        pagesConfig.value.subPages.push({
          pageName: newPageName.value,
          pageId: tabId,
          themeColor: '#1890FF',
          backgroundColor: '#FFFFFF',
          path: `/${newPageName.value.toLowerCase()}`,
          components: [],
        });
      }
    } else {
      // 如果没有选中页面，默认添加到一级页面
      pageList.value.push(newPage);
      // 初始化新页面的配置
      pagesConfig.value.main.push({
        pageName: newPageName.value,
        pageId: tabId,
        themeColor: '#1890FF',
        backgroundColor: '#FFFFFF',
        path: `/${newPageName.value.toLowerCase()}`,
        components: [],
      });
    }
    // 切换到新页面
    currentPageId.value = tabId;
    renderList.value = [];
    // 关闭弹窗
    showAddPageModal.value = false;
    newPageName.value = '';
  }

  function handleAddSubPage() {
    if (!newSubPageName.value.trim()) {
      Message.error('页面名称不能为空');
      return;
    }
    const id = generateTabId();
    subPageList.value.push({ id, name: newSubPageName.value.trim() });
    pagesConfig.value.subPages[id] = [];
    showAddSubPageModal.value = false;
    newSubPageName.value = '';
  }

  function deletePage(page, type) {
    if (type === 'main') {
      const idx = pageList.value.findIndex((p) => p.id === page.id);
      if (idx !== -1) {
        pageList.value.splice(idx, 1);
        // 从 pagesConfig.main 数组中删除对应的页面配置
        const configIdx = pagesConfig.value.main.findIndex((p) => p.pageId === page.id);
        if (configIdx !== -1) {
          pagesConfig.value.main.splice(configIdx, 1);
        }
        if (currentPageId.value === page.id) {
          currentPageId.value = pageList.value.length
            ? pageList.value[0].id
            : null;
        }
      }
    } else if (type === 'sub') {
      const idx = subPageList.value.findIndex((p) => p.id === page.id);
      if (idx !== -1) {
        subPageList.value.splice(idx, 1);
        // 从 pagesConfig.subPages 数组中删除对应的页面配置
        const configIdx = pagesConfig.value.subPages.findIndex((p) => p.pageId === page.id);
        if (configIdx !== -1) {
          pagesConfig.value.subPages.splice(configIdx, 1);
        }
        if (currentPageId.value === page.id) {
          currentPageId.value = pageList.value.length
            ? pageList.value[0].id
            : null;
        }
      }
    }
  }

  // 处理保存操作
  async function handleSave() {
    try {
      // 确保当前页面数据已同步
      syncRenderListToPagesConfig();
      
      // 获取当前数据
      const { data } = await SeedAppApi.getById(_id);
      
      // 准备更新的数据
      const updateData = {
        ...data,
        name: pagesConfig.value.name,
        packageName: pagesConfig.value.packageName,
        icon: pagesConfig.value.icon,
        private: data.private, // 保持原有的 private 字段
        agreement: data.agreement, // 保持原有的 agreement 字段
        content: {
          main: pagesConfig.value.main,
          subPages: pagesConfig.value.subPages,
        }
      };
      
      // 调用更新 API
      await SeedAppApi.update(_id, updateData);
      Message.success('保存成功');
      showSaveConfirm.value = false;
    } catch (err: any) {
      console.error('保存失败:', err);
      Message.error(`保存失败: ${err.message || JSON.stringify(err)}`);
    }
  }

  // 导出到剪贴板
  async function exportToClipboard() {
    try {
      // 确保当前页面数据已同步
      syncRenderListToPagesConfig();
      
      // 转换数据格式
      const transformedData = transformAppData(pagesConfig.value);
      
      // 复制到剪贴板
      await navigator.clipboard.writeText(JSON.stringify(transformedData, null, 2));
      
      Message.success('数据已导出到剪贴板');
    } catch (err: any) {
      console.error('导出失败:', err);
      Message.error(`导出失败: ${err.message || JSON.stringify(err)}`);
    }
  }
</script>

<style lang="less" scoped>
  @import './styles/variables.less';

  .lowcode-container {
    display: flex;
    height: @panel-height;
    background: @center-bg;

    :deep(.arco-btn) {
      background: none;
    }

    :deep(.arco-btn-primary) {
      color: @button-icon-color;
    }

    .left-panel {
      width: @left-panel-width;
      display: flex;
      flex-direction: column;
      border-right: 1px solid @border;
      background: @page-bg;

      .page-list {
        flex: none;
        height: @page-list-height;
        padding: @left-panel-margin-top 16px 16px 16px;
        border-bottom: 1px solid @border;
        background: @page-bg;

        :deep(> div:first-of-type) {
          > div:first-of-type {
            min-height: 40px;
            border-bottom: 1px solid @primary-page-bottom-color;
          }
        }

        :deep(.mock-page-list) {
          margin-top: 10px;
        }
      }
    }

    .right-panel {
      padding: @right-panel-margin-top 16px 16px 16px;

      :deep(h3:first-of-type) {
        margin-block-start: 0px;
      }
    }
  }
</style>

<style src="./styles/common.less"></style>
