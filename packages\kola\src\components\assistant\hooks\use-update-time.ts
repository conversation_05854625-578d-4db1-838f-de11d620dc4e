import { ref, onMounted, onUnmounted } from 'vue';
import { formatTimeToDiff } from '../utils/time';

const timeStampMap = ref({});

export const useUpdateTime = () => {
  const addTimeStamp = (id: number) => {
    timeStampMap.value[id] = formatTimeToDiff(id);
  };

  const updateTimeStamp = (id: number) => {
    timeStampMap.value[id] = formatTimeToDiff(id);
  };

  const getTimeStampMap = () => {
    return timeStampMap;
  };

  // 定时器 ID
  let timerId;

  // 在组件挂载时启动定时器
  onMounted(() => {
    timerId = setInterval(() => {
      Object.keys(timeStampMap.value).forEach((id) => {
        updateTimeStamp(Number(id));
      });
    }, 60000); // 每分钟执行一次
  });

  onUnmounted(() => {
    clearInterval(timerId);
  });

  return {
    addTimeStamp,
    updateTimeStamp,
    getTimeStampMap,
  };
};

export default useUpdateTime;
