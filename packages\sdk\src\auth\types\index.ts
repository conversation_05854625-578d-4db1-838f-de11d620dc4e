// 服务端返回的菜单格式
export interface ServerMenuItem {
  parentMenuId: number;
  platformId: number;
  menuName: string;
  type: number;
  sort: number;
  uniqueCode: string;
  buttonNum: number;
  buttonType?: string;
  isDelete: number;
  status: number;
  createUser: string;
  updateUser: string;
  isCheck: any;
  id: number;
  updateAt: string;
  createAt: string;
  children: ServerMenuItem[];
  buttonTypeCodes: any[];
  checkButtonTypeCodes: any[];
}
