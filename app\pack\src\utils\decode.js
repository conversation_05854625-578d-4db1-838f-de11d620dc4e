const fs = require('fs');
const path = require('path');

const processFiles = (inputDir, rootDir, fileHandler, processList) => {
  const files = fs.readdirSync(inputDir);
  for (const file of files) {
    const fullPath = path.join(inputDir, file);
    const relativePath = path.relative(inputDir, fullPath); // 计算相对路径
    const outputFilePath = path.join(rootDir, relativePath);

    if (fs.statSync(fullPath).isDirectory()) {
      // 递归处理目录
      fs.mkdirSync(outputFilePath, { recursive: true });
      processFiles(fullPath, outputFilePath, fileHandler, processList);
    } else {
      if (file.endsWith('.js')) {
        processList.push({ fullPath, outputFilePath });
      } else {
        const content = fs.readFileSync(fullPath, 'utf-8');
        fs.writeFileSync(outputFilePath, content, 'utf-8');
      }
    }
  }
};

module.exports = {
  processFiles,
};
