<template>
  <div class="wrapper" style="{{ wrapperStyle }}">
    <list class="content">
      <list-item type="1">
        <image
          class="battery-image"
          src="../../../assets/images/dc.png"
        ></image>
      </list-item>
      <list-item type="2">
        <text class="expected-text">预计可用 {{ residueTime }}</text>
      </list-item>
      <list-item type="3">
        <div class="one-btn" @click="setBrightness">
          <!-- <lottie source="../../lottie/data_but.json" autoplay="{{true}}"></lottie> -->
          <image
            src="../../../assets/images/but.png"
            style="width: 540px; height: 120px"
          ></image>
        </div>
      </list-item>
    </list>
    <div class="animation-wrapper" if="{{ showCircle }}">
      <stack class="img-wrapper">
        <image
          class="circle"
          src="https://img.sytangshiwl.top/qujieneng/images/sd-animate.png"
        ></image>
        <div class="loading-content">
          <!-- <lottie source="../../lottie/sd.json" autoplay="{{true}}"></lottie> -->
          <text>{{ count }}s正在进入省电模式...</text>
        </div>
      </stack>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      componentHeight: {
        type: String,
        default: '100%'
      },
      borderRadius: {
        type: String,
        default: '8px'
      },
      backgroundColor: {
        type: String,
        default: '#222'
      },
      backgroundImg: {
        type: String,
        default: ''
      }
    },
    data: {
      batteryInfo: '',
      count: 3,
      showCircle: false
    },

    computed: {
      residueTime() {
        return '1小时1分钟'
      },
      wrapperStyle() {
        return {
          height: this.componentHeight,
          'border-radius': this.borderRadius,
          'background-color': this.backgroundColor,
          'background-image': this.backgroundImg
        }
      }
    },
    async setBrightness() {
      this.showCircle = true
      const delay = () => {
        this.count--
        if (this.count === 0) {
          this.showCircle = false
          this.count = 3
          // let setBrightness = this.nowBrightness < 50 ? 200 : 10
          // DEVICE_UTILS.setBrightness(setBrightness).then(res => {
          //   this.nowBrightness = setBrightness;
          // })

          if (timer) {
            clearTimeout(timer)
          }
          return
        }
        timer = setTimeout(delay, 1000)
      }
      let timer = setTimeout(delay, 1000)
    }
  }
</script>

<style lang="less">
  .wrapper {
    width: 100%;

    .content {
      flex-direction: column;
      flex: 1;

      list-item {
        justify-content: center;
      }
    }

    .animation-wrapper {
      position: fixed;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.9);

      .img-wrapper {
        width: 682px;
        height: 682px;
        align-items: center;
        justify-content: center;

        > image {
          width: 682px;
          height: 682px;
        }
      }

      .circle {
        animation-name: circle360;
        animation-duration: 3s;
        animation-timing-function: linear;
        animation-iteration-count: 1;
      }

      @keyframes circle360 {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }

      .loading-content {
        flex-direction: column;
        align-items: center;

        > lottie {
          width: 130px;
          height: 130px;
        }

        > text {
          font-size: 36px;
          font-weight: bold;
          color: #ffffff;
          height: 36px;
          margin-top: 22px;
        }
      }
    }
  }
</style>