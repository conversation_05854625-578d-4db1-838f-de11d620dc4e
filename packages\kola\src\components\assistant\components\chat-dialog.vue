<template>
  <Teleport to="body">
    <div v-show="dialogFormVisible" ref="chartWrapper" class="chart-wrapper" id="chat-dialog">
      <div class="content-wrapper">
        <Chat
          ref="chatRef"
          v-show="activeTab === 'chat'"
          @close="handleClose"
          @history="handleHistory"
          @advice="handleAdvice"
          @question="handleQuestion"
        />
        <NormalQuestion
          v-show="activeTab === 'question'"
          @close="handleClose"
          @back="handleBack"
          @choose="handleChoose"
        />
        <History
          v-if="activeTab === 'history'"
          v-show="activeTab === 'history'"
          @close="handleClose"
          @back="handleBack"
        />
        <Advice v-show="activeTab === 'advice'" @close="handleClose" @back="handleBack" />
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
  import { ref, watch, provide, onMounted, onUnmounted, nextTick, inject } from 'vue';
  import Chat from './chat/chat.vue';
  import NormalQuestion from './questions/normal-question.vue';
  import History from './history/history.vue';
  import Advice from './advice/index.vue';
  import websocketApi from '../apis';
  import { generateRequestId } from '../utils/uuid';
  import useDefaultQuestions from '../hooks/use-default-questions';
  import useChatRecord from '../hooks/use-chat-record';
  import getCookie from '../utils/token';

  const activeTab = ref('chat');
  const dialogFormVisible = ref(false);
  const chatRef = ref<HTMLElement | null>(null);
  const chartWrapper = ref<HTMLElement | null>(null);
  const platformId = inject<any>('platformId');
  provide('chartWrapper', chartWrapper);
  const { getDefaultQuestions } = useDefaultQuestions();
  const { queryChatRecord, resetRecord } = useChatRecord();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:visible']);

  const handleClose = () => {
    dialogFormVisible.value = false;
  };

  const handleHistory = () => {
    resetRecord();
    queryChatRecord({ keyword: '' }, () =>
      nextTick(() => {
        activeTab.value = 'history';
      })
    );
  };

  const handleAdvice = () => {
    activeTab.value = 'advice';
  };

  const handleQuestion = () => {
    activeTab.value = 'question';
  };

  const handleBack = () => {
    activeTab.value = 'chat';
  };
  const handleChoose = (item: any) => {
    // @ts-ignore
    chatRef.value.handleChoose(item);
    activeTab.value = 'chat';
  };
  provide('handleQuestion', handleQuestion);
  watch(
    () => props.visible,
    (value) => {
      dialogFormVisible.value = value;
    }
  );

  watch(dialogFormVisible, (value) => {
    emit('update:visible', value);
  });
  onMounted(() => {
    const requestId = generateRequestId();
    // const platformId = platformId.value;
    const token = getCookie('token');
    websocketApi.connect(requestId, platformId.value, token).then(() => {
      getDefaultQuestions();
      resetRecord();
    });
  });
  onUnmounted(() => {
    websocketApi.close();
  });
</script>

<style scoped>
  .chart-wrapper {
    position: fixed;
    bottom: 5%;
    right: 60px;
    z-index: 1001;
    background-color: #fff;
    box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
    width: 622px;
    height: 90%;

    /* min-height: 585px; */
    max-height: 743px;
    border-radius: 8px;
  }

  .content-wrapper {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    position: relative;
    background: linear-gradient(
      286deg,
      rgb(22 93 255 / 20%) 0%,
      rgb(54 138 234 / 20%) 35%,
      rgb(182 32 224 / 20%) 73%,
      rgb(247 181 0 / 20%) 100%
    );
  }

  .mask {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(
      180deg,
      rgb(255 255 255 / 0%) 0%,
      rgb(255 255 255 / 100%) 60%,
      rgb(255 255 255 / 100%) 100%
    );
  }
</style>
