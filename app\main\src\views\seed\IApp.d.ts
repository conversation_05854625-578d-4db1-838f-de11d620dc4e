// 标签
export interface ITab {
    name: string;                       // 标签名称
    icon: string;                       // 标签图标
    isActive: boolean;                  // 是否激活
    pages: Array<IPage>                 // 本 tab 下的页面
}

// 页面
export interface IPage {
    name: string;                       // 页面名称 
    components: Array<IComponent>;      // 页面组件
}

// 组件属性
export interface IComponentProps {
    name: string;                       // 组件属性名
    value: string;                      // 组件属性值
}

// 组件
export interface IComponent {
    name: string;                       // 组件名称
    icon: string;                       // 组件图标
    version: string;                    // 组件版本
    description: string;                // 组件描述
    props: Array<IComponentProps>
}

// brand-config 下的配置
export interface IBrand {
    // 隐私协议地址
    IProtocolAddress: {
        IUserAgreement: string;
        IPrivacyPolicy: string;
    }
}

// 对应 manifest.json 下的配置
export interface  IManifest{}

// 应用
export interface IApp {
    package: string;                 // 包名
    name:   string;                  // 应用名称
    versionName: string;             // 版本名称
    versionCode: string;             // 版本code
    icon: string;                    // 应用图标
    version: string;                 // 应用版本
    code: string;                    // 应用code
    IBrand: IBrand;                  // 对应 brand-config.js
    IManifest: IManifest;
    tab: Array<ITab>;                // 应用标签页
}