{"package": "com.template.home", "name": "模板应用", "versionName": "1.0.0", "versionCode": 1000000, "minPlatformVersion": 1100, "icon": "/assets/images/logo.png", "huaweiSplashAdSlotId": "b2pu1nu1ja", "features": [{"name": "system.keyguard"}, {"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.shortcut"}, {"name": "system.webview"}, {"name": "system.share"}, {"name": "service.share"}, {"name": "system.contact"}, {"name": "system.geolocation"}, {"name": "system.fetch"}, {"name": "service.account"}, {"name": "system.storage"}, {"name": "system.device"}, {"name": "system.request"}, {"name": "system.media"}, {"name": "service.alipay"}, {"name": "system.network"}, {"name": "system.file"}, {"name": "system.clipboard"}, {"name": "system.package"}, {"name": "system.app"}, {"name": "service.ad"}, {"name": "system.animate"}, {"name": "system.sensor"}, {"name": "system.vibrator"}, {"name": "system.calendar"}, {"name": "system.audio"}, {"name": "service.push"}, {"name": "system.notification"}, {"name": "system.barcode"}, {"name": "system.texttoaudio"}, {"name": "system.battery"}, {"name": "system.requesttask"}], "permissions": [{"origin": "*"}], "template/official": "ecommerce", "config": {"logLevel": "debug", "network": {"readTimeout": 8000}, "designWidth": 750}, "router": {"entry": "pages/Main", "pages": {}}, "display": {"themeMode": 0, "fitCutout": "portrait|landscape", "titleBar": false, "fullScreen": true, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "windowSoftInputMode": "adjustPan"}}