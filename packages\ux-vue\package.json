{"name": "ux-vue", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "bin": {"tt": "bin/index.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@babel/generator": "^7.27.5", "@babel/parser": "^7.27.5", "@babel/traverse": "^7.27.4", "chalk": "^5.4.1", "cheerio": "^1.1.0", "commander": "^14.0.0", "escodegen": "^2.1.0", "esprima": "^4.0.1", "estraverse": "^5.3.0", "inquirer": "8", "ora": "5", "parse5": "^7.3.0"}}