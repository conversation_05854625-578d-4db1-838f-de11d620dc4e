<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="fieldLabel"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
  >
    <div class="wildcard">
      <a-textarea
        ref="textareaRef"
        v-model="value"
        show-word-limit
        :disabled="field.disabled"
        :readonly="field.readonly"
        :placeholder="field.placeholder || `请输入${fieldLabel}`"
        :max-length="field.maxLength"
        :style="field?.style"
      >
      </a-textarea>
      <div>
        通配符：
        <a-button
          v-for="item in field.source.data"
          :key="item.value"
          type="text"
          @click="handleInsertWildCard(item?.value)"
          size="mini"
        >
          <template #icon>
            <icon-plus />
          </template>
          {{ item.label }}
        </a-button>
      </div>
    </div>
    <template #extra v-if="field.extra">
      <component :is="field.extra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, ref } from 'vue';
  import { isFunction } from 'lodash';
  import { WildcardField } from '../../../types/form';

  const props = defineProps<{
    field: WildcardField;
    path: string;
    extra?: any;
  }>();

  const formData = inject('formData');
  const fieldLabel = computed(() => {
    return isFunction(props.field.label)
      ? props.field.label(props.field, formData?.value as any, props.path)
      : props.field.label;
  });
  const value = defineModel<string>({
    default: '',
    set(val) {
      props.field.onChange?.(val, formData);
      if (props.field.setter) {
        return props.field.setter(val);
      }
      return val;
    },
  });
  const textareaRef = ref();

  const handleInsertWildCard = (wildcard: string) => {
    const textareaEl = textareaRef.value.textareaRef;
    const startPos = textareaEl.selectionStart;
    const endPos = textareaEl.selectionEnd;
    const text = textareaEl.value;
    const newText = text.substring(0, startPos) + wildcard + text.substring(endPos);
    if (props.field.maxLength) {
      textareaEl.value = newText.substring(0, props.field.maxLength);
      value.value = textareaEl.value;
      return;
    }
    textareaEl.value = newText;
    value.value = newText;
  };
</script>
