import ObsClient from 'esdk-obs-browserjs/src/obs';
import { v4 as uuIdV4 } from 'uuid';
import { ENV_CONFIG } from '../constants';

let tokenCatch: any = null;
// @ts-ignore
const env = process.env.VITE_MODE || 'testing';
function loadParams() {
  if (tokenCatch) {
    return Promise.resolve(tokenCatch);
  }
  const params = {
    access_key_id: ENV_CONFIG[env].UPLOAD_ASSESS_KEY,
    secret_access_key: ENV_CONFIG[env].UPLOAD_SECRET_KEY,
    server: ENV_CONFIG[env].UPLOAD_SERVER,
  };
  tokenCatch = params;
  return params;
}

let obsClient: any = null;

function obsUpload({ ...option }) {
  const bucket = ENV_CONFIG[env].UPLOAD_BUCKET;
  if (obsClient === null) {
    tokenCatch = loadParams();
    obsClient = new ObsClient(tokenCatch);
  }
  return clientPut(bucket, option);
}

function clientPut(bucket, option) {
  const { file, filename, ProgressCallback, cancel, path = '' } = option;
  let initCancel = false;

  // @ts-ignore
  return new Promise((resolve, reject) => {
    const fileName = filename || `${uuIdV4()}_${file.name}`;

    const requestOption = {
      Bucket: bucket, // 桶名
      Key: `${path}${fileName}`, // 文件名    此处用的是uuid
      SourceFile: file, // 流文件
      // PartSize: file.size,
    } as Record<string, any>;

    if (ProgressCallback) {
      requestOption.ProgressCallback = (...prams) => {
        if (cancel && requestOption?.cancelHook && !initCancel) {
          initCancel = true;
          cancel(requestOption.cancelHook);
        }
        if (ProgressCallback) {
          ProgressCallback(...prams);
        }
      };
    }
    obsClient.putObject(requestOption, (err) => {
      if (!err) {
        // url  https://Bucket.server/file.name    server不要https
        const url = `https://${bucket}.obs.cn-north-4.myhuaweicloud.com/${path}${fileName}`;
        resolve({
          url,
          file,
          obsFileName: fileName,
        });
      } else {
        console.log('err', err);
        reject(err);
      }
    });
  });
}

export default obsUpload;
