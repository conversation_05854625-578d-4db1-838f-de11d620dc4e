import device from '@system.device'


class Device {
    constructor() {
        this.brand = 'other',
        this.model = 'unknown',
        this.os = '',
        this.osVersionName = ''
        this.platformVersionCode = 0,
        this.packageName = '',
        this.statusBarHeight = 0,
        this.manufacturer = '',
        this.marketingName = ''
    }
    async init() {
        const deviceInfo = await getDeviceInfo()
        console.log('deviceInfo',deviceInfo)
        const {
            model,
            osVersionName,
            platformVersionCode,
            vendorOsName,
            osType,
            statusBarHeight,
            manufacturer,
            marketingName,
            brand
        } = deviceInfo;
        this.brand = brand // 获取品牌
        this.model = model || 'unknown' // 获取设备型号
        this.osVersionName = osVersionName || '9'
        this.platformVersionCode = platformVersionCode || 0
        this.os = vendorOsName === 'harmonyOS' ? 'harmony' : osType.toLowerCase()
        this.packageName = device.host.package
        this.statusBarHeight = statusBarHeight
        this.manufacturer = manufacturer || ''
        this.marketingName = marketingName || ''
    }
}

function getDeviceInfo() {
    return new Promise((resolve, reject) => {
        device.getInfo({
            success: deviceInfo => {
                resolve(deviceInfo)
            },
            fail: error => {
                reject(error)
            }
        })
    })
}



export default Device;