import { CSSProperties, VNode } from 'vue';

export type DDetail = {
  record?: any;
  data: DDetailData;
};

export type DDetailData = DDetailItem[] | ((data: any) => DDetailItem[]);

export type DDetailItem = {
  title: string;
  data: { label: string; value: string | VNode }[];
  size?: 'mini' | 'small' | 'medium' | 'large';
  labelStyle?: CSSProperties;
  valueStyle?: CSSProperties;
  column?: number;
};
