<template>
  <div class="c-weektime">
    <div class="c-weektime-disabled" v-if="mergedDisabled"></div>
    <div class="c-schedue"></div>
    <div :class="{ 'c-schedue': true, 'c-schedue-notransi': tableConfig.mode }" :style="styleValue"></div>
    <table :class="{ 'c-min-table': !half }" class="c-weektime-table">
      <thead class="c-weektime-head">
        <tr>
          <th rowspan="8" class="week-td">星期/时间</th>
          <th :colspan="half ? 48 : 12">00:00 - 12:00</th>
          <th :colspan="half ? 48 : 12">12:00 - 24:00</th>
        </tr>
        <tr>
          <td v-for="t in theadArr" :key="t" :colspan="half ? 4 : 1">{{ t }} </td>
        </tr>
      </thead>
      <tbody class="c-weektime-body">
        <tr v-for="t in selectedData" :key="t.row">
          <td>{{ t.value }}</td>
          <td
            v-for="n in t.child"
            :key="`${n.row}-${n.col}`"
            :data-week="n.row"
            :data-time="n.col"
            :class="selectClasses(n)"
            @mouseenter="cellEnter(n)"
            @mousedown="cellDown(n)"
            @mouseup="cellUp(n)"
            class="weektime-atom-item"
            :colspan="half ? 2 : 1"
          ></td>
        </tr>
        <tr>
          <td :colspan="half ? 96 : 49" class="c-weektime-preview">
            <div class="g-clearfix c-weektime-con">
              <span class="g-pull-left">{{ selectState ? '已选择时间段' : '可拖动鼠标选择时间段' }}</span>
              <a @click.prevent="handleClear" class="g-pull-right">清空选择</a>
            </div>
            <div v-if="selectState" class="c-weektime-time">
              <div v-for="t in selectValue" :key="t.id">
                <p v-if="t.value">
                  <span class="g-tip-text">{{ t.week }}：</span>
                  <span>{{ t.value }}</span>
                </p>
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
  import { computed, reactive, onMounted, ref, nextTick } from 'vue';
  import { cloneDeep } from 'lodash';
  import { useFormItem } from '@arco-design/web-vue';
  import { splicing, transformData, WeekTimeNumber } from './weektimeData';

  const { eventHandlers, mergedDisabled } = useFormItem();

  const createArr = (len) => {
    return Array.from(Array(len)).map((ret, id) => id);
  };

  const props = defineProps({
    params: {
      type: Object,
      default() {
        return { number: WeekTimeNumber.KUAI_SHOU };
      },
    },
  });

  const half = props.params.number === WeekTimeNumber.TOU_TIAO;

  const weekTimeValue = defineModel<{ value: string; week: string; id: number }[]>([] as any);

  const selectedData = computed(() => {
    return transformData(weekTimeValue.value, props.params.number);
  });

  const theadArr = ref<Array<number>>([]);

  const tableConfig = reactive({
    width: 0,
    height: 0,
    left: 0,
    top: 0,
    mode: 0,
    row: 0,
    col: 0,
  });

  const check = ref(false);

  const styleValue = computed(() => {
    return {
      width: `${tableConfig.width}px`,
      height: `${tableConfig.height}px`,
      left: `${tableConfig.left}px`,
      top: `${tableConfig.top}px`,
    };
  });

  const selectValue = computed(() => weekTimeValue.value);

  const selectState = computed(() => {
    return weekTimeValue.value?.some((ret) => ret.value);
  });

  const selectClasses = computed(() => (n) => n.check ? 'ui-selected' : '');

  onMounted(() => {
    theadArr.value = createArr(24);
  });

  const handleClear = () => {
    selectedData.value.forEach((item) => {
      item.child.forEach((t) => {
        t.check = false;
      });
    });
    weekTimeValue.value?.forEach((item) => {
      item.value = '';
    });
  };

  const cellEnter = (item) => {
    const ele = document.querySelector(`td[data-week='${item.row}'][data-time='${item.col}']`) as HTMLElement;

    const { row, col, mode } = tableConfig;

    if (ele && !mode) {
      tableConfig.top = ele.offsetTop;
      tableConfig.left = ele.offsetLeft;
    } else if (item.col <= col && item.row <= row) {
      tableConfig.width = (col - item.col + 1) * ele.offsetWidth;
      tableConfig.height = (row - item.row + 1) * ele.offsetHeight;
      tableConfig.top = ele.offsetTop;
      tableConfig.left = ele.offsetLeft;
    } else if (item.col >= col && item.row >= row) {
      tableConfig.width = (item.col - col + 1) * ele.offsetWidth;
      tableConfig.height = (item.row - row + 1) * ele.offsetHeight;

      if (item.col > col && item.row === row) {
        tableConfig.top = ele.offsetTop;
      }
      if (item.col === col && item.row > row) {
        tableConfig.left = ele.offsetLeft;
      }
    } else if (item.col > col && item.row < row) {
      tableConfig.width = (item.col - col + 1) * ele.offsetWidth;
      tableConfig.height = (row - row + 1) * ele.offsetHeight;
      tableConfig.top = ele.offsetTop;
    } else if (item.col < col && item.row > row) {
      tableConfig.width = (col - item.col + 1) * ele.offsetWidth;
      tableConfig.height = (item.row - row + 1) * ele.offsetHeight;
      tableConfig.top = ele.offsetTop;
    }
  };

  const cellDown = (item) => {
    const ele = document.querySelector(`td[data-week='${item.row}'][data-time='${item.col}']`) as HTMLElement;

    check.value = Boolean(item.check);
    tableConfig.mode = 1;
    if (ele) {
      tableConfig.width = ele.offsetWidth;
      tableConfig.height = ele.offsetHeight;
    }

    tableConfig.row = item.row;
    tableConfig.col = item.col;
  };

  const cellUp = (item) => {
    const { row, col } = tableConfig;
    if (item.col <= col && item.row <= row) {
      selectWeek([item.row, row], [item.col, col], !check.value);
    } else if (item.col >= col && item.row >= row) {
      selectWeek([row, item.row], [col, item.col], !check.value);
    } else if (item.col > col && item.row < row) {
      selectWeek([item.row, row], [col, item.col], !check.value);
    } else if (item.col < col && item.row > row) {
      selectWeek([row, item.row], [item.col, col], !check.value);
    }
    tableConfig.width = 0;
    tableConfig.height = 0;
    tableConfig.mode = 0;
  };
  const selectWeek = (row, col, checked) => {
    const [minRow, maxRow] = row;
    const [minCol, maxCol] = col;
    selectedData.value.forEach((item) => {
      item.child.forEach((t) => {
        if (t.row >= minRow && t.row <= maxRow && t.col >= minCol && t.col <= maxCol) {
          t.check = checked;
        }
      });
    });

    selectedData.value.forEach((item, index) => {
      const timeResult = cloneDeep(splicing(item.child));
      weekTimeValue.value[index].value = timeResult ?? '';
    });

    // 校验
    nextTick(() => {
      eventHandlers.value.onChange?.();
    });
  };
</script>

<style scoped lang="less">
  .c-weektime {
    min-width: 640px;
    position: relative;
    display: inline-block;

    .c-weektime-disabled {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .c-schedue {
    background: rgb(var(--arcoblue-3));
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0.6;
    pointer-events: none;
  }

  .c-schedue-notransi {
    transition: width 0.12s ease, height 0.12s ease, top 0.12s ease, left 0.12s ease;
  }

  .c-weektime-table {
    border-collapse: collapse;

    th {
      vertical-align: inherit;
      font-weight: bold;
    }

    tr {
      height: 30px;
    }

    tr,
    td,
    th {
      user-select: none;
      border: 1px solid var(--color-neutral-3);
      text-align: center;
      min-width: 12px;
      line-height: 1.8em;
      transition: background 0.2s ease;
    }

    .c-weektime-head {
      font-size: 12px;

      .week-td {
        width: 70px;
      }
    }

    .c-weektime-body {
      font-size: 12px;

      td {
        &.weektime-atom-item {
          user-select: unset;
          background-color: var(--color-neutral-2);
        }

        &.ui-selected {
          background-color: rgb(var(--arcoblue-4));
        }
      }
    }

    .c-weektime-preview {
      line-height: 2.4em;
      padding: 0 10px;
      font-size: 14px;

      .c-weektime-con {
        line-height: 46px;
        user-select: none;
      }

      .c-weektime-time {
        text-align: left;
        line-height: 2.4em;

        p {
          max-width: 625px;
          line-height: 1.4em;
          word-break: break-all;
          margin-bottom: 8px;
        }
      }
    }
  }

  .c-min-table {
    tr,
    td,
    th {
      min-width: 24px;
    }
  }

  .g-clearfix {
    &::after,
    &::before {
      clear: both;
      content: ' ';
      display: table;
    }
  }

  .g-pull-left {
    float: left;
  }

  .g-pull-right {
    float: right;
    cursor: pointer;
    color: rgb(var(--arcoblue-6));
  }

  .g-tip-text {
    color: var(--color-neutral-6);
  }
</style>
