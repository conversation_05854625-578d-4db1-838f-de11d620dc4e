import { mergeConfig } from 'vite';
import baseConfig from './vite.config.base';
import configCompressPlugin from './plugin/compress';
import configVisualizerPlugin from './plugin/visualizer';
import configArcoResolverPlugin from './plugin/arcoResolver';
import configImageminPlugin from './plugin/imagemin';
import path, { resolve } from 'path';
import monacoEditorPlugin from 'vite-plugin-monaco-editor';

export default mergeConfig(
  {
    mode: 'production',
    plugins: [
      configCompressPlugin('gzip'),
      configVisualizerPlugin(),
      configArcoResolverPlugin(),
      configImageminPlugin(),
      monacoEditorPlugin({
        customDistPath: () =>
          resolve(__dirname, '../../pack/client/monaco-editor/'),
        publicPath: 'monaco-editor',
      }),
    ],
    build: {
      outDir: path.resolve(__dirname, '../../pack/client'),
      rollupOptions: {
        output: {
          manualChunks: {
            arco: ['@arco-design/web-vue'],
            chart: ['echarts', 'vue-echarts'],
            vue: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'vue-i18n'],
          },
        },
      },
      chunkSizeWarningLimit: 2000,
    },
    base: '/',
  },
  baseConfig
);
