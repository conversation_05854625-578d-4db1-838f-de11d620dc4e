const { Worker } = require('worker_threads');
const path = require('path');
const { TASK_STATUS } = require('../constant');

class Pack {
  // eslint-disable-next-line no-useless-constructor
  constructor() {}

  pack(task) {
    // 执行打包
    return new Promise((resolve, reject) => {
      const workerPath = path.join(__dirname, './worker.js');
      console.log('workerPath', workerPath);
      const worker = new Worker(workerPath);

      worker.postMessage(task);
      worker.on('message', (result) => {
        // eslint-disable-next-line default-case
        switch (result.type) {
          case 'success':
            this.taskComplete(task, result);
            resolve(task);
            break;
          case 'error':
            this.taskError(task, result.message, result.errorCode);
            reject(result.message);
            break;
        }
      });
    });
  }

  taskComplete(task, result) {
    task.status = TASK_STATUS.SUCCESS;
    result.devPkg && (task.devPkg = result.devPkg);
    result.releaseLogPkg && (task.releaseLogPkg = result.releaseLogPkg);
    result.releasePkg && (task.releasePkg = result.releasePkg);
    task.timeCost = result.timeCost;
    task.version = result.version;
    task.sdkCommit = result.sdkCommit;
    task.appManifestRes = result.appManifestRes
    task.appDefinePluginRes = result.appDefinePluginRes
  }

  taskError(task, message, errorCode) {
    task.status = TASK_STATUS.FAILED;
    task.failedResult = message;
    task.errorCode = errorCode;
    console.log('taskError', task);
  }
}

module.exports = new Pack(); 
