import { extractDomainFromUrl, getCookie } from './utils';
import { isDev, isProd } from './env';

export function syncToken() {
  if (isDev) return;

  const currentDomain = extractDomainFromUrl(window.location.origin) ?? '';
  const domains = [
    {
      domain: '.ghfkj.cn',
      testPassport: 'https://test-passport.ghfkj.cn',
      prodPassport: 'https://passport.ghfkj.cn',
    },
    {
      domain: '.cmywkj.cn',
      testPassport: 'https://test-passport.cmywkj.cn',
      prodPassport: 'https://passport.cmywkj.cn',
    },
    {
      domain: '.chengyouyun.com',
      testPassport: 'https://test-workspace.chengyouyun.com',
      prodPassport: 'https://workspace.chengyouyun.com',
    },
  ];

  domains
    .filter((domain) => domain.domain !== currentDomain)
    .forEach(({ domain, testPassport, prodPassport }) => {
      const baseUrl = isProd ? prodPassport : testPassport;
      const img = document.createElement('img');
      img.src = `${baseUrl}/apis/common/user/sync/token/v1?domain=${domain}&token=${getCookie('token')}`;
      img.style.display = 'none';
      document.body.appendChild(img);
    });
}

export default null;
