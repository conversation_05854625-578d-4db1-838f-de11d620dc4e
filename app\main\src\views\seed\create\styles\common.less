.component-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  cursor: move;
  transition: all 0.2s;
  background: var(--color-bg-2);
  user-select: none;
}
.component-item:hover {
  border-color: rgb(var(--primary-6));
  background: var(--color-fill-2);
}
.component-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: var(--color-text-2);
}
.component-name {
  font-size: 14px;
  color: var(--color-text-1);
} 