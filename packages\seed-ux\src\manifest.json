{"package": "com.component.library", "name": "SEED组件库", "versionName": "1.0.0", "versionCode": 1, "minPlatformVersion": 1100, "icon": "/assets/images/logo.png", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.bluetooth"}, {"name": "system.volume"}, {"name": "system.brightness"}, {"name": "system.device"}, {"name": "system.battery"}], "permissions": [{"origin": "*"}], "template/official": "component-template", "config": {"logLevel": "debug", "designWidth": 1080}, "router": {"entry": "components/battery/OperationBoard", "pages": {"components/battery/OperationBoard": {"component": "index"}, "components/battery/PhoneInfo": {"component": "index"}, "components/battery/CommonHeader": {"component": "index"}, "components/battery/SavingSkill": {"component": "index"}, "components/battery/BatteryCare": {"component": "index"}, "components/battery/JNOperationBoard": {"component": "index"}, "components/battery/JNPowerManager": {"component": "index"}, "pages/Demo": {"component": "index"}, "pages/DemoDetail": {"component": "index"}}, "widgets": {"widgets/CardDemo": {"name": "卡片名称4x2", "description": "快应用卡片展示", "component": "index", "path": "/widgets/CardDemo", "minPlatformVersion": 1032, "params": {"size": "4x2"}, "targetManufacturers": ["hua<PERSON>", "honer", "vivo", "OPPO", "xia<PERSON>"], "features": [], "type": "js"}}}, "display": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "menu": true, "pages": {"pages/Demo": {"titleBarText": "组件库", "menu": false}, "pages/DemoDetail": {"titleBarText": "详情页"}}}, "deviceTypeList": ["phone"]}