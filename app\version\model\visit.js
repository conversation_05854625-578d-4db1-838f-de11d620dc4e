const mongoose = require('mongoose');

const pvUvSchema = new mongoose.Schema(
  {
    // 页面路径或应用路径
    path: {
      type: String,
      required: true,
      trim: true,
    },
    // 用户ID（可选，用于识别独立访客）
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User', // 假设有一个User模型
      default: null,
    },
    // IP地址（用于识别独立访客）
    ip: {
      type: String,
      required: true,
      trim: true,
    },
    // 用户代理（User-Agent，用于识别设备和浏览器）
    userAgent: {
      type: String,
      required: true,
      trim: true,
    },
    // 访问时间
    timestamp: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
  }
);

module.exports = mongoose.model('Visit', pvUvSchema);
