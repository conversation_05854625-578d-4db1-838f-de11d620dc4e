{"root": true, "env": {"node": true, "commonjs": true}, "extends": ["eslint:recommended", "plugin:ux/essential"], "parserOptions": {"parser": "babel-es<PERSON>", "sourceType": "module", "ecmaFeatures": {"ecmaVersion": true, "jsx": true}}, "globals": {"loadData": false, "saveData": false, "history": false, "console": false, "setTimeout": false, "clearTimeout": false, "setInterval": false, "clearInterval": false}, "rules": {"indent": ["warn", 2], "no-console": ["warn", {"allow": ["info", "warn", "error"]}], "no-unused-vars": ["warn", {"varsIgnorePattern": "prompt"}], "quotes": ["warn", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "linebreak-style": ["warn", "unix"], "semi": ["warn", "never"]}}