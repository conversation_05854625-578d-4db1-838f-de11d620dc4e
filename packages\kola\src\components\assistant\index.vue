<template>
  <div
    ref="icon"
    class="floating-icon"
    :style="iconStyle"
    @mousedown="startDrag"
    @click="handleClick"
    @mouseover="toggleImage(true)"
    @mouseleave="toggleImage(false)"
  >
    <div class="icon-wrapper">
      <img v-show="imgBox === 'img-box' && !showChartDialog" :src="weixiao" class="img-box" />
      <img v-show="imgBox === 'img-box-gif' || showChartDialog" :src="talking" class="img-box-gif" />
    </div>
    <div class="tooltip" v-if="showTooltip">
      <div class="tip-content">
        Hi, 你好！我是你的诚优小助手～<br />
        有什么问题和意见请找我哦！
      </div>
    </div>
    <ChatDialog v-if="shouldRenderChatDialog" v-model:visible="showChartDialog" />
  </div>
  <!-- <Welcome v-model:welVisible="showWelcome" @on-show="handleShow" /> -->
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, onBeforeUnmount, nextTick, watch, computed, provide } from 'vue';
  import { PLATFORM_ID_MAP } from './constants';
  import ChatDialog from './components/chat-dialog.vue';
  // import Welcome from './components/common/welcom.vue';
  import websocketApi from './apis';
  // @ts-ignore
  import weixiao from './images/weixiao.png';
  // @ts-ignore
  import talking from './images/talking.gif';

  // 测试vue3.5.12 解构props
  const { type } = defineProps<{
    type: 'passport' | 'adp' | 'tactic' | 'material';
  }>();

  // watch(
  //   () => type,
  //   (value) => {
  //     console.log('type', value);
  //   },
  //   { immediate: true }
  // );
  const platformId = computed(() => PLATFORM_ID_MAP[type]);
  provide('platformId', platformId);
  const showChartDialog = ref(false);
  const showWelcome = ref(false);
  const showTooltip = ref(false);
  const shouldRenderChatDialog = ref(false);
  const icon = ref<HTMLDivElement | null>(null);
  const isDragging = ref(false);
  const lastClosedTime = ref<number | null>(null);
  let timer: number | undefined;

  const iconStyle = reactive({
    position: 'absolute' as const,
    top: '90%',
    right: '0',
    cursor: 'pointer',
  });
  const imgBox = ref('img-box');
  // 切换图片的函数
  const toggleImage = (isHovered: boolean) => {
    showTooltip.value = isHovered && !isDragging.value && !showChartDialog.value;

    imgBox.value = isHovered ? 'img-box-gif' : 'img-box';
  };

  let startX = 0;
  let startY = 0;
  let initialRight = 0;
  let initialTop = 0;
  const dragStartTime = ref(0);
  const startDrag = (event: MouseEvent) => {
    if (!icon.value) return;
    dragStartTime.value = new Date().getTime();
    isDragging.value = true;
    showTooltip.value = false;
    startX = event.clientX;
    startY = event.clientY;

    const rect = icon.value.getBoundingClientRect();
    const windowWidth = window.innerWidth;
    initialRight = windowWidth - rect.right;
    initialTop = rect.top;

    document.addEventListener('mousemove', onDrag);
    document.addEventListener('mouseup', stopDrag);
  };
  const onDrag = (event: MouseEvent) => {
    if (!isDragging.value || !icon.value) return;

    const deltaX = event.clientX - startX;
    const deltaY = event.clientY - startY;

    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const iconRect = icon.value.getBoundingClientRect();

    let newRight = initialRight - deltaX;
    let newTop = initialTop + deltaY;

    if (newRight < 0) {
      newRight = 0;
    }
    if (newRight + iconRect.width > windowWidth) {
      newRight = windowWidth - iconRect.width;
    }
    if (newTop < 0) {
      newTop = 0;
    }
    if (newTop + iconRect.height > windowHeight) {
      newTop = windowHeight - iconRect.height;
    }

    iconStyle.right = `${newRight}px`;
    iconStyle.top = `${newTop}px`;
  };

  const stopDrag = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mouseup', stopDrag);
    iconStyle.right = '0';
  };

  const handleClick = () => {
    const clickTime = new Date().getTime();
    if (!isDragging.value && clickTime - dragStartTime.value < 200) {
      if (showChartDialog.value) {
        showChartDialog.value = false;
        lastClosedTime.value = new Date().getTime();
        return;
      }
      const currentTime = new Date().getTime();
      if (lastClosedTime.value && currentTime - lastClosedTime.value > 2 * 60 * 60 * 1000) {
        // 超过两小时，重新渲染
        shouldRenderChatDialog.value = false;
        nextTick(() => {
          shouldRenderChatDialog.value = true;
          nextTick(() => {
            showChartDialog.value = true;
          });
        });
      } else if (!shouldRenderChatDialog.value) {
        // 首次点击，渲染组件
        shouldRenderChatDialog.value = true;
        nextTick(() => {
          showChartDialog.value = true;
        });
      } else {
        // 两小时内，不重新渲染
        showChartDialog.value = true;
      }
    }
  };
  // 定义一个函数来检查时间差并执行操作
  const checkTimeDifference = () => {
    const currentTime = new Date().getTime();
    if (lastClosedTime.value && currentTime - lastClosedTime.value > 2 * 60 * 60 * 1000) {
      websocketApi.close();
      // 清除定时器
      clearTimeout(timer);
      timer = undefined;
    } else {
      // 如果还没到2小时，继续设置定时器
      timer = setTimeout(checkTimeDifference, 60 * 1000);
    }
  };
  const startTimer = () => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(checkTimeDifference, 60 * 1000);
  };

  const handleShow = () => {
    shouldRenderChatDialog.value = true;
    nextTick(() => {
      showChartDialog.value = true;
    });
  };

  watch(showChartDialog, (newVal) => {
    if (!newVal) {
      lastClosedTime.value = new Date().getTime();
      startTimer();
    }
  });
  onMounted(() => {
    document.addEventListener('mouseup', stopDrag);
    const showAiGuide = localStorage.getItem('showAiGuide');
    const isTime = new Date() < new Date('2024-11-01T00:00:00');
    if (isTime && showAiGuide !== 'true') {
      setTimeout(() => {
        showWelcome.value = true;
      }, 1000);
    }
  });

  onBeforeUnmount(() => {
    document.removeEventListener('mouseup', stopDrag);
  });
</script>

<style scoped>
  .floating-icon {
    width: 42px;
    height: 42px;
    background-color: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
    z-index: 1000;
    user-select: none;
  }

  .tooltip {
    position: absolute;
    height: auto;
    top: -45px;
    right: 55px;
    padding: 6px;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
    white-space: normal;
    z-index: 1000;
    text-align: left;
    width: 250px;
    box-sizing: border-box;
    background: linear-gradient(135deg, #b388ff, #8fd3ff);
    border-radius: 12px;
    border-bottom-right-radius: 0;
  }

  .tip-content {
    padding: 8px;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: rgb(255 255 255 / 20%);
    border-radius: 10px;
  }

  .icon-wrapper {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .img-box {
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .img-box-gif {
    width: 119%;
    height: 119%;
    pointer-events: none;
  }
</style>
