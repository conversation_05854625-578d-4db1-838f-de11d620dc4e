import { http } from '@/utils/http';

const SeedAppApi = {
  // 创建SeedApp
  create(data) {
    return http.post('/api/SeedApps', data);
  },

  // 获取SeedApp列表
  list(params) {
    return http.get('/api/SeedApps', { params });
  },

  // 获取单个SeedApp
  getById(id) {
    return http.get(`/api/SeedApps/${id}`);
  },

  // 更新SeedApp
  update(id, data) {
    return http.put(`/api/SeedApps/${id}`, data);
  },

  // 删除SeedApp
  delete(id) {
    return http.delete(`/api/SeedApps/${id}`);
  },

  // 构建
  runSeedApp(id) {
    return http.post(`/api/SeedApps/${id}/run`);
  },

};

export default SeedAppApi;
