<template>
  <div class="items">
    <a-button
      v-for="item in tagsList"
      :key="item.code"
      :class="getTagClass(item.code)"
      @click="handleClick(item.code)"
      type="text"
      >{{ item.label }}</a-button
    >
  </div>
</template>

<script setup lang="ts">
  const formData = defineModel<any>({});
  const tagsList = [
    // {
    //   label: '工作台',
    //   code: 1,
    // },
    {
      label: '优量投放',
      code: '2',
    },
    {
      label: '优策营销',
      code: '3',
    },
    {
      label: '优创素材',
      code: '4',
    },
  ];
  const handleClick = (type: string | number) => {
    if (formData.value.feedbackModule === type) {
      formData.value.feedbackModule = '';
      return;
    }
    formData.value.feedbackModule = type;
  };
  const getTagClass = (code: string | number) => {
    return formData.value.feedbackModule === code ? 'tag-operation-item active' : 'tag-operation-item';
  };
</script>

<style scoped>
  .items {
    width: 100%;

    .tag-operation-item {
      display: inline-block;
      margin-bottom: 10px;
      margin-right: 10px;
      color: rgb(75 75 75);
      border-color: rgb(233 233 233);
      border-radius: 4px;
      border: none;

      &.active {
        color: #fff;
        border-color: #165dff;
        background-color: #165dff;
      }
    }

    .tag-operation-item:hover {
      color: rgb(var(--link-4));
      border-color: rgb(var(--primary-3));
    }
  }
</style>
